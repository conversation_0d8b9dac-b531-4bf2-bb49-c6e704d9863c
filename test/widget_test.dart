// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:bd/main.dart';
import 'package:puppeteer/puppeteer.dart';

// 模拟的网络请求函数，使用 Future.delayed 来模拟网络延迟
Future<String> fetchApi(String url, int delayInSeconds) async {
  // 模拟网络请求耗时
  await Future.delayed(Duration(seconds: delayInSeconds));
  // 模拟返回的数据
  print('Completed: $url after $delayInSeconds seconds');
  return 'Response from $url';
}

Future<void> main() async {
  // 启动无头浏览器
  var browser = await puppeteer.launch(headless: true);

  // 打开一个新页面
  var page = await browser.newPage();
  // 访问目标网页
  await page.goto('https://example.com');

  print("object");
}