// ignore_for_file: no_leading_underscores_for_local_identifiers

import 'dart:convert';
import 'dart:io';
import 'package:collection/collection.dart';
import 'package:bd/exports.dart';
import 'package:bd/utils/notification_util.dart';
import 'package:bd/utils/store_util.dart';
import 'package:bd/utils/multi_window_manager.dart';
import 'package:bd/pages/settings/settings_window_app.dart';
import 'package:bd/pages/data_analysis/data_analysis_window_app.dart';
import 'package:bd/pages/jlzs/jlzs_window_app.dart';
import 'package:bd/pages/jlzs/analysis/jlzs_analysis_window_app.dart';
import 'package:bd/pages/jlzs/profile/jlzs_profile_window_app.dart';
import 'package:flutter/material.dart';
import 'package:desktop_multi_window/desktop_multi_window.dart';
import 'app.dart';
import 'package:window_manager/window_manager.dart';

void main(List<String> args) async {
  WidgetsFlutterBinding.ensureInitialized();

  // 按照官方示例的参数格式处理
  if (args.firstOrNull == 'multi_window') {
    // 子窗口启动
    final windowId = int.parse(args[1]);
    final argument = args[2].isEmpty
        ? const <String, dynamic>{}
        : jsonDecode(args[2]) as Map<String, dynamic>;

    print('启动子窗口: windowId=$windowId, args=$argument');

    // 初始化子窗口
    await _initializeSubWindow(argument);

    // 根据参数中的窗口类型启动对应的应用
    final windowTypeStr = argument['windowType'] as String?;
    if (windowTypeStr != null) {
      final windowType = WindowType.values.byName(windowTypeStr);
      print('启动窗口类型: $windowType');

      switch (windowType) {
        case WindowType.settings:
          runApp(SettingsWindowApp(
            windowController: WindowController.fromWindowId(windowId),
            args: argument,
          ));
          break;
        case WindowType.dataAnalysis:
          runApp(DataAnalysisWindowApp(
            windowController: WindowController.fromWindowId(windowId),
            args: argument,
          ));
          break;
        case WindowType.jlzs:
          runApp(JlzsWindowApp(
            windowController: WindowController.fromWindowId(windowId),
            args: argument,
          ));
          break;
        case WindowType.jlzsAnalysis:
          runApp(JlzsAnalysisWindowApp(
            windowController: WindowController.fromWindowId(windowId),
            args: argument,
          ));
          break;
        case WindowType.jlzsProfile:
          runApp(JlzsProfileWindowApp(
            windowController: WindowController.fromWindowId(windowId),
            args: argument,
          ));
          break;
      }
    } else {
      // 没有窗口类型，启动默认窗口
      print('没有窗口类型，启动默认设置窗口');
      runApp(SettingsWindowApp(
        windowController: WindowController.fromWindowId(windowId),
        args: argument,
      ));
    }
  } else {
    // 主窗口入口点 (保持原有逻辑)
    await _initializeMainWindow();
    runApp(const UtilsAPP());
  }
}

/// 初始化主窗口
Future<void> _initializeMainWindow() async {
  await windowManager.ensureInitialized();
  // Initialize directories in StoreUtil
  await StoreUtil.init();
  await NotificationUtil.init();
  // Initialize HiveUtil
  // await HiveUtil.init();
  if (Platform.isWindows || Platform.isMacOS || Platform.isLinux) {
    WindowManagerUtils.instance.initializeWindow();
  }
}

/// 初始化子窗口
Future<void> _initializeSubWindow(Map<String, dynamic> args) async {
  // 子窗口不需要window_manager，因为desktop_multi_window会处理
  // 只初始化必要的服务，不调用window_manager
  print('初始化子窗口，参数: $args');
  try {
    await StoreUtil.init();
    print('子窗口初始化完成');
  } catch (e) {
    print('子窗口初始化部分失败，但继续运行: $e');
  }
}
