// ignore_for_file: constant_identifier_names, depend_on_referenced_packages

import 'dart:io';
import 'package:bd/utils/hive_util.dart';
import 'package:path_provider/path_provider.dart';

class StoreUtil {
  StoreUtil._();

  static Directory? cacheDir;
  static Directory? docDir;
  static Directory? filesDir;
  // static late Directory imageDirectory;

  static const int KB = 1024;
  static const int MB = 1024 * KB;
  static const int GB = 1024 * MB;

  static Future<void> init() async {
    cacheDir = await getTemporaryDirectory();
    // imageDirectory = await Directory('${cacheDir.path}/image/').create(recursive: true);
    docDir = await getApplicationDocumentsDirectory();
    filesDir = await getApplicationSupportDirectory();
  }


  //路径处理
  static Future<String> checkAndCreateFolders(String a, String b) async {
    // 获取当前目录
    Directory currentDir = Directory.current;
    b = b.contains('/') ? b.replaceAll('/', '') : b;
    // 组合路径
    Directory aDir = Directory('${currentDir.path}/$a');
    Directory bDir = Directory('${aDir.path}/$b');

    // 判断A文件夹是否存在
    if (await aDir.exists()) {
    } else {
      // 创建A文件夹
      await aDir.create();
    }

    // 判断B文件夹是否存在
    if (await bDir.exists()) {
    } else {
      // 创建B文件夹
      await bDir.create();
    }
    // 返回B文件夹路径
    return bDir.path;
  }

// //  設置token
//   static setToken(final String token) {
//     HiveUtil.db?.put(HiveBoxKey.login, 'Bearer $token');
//   }
//
// // 刪除token
//   static removeToken() {
//     HiveUtil.db.delete(HiveBoxKey.login);
//   }
//
// // 獲取toekn
//   static String getToken() {
//     return HiveUtil.db.get(HiveBoxKey.login, defaultValue: '');
//   }

// 设置隐私协议
  static setPrivacyChecked() {
    // HiveUtil.db.put(HiveBoxKey.privacy, true);
    // HiveUtil.set(key: HiveBoxKey.privacy, value: true);
  }

// 获取隐私协议
//   static bool getPrivacyChecked() {
//     return HiveUtil.db.get(HiveBoxKey.privacy, defaultValue: false);
//   }

  // 清空緩存
  static Future<void> clearCache() async {
    await Future.wait(
      <Future<void>>[
        for (final FileSystemEntity f in cacheDir!.listSync()) f.delete(recursive: true),
      ],
    );
  }

  static Future<int> getCacheSize() async {
    final List<FileSystemEntity> listSync = cacheDir!.listSync(recursive: true);
    int size = 0;
    for (final FileSystemEntity file in listSync) {
      size += file.statSync().size;
    }
    return size;
  }

  static Future<String> getFormatCacheSize() async {
    final int size = await getCacheSize();
    if (size >= GB) {
      return '${(size / GB).toStringAsFixed(2)} GB';
    }
    if (size >= MB) {
      return '${(size / MB).toStringAsFixed(2)} MB';
    }
    if (size >= KB) {
      return '${(size / KB).toStringAsFixed(2)} KB';
    }
    return '$size B';
  }
}