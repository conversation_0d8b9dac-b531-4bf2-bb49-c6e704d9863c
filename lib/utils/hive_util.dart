// // ignore_for_file: unnecessary_question_mark, prefer_single_quotes
//
// import 'dart:io';
//
// import 'package:flutter/material.dart';
// import 'package:hive/hive.dart';
// import 'store_util.dart';
//
//
// class HiveUtil {
//   const HiveUtil._();
//
//   static  Box? db;
//
//   static Future<void> init() async {
//     // Log_Util.d("HiveUtil初始化");
//     Hive.init("${StoreUtil.filesDir!.path}${Platform.pathSeparator}hivedb");
//     db = await Hive.openBox(HiveBoxKey.db);
//   }
//
//   // 设置
//   static set({required String key, @required dynamic value}) {
//     db?.put(key, value);
//   }
//
//   // 获取
//   static dynamic get({required String key, dynamic defaultValue}) {
//     if (defaultValue != null) {
//       return db?.get(key, defaultValue: defaultValue);
//     } else {
//       return db?.get(
//         key,
//       );
//     }
//   }
//
//
//
//   // 删除
//   static del({
//     required String key,
//   }) {
//     db?.delete(key);
//   }
// }
//
// class HiveDBKey {
//   const HiveDBKey._();
//
//   static const String WYJ_RESUMED_TIME = 'wyj_resumed_time';
//   static const String WYJ_JG_REGiSTRATION_ID = 'wyj_jg_registration_id';
//   static const String WYJ_USER_ID = 'wyj_userid';
//   // 搜索编号历史记录
//   static const String WYY_SEARCH_NUMBER = 'wyy_search_number';
//   // 搜索门店历史记录
//   static const String WYY_SEARCH_STORE = 'wyy_search_store';
//   //  搜索楼盘历史记录
//   static const String WYY_SEARCH_REAL_ESTATE_DEVELOPMENT = 'wyy_search_real_estate_development';
//
// }
//
// class HiveBoxKey {
//   const HiveBoxKey._();
//
//   static const String db = 'KEY_DB';
//
//   static const String themes = 'KEY_THEMES';
//   // toekn
//   static const String login = 'KEY_TOKEN';
//   // 隐私协议
//   static const String privacy = 'KEY_PRIVACY';
// }
