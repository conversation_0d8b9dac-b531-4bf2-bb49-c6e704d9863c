import 'dart:convert';
import 'package:desktop_multi_window/desktop_multi_window.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// 窗口类型定义
enum WindowType {
  settings,     // 设置窗口
  dataAnalysis, // 数据分析窗口
  jlzs,         // 巨量指数窗口
  jlzsAnalysis, // 巨量指数分析窗口
  jlzsProfile,  // 巨量指数人群画像窗口
}

/// 多窗口管理器 - 专门管理设置窗口
class MultiWindowManager {
  static final MultiWindowManager _instance = MultiWindowManager._internal();
  factory MultiWindowManager() => _instance;

  final Map<int, WindowInfo> _windows = {};

  MultiWindowManager._internal() {
    // 监听窗口关闭事件
    _setupWindowCloseListener();
  }

  /// 设置窗口关闭监听器
  void _setupWindowCloseListener() {
    // 不在这里设置消息处理器，避免与HomeLogic冲突
    // 窗口关闭处理将通过HomeLogic转发
    print('多窗口管理器初始化完成');
  }

  /// 创建新窗口
  Future<int> createWindow(WindowType type, {Map<String, dynamic>? args}) async {
    final config = WindowConfig.configs[type]!;
    final windowArgs = {
      'windowType': type.name,  // 使用更明确的键名
      'type': type.name,
      'config': config.toJson(),
      ...?args,
    };

    print('创建窗口: $type, 参数: ${jsonEncode(windowArgs)}');
    final window = await DesktopMultiWindow.createWindow(jsonEncode(windowArgs));

    try {
      // 设置窗口基本属性
      await window.setTitle(config.title);

      // 计算居中位置并设置窗口大小和位置
      await _setCenteredFrame(window, config.size);

      // 设置窗口是否可调整大小
      if (!config.resizable) {
        print('设置窗口不可调整大小: ${config.title}');
        await window.resizable(false);
      }

      // 设置窗口是否置顶
      if (config.alwaysOnTop) {
        print('设置窗口置顶: ${config.title}');
        // await window.setAlwaysOnTop(true);
      }

    } catch (e) {
      print('设置窗口属性时出错: $e');
      // 即使设置属性失败，也继续创建窗口
      // 如果居中设置失败，使用默认的center方法
      try {
        await window.center();
      } catch (centerError) {
        print('窗口居中失败: $centerError');
      }
    }

    final windowId = window.windowId;
    _windows[windowId] = WindowInfo(windowId, type, window);

    return windowId;
  }

  /// 显示窗口
  Future<void> showWindow(int windowId) async {
    final windowInfo = _windows[windowId];
    if (windowInfo != null) {
      await windowInfo.window.show();

      // 窗口显示后再次确保设置正确应用
      final config = WindowConfig.configs[windowInfo.type];
      if (config != null) {
        try {
          if (!config.resizable) {
            await windowInfo.window.resizable(true);
            print('窗口显示后设置不可调整大小: ${config.title}');
          }
          if (config.alwaysOnTop) {
            await windowInfo.window.resizable(true);
            print('窗口显示后设置置顶: ${config.title}');
          }
        } catch (e) {
          print('窗口显示后设置属性失败: $e');
        }
      }
    }
  }

  /// 关闭窗口
  Future<void> closeWindow(int windowId) async {
    final windowInfo = _windows[windowId];
    if (windowInfo != null) {
      await windowInfo.window.close();
      _windows.remove(windowId);
    }
  }

  /// 隐藏窗口
  Future<void> hideWindow(int windowId) async {
    final windowInfo = _windows[windowId];
    if (windowInfo != null) {
      await windowInfo.window.hide();
    }
  }

  /// 设置窗口的便捷方法
  Future<void> openSettingsWindow() async {
    await _createWindow(WindowType.settings);
  }

  /// 数据分析窗口的便捷方法
  Future<void> openDataAnalysisWindow() async {
    await _createWindow(WindowType.dataAnalysis);
  }

  /// 巨量指数窗口的便捷方法
  Future<void> openJlzsWindow() async {
    await _createWindow(WindowType.jlzs);
  }

  /// 巨量指数分析窗口的便捷方法
  Future<void> openJlzsAnalysisWindow() async {
    await _createWindow(WindowType.jlzsAnalysis);
  }

  /// 巨量指数人群画像窗口的便捷方法
  Future<void> openJlzsProfileWindow() async {
    await _createWindow(WindowType.jlzsProfile);
  }

  /// 简单创建窗口（按照官方示例）
  Future<void> _createWindow(WindowType type) async {
    try {
      print('创建 $type 窗口');
      final windowId = await createWindow(type);
      await showWindow(windowId);
      print('窗口 $windowId ($type) 创建成功');
    } catch (e) {
      print('创建窗口失败: $e');
    }
  }

  /// 处理窗口关闭（公共方法）
  void handleWindowClosed(String windowTypeStr) {
    try {
      final windowType = WindowType.values.byName(windowTypeStr);
      final windowToRemove = _windows.values
          .where((w) => w.type == windowType)
          .firstOrNull;
      if (windowToRemove != null) {
        _windows.remove(windowToRemove.id);
        print('窗口 ${windowToRemove.id} ($windowType) 已从管理器中移除');
      }
    } catch (e) {
      print('处理窗口关闭时出错: $e');
    }
  }

  /// 获取所有窗口信息
  Map<int, WindowInfo> get windows => Map.unmodifiable(_windows);

  /// 设置窗口居中显示
  Future<void> _setCenteredFrame(WindowController window, Size windowSize) async {
    try {
      // 获取屏幕尺寸
      final screenSize = await _getScreenSize();

      // 计算居中位置
      final centerX = (screenSize.width - windowSize.width) / 2;
      final centerY = (screenSize.height - windowSize.height) / 2;

      // 确保位置不为负数，并转换为double类型
      final x = (centerX > 0 ? centerX : 0).toDouble();
      final y = (centerY > 0 ? centerY : 0).toDouble();

      final centeredOffset = Offset(x, y);
      final frame = centeredOffset & windowSize;

      print('设置窗口居中: 屏幕${screenSize.width}x${screenSize.height}, 窗口${windowSize.width}x${windowSize.height}, 位置($x, $y)');
      await window.setFrame(frame);
    } catch (e) {
      print('计算居中位置失败: $e');
      // 如果计算失败，使用默认的center方法
      await window.center();
    }
  }

  /// 获取屏幕尺寸
  Future<Size> _getScreenSize() async {
    try {
      // 尝试从主窗口获取屏幕尺寸
      if (WidgetsBinding.instance.window.physicalSize != Size.zero) {
        final physicalSize = WidgetsBinding.instance.window.physicalSize;
        final devicePixelRatio = WidgetsBinding.instance.window.devicePixelRatio;
        final logicalSize = physicalSize / devicePixelRatio;
        print('检测到屏幕尺寸: ${logicalSize.width}x${logicalSize.height}');
        return logicalSize;
      }
    } catch (e) {
      print('获取屏幕尺寸失败: $e');
    }

    // 如果无法获取，使用常见的默认屏幕尺寸
    print('使用默认屏幕尺寸: 1920x1080');
    return const Size(1920, 1080);
  }
}

/// 窗口信息类
class WindowInfo {
  final int id;
  final WindowType type;
  final WindowController window;

  WindowInfo(this.id, this.type, this.window);
}

/// 窗口配置类
class WindowConfig {
  final WindowType type;
  final String title;
  final Size size;
  final bool resizable;
  final bool alwaysOnTop;

  const WindowConfig({
    required this.type,
    required this.title,
    required this.size,
    this.resizable = true,
    this.alwaysOnTop = false,
  });

  /// 窗口配置定义
  static const Map<WindowType, WindowConfig> configs = {
    WindowType.settings: WindowConfig(
      type: WindowType.settings,
      title: '系统设置',
      size: Size(600, 1000),
      resizable: true,
      alwaysOnTop: false,
    ),
    WindowType.dataAnalysis: WindowConfig(
      type: WindowType.dataAnalysis,
      title: '数据分析',
      size: Size(1000, 700),
      resizable: true,
      alwaysOnTop: false,
    ),
    WindowType.jlzs: WindowConfig(
      type: WindowType.jlzs,
      title: '巨量指数',
      size: Size(1800, 1400),
      resizable: false,
      alwaysOnTop: false,
    ),
    WindowType.jlzsAnalysis: WindowConfig(
      type: WindowType.jlzsAnalysis,
      title: '巨量指数分析',
      size: Size(1500, 800),
      resizable: true,
      alwaysOnTop: false,
    ),
    WindowType.jlzsProfile: WindowConfig(
      type: WindowType.jlzsProfile,
      title: '巨量指数人群画像',
      size: Size(1000, 700),
      resizable: true,
      alwaysOnTop: false,
    ),
  };

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'type': type.name,
      'title': title,
      'width': size.width,
      'height': size.height,
      'resizable': resizable,
      'alwaysOnTop': alwaysOnTop,
    };
  }

  /// 从JSON创建
  static WindowConfig fromJson(Map<String, dynamic> json) {
    return WindowConfig(
      type: WindowType.values.byName(json['type']),
      title: json['title'],
      size: Size(json['width'], json['height']),
      resizable: json['resizable'] ?? true,
      alwaysOnTop: json['alwaysOnTop'] ?? false,
    );
  }
}
