import 'dart:io';
import 'dart:convert';
import 'package:path/path.dart' as path;
import 'package:puppeteer/puppeteer.dart';
import 'package:http/http.dart' as http;

/// Puppeteer Chrome版本信息
class ChromeVersion {
  final String version;
  final String platform;
  final bool isExtracted;
  final String? executablePath;
  final int? sizeInMB;
  final DateTime? lastUsed;

  ChromeVersion({
    required this.version,
    required this.platform,
    this.isExtracted = false,
    this.executablePath,
    this.sizeInMB,
    this.lastUsed,
  });

  /// 获取版本状态描述
  String get statusDescription {
    if (isExtracted && executablePath != null) {
      return '可用 (${sizeInMB ?? 0}MB)';
    } else {
      return '不完整';
    }
  }
}

/// 下载进度回调
typedef ProgressCallback = void Function(int received, int total, double progress);

/// Puppeteer Chrome管理器
/// 专门管理Puppeteer自动下载的Chrome版本
class ChromeManager {
  static final ChromeManager _instance = ChromeManager._internal();
  factory ChromeManager() => _instance;
  ChromeManager._internal();

  static const String _localChromeDir = '.local-chrome';

  /// 获取Puppeteer已下载的Chrome版本
  Future<List<ChromeVersion>> getLocalVersions() async {
    List<ChromeVersion> versions = [];

    try {
      Directory localDir = Directory(_localChromeDir);
      if (!await localDir.exists()) {
        print('Chrome目录不存在: $_localChromeDir');
        return versions;
      }

      await for (var entity in localDir.list()) {
        if (entity is Directory) {
          String versionName = path.basename(entity.path);
          String platform = _getCurrentPlatform();

          // 检查是否已解压并可用
          String executablePath = _getExecutablePath(entity.path, platform);
          bool isExtracted = await File(executablePath).exists();

          // 计算目录大小
          int? sizeInMB;
          try {
            sizeInMB = await _calculateDirectorySize(entity.path);
          } catch (e) {
            print('计算目录大小失败: $e');
          }

          // 获取最后修改时间
          DateTime? lastUsed;
          try {
            var stat = await entity.stat();
            lastUsed = stat.modified;
          } catch (e) {
            print('获取修改时间失败: $e');
          }

          versions.add(ChromeVersion(
            version: versionName,
            platform: platform,
            isExtracted: isExtracted,
            executablePath: isExtracted ? executablePath : null,
            sizeInMB: sizeInMB,
            lastUsed: lastUsed,
          ));
        }
      }

      // 按版本号排序
      versions.sort((a, b) => b.version.compareTo(a.version));
    } catch (e) {
      print('获取本地Chrome版本失败: $e');
    }

    return versions;
  }

  /// 获取Chrome版本的详细信息
  Future<Map<String, dynamic>> getChromeInfo() async {
    var versions = await getLocalVersions();
    var recommended = await getRecommendedVersion();

    return {
      'totalVersions': versions.length,
      'availableVersions': versions.where((v) => v.isExtracted).length,
      'recommendedVersion': recommended?.version,
      'totalSizeMB': versions.fold<int>(0, (sum, v) => sum + (v.sizeInMB ?? 0)),
    };
  }

  /// 使用Puppeteer下载Chrome
  Future<bool> downloadChromeWithPuppeteer({
    String? version,
    ProgressCallback? onProgress,
  }) async {
    try {
      print('🚀 开始使用Puppeteer下载Chrome...');

      // 如果没有指定版本，让Puppeteer下载默认版本
      if (version == null) {
        print('📦 下载Puppeteer默认Chrome版本...');

        // 创建一个临时的Puppeteer实例来触发下载
        var browser = await puppeteer.launch(
          headless: true,
          timeout: Duration(minutes: 10), // 给足够时间下载
        );

        await browser.close();
        print('✅ Puppeteer Chrome下载完成');
        return true;
      } else {
        // 下载指定版本
        return await _downloadSpecificVersion(version, onProgress);
      }
    } catch (e) {
      print('❌ Puppeteer下载Chrome失败: $e');
      return false;
    }
  }

  /// 下载指定版本的Chrome
  Future<bool> _downloadSpecificVersion(String version, ProgressCallback? onProgress) async {
    try {
      // 获取下载URL
      String? downloadUrl = await _getDownloadUrl(version);
      if (downloadUrl == null) {
        print('❌ 无法获取Chrome $version 的下载链接');
        return false;
      }

      print('📥 开始下载Chrome $version...');
      print('🔗 下载链接: $downloadUrl');

      // 创建版本目录
      String versionDir = path.join(_localChromeDir, version);
      await Directory(versionDir).create(recursive: true);

      String platform = _getCurrentPlatform();
      String zipPath = path.join(versionDir, 'chrome-$platform.zip');

      // 下载文件
      var request = await HttpClient().getUrl(Uri.parse(downloadUrl));
      var response = await request.close();

      if (response.statusCode == 200) {
        var file = File(zipPath);
        var sink = file.openWrite();

        int received = 0;
        int total = response.contentLength;

        await response.listen((data) {
          sink.add(data);
          received += data.length;

          if (onProgress != null && total > 0) {
            double progress = received / total;
            onProgress(received, total, progress);
          }
        }).asFuture();

        await sink.close();

        print('✅ Chrome $version 下载完成');

        // 自动解压
        bool extracted = await _extractZip(zipPath, versionDir);
        if (extracted) {
          print('✅ Chrome $version 解压完成');
          // 删除zip文件节省空间
          await File(zipPath).delete();
        }

        return extracted;
      } else {
        print('❌ 下载失败，状态码: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      print('❌ 下载指定版本失败: $e');
      return false;
    }
  }

  /// 获取Chrome版本的下载URL
  Future<String?> _getDownloadUrl(String version) async {
    try {
      String apiUrl = 'https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json';
      var response = await http.get(Uri.parse(apiUrl));

      if (response.statusCode == 200) {
        var data = json.decode(response.body);
        var versions = data['versions'] as List;

        // 查找指定版本
        for (var versionData in versions) {
          if (versionData['version'] == version) {
            var downloads = versionData['downloads'];
            if (downloads != null && downloads['chrome'] != null) {
              var chromeDownloads = downloads['chrome'] as List;
              String platform = _getCurrentPlatform();

              // 查找当前平台的下载链接
              for (var download in chromeDownloads) {
                if (download['platform'] == platform) {
                  return download['url'];
                }
              }
            }
            break;
          }
        }
      }
    } catch (e) {
      print('获取下载URL失败: $e');
    }

    return null;
  }

  /// 解压zip文件
  Future<bool> _extractZip(String zipPath, String extractDir) async {
    try {
      // 这里可以使用archive包或者系统命令来解压
      // 为了简化，先使用系统命令
      if (Platform.isWindows) {
        var result = await Process.run('powershell', [
          'Expand-Archive',
          '-Path', zipPath,
          '-DestinationPath', extractDir,
          '-Force'
        ]);
        return result.exitCode == 0;
      } else {
        var result = await Process.run('unzip', ['-o', zipPath, '-d', extractDir]);
        return result.exitCode == 0;
      }
    } catch (e) {
      print('解压失败: $e');
      return false;
    }
  }

  /// 获取可用的Chrome版本列表
  Future<List<String>> getAvailableVersions() async {
    try {
      String apiUrl = 'https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json';
      var response = await http.get(Uri.parse(apiUrl));

      if (response.statusCode == 200) {
        var data = json.decode(response.body);
        var versions = data['versions'] as List;

        // 只返回最近的10个版本
        return versions
            .take(10)
            .map((v) => v['version'] as String)
            .toList();
      }
    } catch (e) {
      print('获取可用版本失败: $e');
    }

    return [];
  }

  /// 清理不完整的Chrome版本
  Future<int> cleanupIncompleteVersions() async {
    int cleanedCount = 0;

    try {
      var versions = await getLocalVersions();

      for (var version in versions) {
        if (!version.isExtracted) {
          bool deleted = await deleteVersion(version.version);
          if (deleted) {
            cleanedCount++;
            print('已清理不完整版本: ${version.version}');
          }
        }
      }
    } catch (e) {
      print('清理不完整版本失败: $e');
    }

    return cleanedCount;
  }

  /// 删除Chrome版本
  Future<bool> deleteVersion(String version) async {
    try {
      String versionDir = path.join(_localChromeDir, version);
      Directory dir = Directory(versionDir);
      
      if (await dir.exists()) {
        await dir.delete(recursive: true);
        print('已删除Chrome版本: $version');
        return true;
      }
      
      return false;
    } catch (e) {
      print('删除Chrome版本失败: $e');
      return false;
    }
  }

  /// 获取当前平台标识
  String _getCurrentPlatform() {
    if (Platform.isWindows) {
      return 'win64';
    } else if (Platform.isMacOS) {
      return 'mac-x64';
    } else if (Platform.isLinux) {
      return 'linux64';
    } else {
      return 'win64'; // 默认
    }
  }

  /// 获取可执行文件路径
  String _getExecutablePath(String versionDir, String platform) {
    if (Platform.isWindows) {
      return path.join(versionDir, 'chrome-win64', 'chrome.exe');
    } else if (Platform.isMacOS) {
      return path.join(versionDir, 'chrome-mac', 'Chromium.app', 'Contents', 'MacOS', 'Chromium');
    } else {
      return path.join(versionDir, 'chrome-linux64', 'chrome');
    }
  }

  /// 获取推荐的Chrome版本（最新的已解压版本）
  Future<ChromeVersion?> getRecommendedVersion() async {
    var localVersions = await getLocalVersions();
    var extractedVersions = localVersions.where((v) => v.isExtracted).toList();

    if (extractedVersions.isNotEmpty) {
      // 按版本号排序，返回最新的
      extractedVersions.sort((a, b) => b.version.compareTo(a.version));
      return extractedVersions.first;
    }

    return null;
  }

  /// 计算目录大小（MB）
  Future<int> _calculateDirectorySize(String dirPath) async {
    int totalSize = 0;

    try {
      Directory dir = Directory(dirPath);
      await for (var entity in dir.list(recursive: true)) {
        if (entity is File) {
          var stat = await entity.stat();
          totalSize += stat.size;
        }
      }
    } catch (e) {
      print('计算目录大小失败: $e');
    }

    return (totalSize / (1024 * 1024)).round(); // 转换为MB
  }
}
