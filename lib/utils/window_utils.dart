import 'package:flutter/material.dart';
import 'package:window_manager/window_manager.dart';

class WindowManagerUtils {
  static final WindowManagerUtils instance = WindowManagerUtils._();

  WindowManagerUtils._();

  Future<void> initializeWindow() async {
    WindowOptions windowOptions = WindowOptions(
      size: Size(300, 400),
      center: true,
      backgroundColor: Colors.transparent,
      skipTaskbar: false,
      // titleBarStyle: TitleBarStyle.hidden,
    );
    await windowManager.waitUntilReadyToShow(windowOptions, () async {
      await windowManager.show();
      await windowManager.focus();
    });
  }



  void setSize(double width, double height){
    WindowManager.instance.setSize(Size(width, height));
  }
}