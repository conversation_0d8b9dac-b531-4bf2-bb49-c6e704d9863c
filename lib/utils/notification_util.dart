import 'package:local_notifier/local_notifier.dart';
import 'dart:io';

class NotificationUtil {
  static Future<void> init() async {
    if (Platform.isWindows) {
      await localNotifier.setup(
        appName: '百度指数',
        shortcutPolicy: ShortcutPolicy.requireCreate,
      );
    }
  }

  static void show({
    required String title,
    required String body,
  }) {
    if (!Platform.isWindows) return;

    LocalNotification notification = LocalNotification(
      title: title,
      body: body,
    );

    notification.show();
  }
} 