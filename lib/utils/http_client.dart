import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:native_dio_adapter/native_dio_adapter.dart';
import 'dart:io';

class HttpClientUtil {
  static final HttpClientUtil _instance = HttpClientUtil._internal();
  factory HttpClientUtil() => _instance;

  late Dio dio;

  HttpClientUtil._internal() {
    dio = Dio(BaseOptions(
      connectTimeout: const Duration(seconds: 300),
      receiveTimeout: const Duration(seconds: 300),
      sendTimeout: const Duration(seconds: 300), 
    ));
    
    dio.httpClientAdapter = NativeAdapter();
  }

  /// 请求方法封装
  Future<T?> request<T>({
    required String url,
    String method = 'GET',
    Map<String, dynamic>? headers,
    Map<String, dynamic>? queryParams,
    dynamic data,
    int maxRetries = 3,
    int retryDelay = 1,
    ResponseType responseType = ResponseType.json,
    String? contentType,
    String? proxyAddress,
    String? proxyUsername,
    String? proxyPassword,
  }) async {
    int retryCount = 0;
    DioException? lastError;

    // 动态设置代理
    if (proxyAddress != null) {
      if (dio.httpClientAdapter is IOHttpClientAdapter) {
        (dio.httpClientAdapter as IOHttpClientAdapter).onHttpClientCreate = (client) {
          client.findProxy = (uri) {
            if (proxyUsername != null && proxyPassword != null) {
              return 'PROXY $proxyUsername:$proxyPassword@$proxyAddress';
            }
            return 'PROXY $proxyAddress';
          };
          client.badCertificateCallback = (cert, host, port) => true;
          return client;
        };
      }
    }

    while (retryCount <= maxRetries) {
      try {
        final response = await dio.request(
          url,
          options: Options(
            method: method,
            headers: headers,
            responseType: responseType,
            contentType: contentType,
          ),
          queryParameters: queryParams,
          data: data,
        );

        if (response.statusCode == 200) {
          return response.data as T;
        }

        throw DioException(
          requestOptions: response.requestOptions,
          response: response,
          message: 'HTTP错误: ${response.statusCode}',
        );

      } on DioException catch (e) {
        lastError = e;
        print(e);

        if (_shouldRetry(e) && retryCount < maxRetries) {
          retryCount++;
          print("🔄 第$retryCount次重试请: $url");
          await Future.delayed(Duration(seconds: retryDelay * retryCount));
          continue;
        }

        throw lastError!;
      }
    }

    throw lastError ?? DioException(
      requestOptions: RequestOptions(path: url),
      message: '请求失败，已重试$maxRetries次',
    );
  }

  bool _shouldRetry(DioException error) {
    return error.type == DioExceptionType.connectionTimeout ||
        error.type == DioExceptionType.sendTimeout ||
        error.type == DioExceptionType.receiveTimeout ||
        error.type == DioExceptionType.connectionError ||
        (error.response?.statusCode ?? 0) >= 500;
  }

  Future<T?> get<T>({
    required String url,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? queryParams,
    int maxRetries = 3,
    ResponseType responseType = ResponseType.json,
    String? proxyAddress,
    String? proxyUsername,
    String? proxyPassword,
  }) async {
    return request<T>(
      url: url,
      method: 'GET',
      headers: headers,
      queryParams: queryParams,
      maxRetries: maxRetries,
      responseType: responseType,
      proxyAddress: proxyAddress,
      proxyUsername: proxyUsername,
      proxyPassword: proxyPassword,
    );
  }

  Future<T?> post<T>({
    required String url,
    Map<String, dynamic>? headers,
    Map<String, dynamic>? queryParams,
    dynamic data,
    int maxRetries = 3,
    ResponseType responseType = ResponseType.json,
    String? contentType,
    String? proxyAddress,
    String? proxyUsername,
    String? proxyPassword,
  }) async {
    return request<T>(
      url: url,
      method: 'POST',
      headers: headers,
      queryParams: queryParams,
      data: data,
      maxRetries: maxRetries,
      responseType: responseType,
      contentType: contentType,
      proxyAddress: proxyAddress,
      proxyUsername: proxyUsername,
      proxyPassword: proxyPassword,
    );
  }
}