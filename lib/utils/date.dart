import 'package:intl/intl.dart';

class Period {
  final String start;
  final String end;
  final int days;

  Period({required this.start, required this.end, required this.days});
}

class YearDetails {
  final String start;
  final String end;
  final int days;
  final List<Period> months;
  final List<Period> weeks;

  YearDetails({required this.start, required this.end, required this.days, required this.months, required this.weeks});
}

List<YearDetails> daysInEachYear(String startDateStr, String endDateStr) {
  final DateFormat dateFormat = DateFormat('yyyy-MM-dd');
  DateTime startDate;
  DateTime endDate;

  try {
    startDate = dateFormat.parse(startDateStr);
  } catch (e) {
    throw ArgumentError('Invalid start date format');
  }

  try {
    endDate = dateFormat.parse(endDateStr);
  } catch (e) {
    throw ArgumentError('Invalid end date format');
  }

  List<YearDetails> yearsDetails = [];

  for (int year = startDate.year; year <= endDate.year; year++) {
    DateTime startOfYear, endOfYear;

    if (year == startDate.year) {
      startOfYear = startDate;
    } else {
      startOfYear = DateTime(year, 1, 1);
    }

    if (year == endDate.year) {
      endOfYear = endDate;
    } else {
      endOfYear = DateTime(year, 12, 31);
    }

    int days = endOfYear.difference(startOfYear).inDays + 1;

    List<Period> monthsDetails = [];
    for (int month = 1; month <= 12; month++) {
      if ((year == startDate.year && month < startDate.month) || (year == endDate.year && month > endDate.month)) {
        continue;
      }

      DateTime startOfMonth, endOfMonth;

      if (year == startDate.year && month == startDate.month) {
        startOfMonth = startDate;
        endOfMonth = DateTime(year, month + 1, 0);
      } else if (year == endDate.year && month == endDate.month) {
        startOfMonth = DateTime(year, month, 1);
        endOfMonth = endDate;
      } else {
        startOfMonth = DateTime(year, month, 1);
        endOfMonth = DateTime(year, month + 1, 0);
      }

      int monthDays = endOfMonth.difference(startOfMonth).inDays + 1;
      monthsDetails.add(Period(
        start: dateFormat.format(startOfMonth),
        end: dateFormat.format(endOfMonth),
        days: monthDays,
      ));
    }

    List<Period> weeksDetails = [];
    DateTime wStart = startOfYear;
    while (!wStart.isAfter(endOfYear)) {
      DateTime wEnd = wStart.add(Duration(days: 6));
      if (wEnd.isAfter(endOfYear)) {
        wEnd = endOfYear;
      }
      int weekDays = wEnd.difference(wStart).inDays + 1;
      weeksDetails.add(Period(
        start: dateFormat.format(wStart),
        end: dateFormat.format(wEnd),
        days: weekDays,
      ));
      wStart = wEnd.add(Duration(days: 1));
    }

    yearsDetails.add(YearDetails(
      start: dateFormat.format(startOfYear),
      end: dateFormat.format(endOfYear),
      days: days,
      months: monthsDetails,
      weeks: weeksDetails,
    ));
  }

  return yearsDetails;
}

List<String> getDateRange(String startDate, String endDate) {
  final DateFormat formatter = DateFormat('yyyy-MM-dd');
  DateTime start;
  DateTime end;

  try {
    start = formatter.parseStrict(startDate);
  } catch (e) {
    print("Error parsing start date: $e");
    return [];
  }

  try {
    end = formatter.parseStrict(endDate);
  } catch (e) {
    print("Error parsing end date: $e");
    return [];
  }

  List<String> dateRange = [];
  for (DateTime d = start; d.isBefore(end) || d.isAtSameMomentAs(end); d = d.add(Duration(days: 1))) {
    dateRange.add(formatter.format(d));
  }

  return dateRange;
}

