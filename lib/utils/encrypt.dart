import 'dart:convert';
import 'package:encrypt/encrypt.dart' as encrypt;
import 'package:intl/intl.dart';

String getCipherText(List<String> keyword,String sTime,String k) {
  print(keyword);
  String startTime = sTime;
  String endTime = '${DateTime.now().millisecondsSinceEpoch}';
  final globalHeaders = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
  };

  final waitEncryptedData = {
    'ua': globalHeaders["User-Agent"],
    'url': "https://index.baidu.com/v2/main/index.html#/trend/${Uri.encodeComponent(keyword[0])}?words=${Uri.encodeComponent(keyword.join(",")).replaceAll("%2C", ",")}",
    'platform': 'Win32',
    'clientTs': endTime,
    'version': '*******',
  };

  final key = encrypt.Key.fromUtf8(k);
  final iv = encrypt.IV.fromUtf8('1234567887654321');

  print(waitEncryptedData);
  final encrypter = encrypt.Encrypter(encrypt.AES(key, mode: encrypt.AESMode.cbc, padding: 'PKCS7'));

  // Convert JSON object to string and encrypt
  final encryptedData = encrypter.encrypt("""{"ua":"${globalHeaders["User-Agent"]}","url":"https://index.baidu.com/v2/main/index.html#/trend/${Uri.encodeComponent(keyword[0])}?words=${Uri.encodeComponent(keyword.join(",")).replaceAll("%2C", ",")}","platform":"Win32","clientTs":$endTime,"version":"*******"}""", iv: iv);
  // final encryptedData = encrypter.encrypt("""{"ua":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36","url":"https://index.baidu.com/v2/main/index.html#/trend/%E8%B5%A4%E5%A3%81%E4%B9%8B%E6%88%98?words=%E8%B5%A4%E5%A3%81%E4%B9%8B%E6%88%98","platform":"Win32","clientTs":1732354160891,"version":"*******"}""", iv: iv);

  //'{"ua":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36","url":"https://index.baidu.com/v2/main/index.html#/trend/%E8%B5%A4%E5%A3%81%E4%B9%8B%E6%88%98?words=%E8%B5%A4%E5%A3%81%E4%B9%8B%E6%88%98","platform":"Win32","clientTs":1732354160891,"version":"*******"}'
  // print("""{"ua":"${globalHeaders["User-Agent"]}","url":"https://index.baidu.com/v2/main/index.html#/trend/${Uri.encodeComponent(keyword[0])}?words=${Uri.encodeComponent(keyword.join(",")).replaceAll("%2C", ",")}","platform":"Win32","clientTs":$endTime,"version":"*******"}""");
  final cipherText = '$startTime' + '_' + '$endTime' + '_' + base64.encode(encryptedData.bytes);

  return cipherText;
}

// "{"ua":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","url":"https://index.baidu.com/v2/main/index.html#/trend/%E9%9B%BE%E9%9C%BE?words=%E9%9B%BE%E9%9C%BE,%E4%B8%87%E5%8F%A4%E7%A5%9E%E5%B8%9D","platform":"Win32","clientTs":1719310996478,"version":"*******"}"
// "{"ua":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","url":"https://index.baidu.com/v2/main/index.html#/trend/%E9%9B%BE%E9%9C%BE?words=%E9%9B%BE%E9%9C%BE","platform":"Win32","clientTs":1719309815043,"version":"*******"}"
