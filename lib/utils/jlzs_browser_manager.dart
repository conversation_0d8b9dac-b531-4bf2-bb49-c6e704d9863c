import 'dart:io';
import 'package:puppeteer/puppeteer.dart';
import '../model/jlzs_account_new_model.dart';
import 'chrome_manager.dart';

/// 巨量指数浏览器管理器
/// 负责管理账号与浏览器实例的映射关系
class JlzsBrowserManager {
  static final JlzsBrowserManager _instance = JlzsBrowserManager._internal();
  factory JlzsBrowserManager() => _instance;
  JlzsBrowserManager._internal();

  // 账号ID -> 浏览器实例映射
  final Map<String, Browser> _accountBrowsers = {};
  
  // 账号ID -> 调试端口映射
  final Map<String, int> _debugPorts = {};
  
  // 基础端口号
  static const int _basePort = 9222;
  
  // 已分配的端口
  final Set<int> _usedPorts = {};

  // Chrome管理器
  final ChromeManager _chromeManager = ChromeManager();

  /// 获取账号对应的浏览器实例
  Browser? getBrowserByAccountId(String accountId) {
    return _accountBrowsers[accountId];
  }

  /// 为账号创建浏览器实例
  Future<Browser> createBrowserForAccount(JlzsAccountModel account) async {
    print('🚀 开始为账号 ${account.id} 创建浏览器实例...');

    // 如果已存在，直接返回
    if (_accountBrowsers.containsKey(account.id)) {
      print('✅ 账号 ${account.id} 的浏览器实例已存在，直接返回');
      return _accountBrowsers[account.id]!;
    }

    // 分配调试端口
    int debugPort = await _getAvailablePort(account.id);
    print('📡 为账号 ${account.id} 分配调试端口: $debugPort');

    // 创建用户数据目录
    String userDataDir = await _createUserDataDir(account.id);
    print('📁 用户数据目录: $userDataDir');

    // 构建启动参数（简化版本，减少可能的冲突）
    List<String> args = [
      '--remote-debugging-port=$debugPort', // 关键：指定调试端口
      '--user-data-dir=$userDataDir',      // 关键：指定用户数据目录，确保进程隔离
      '--no-first-run',                   // 跳过首次运行设置
      '--no-default-browser-check',       // 不检查默认浏览器
      '--disable-extensions',             // 禁用扩展
      '--disable-sync',                   // 禁用同步，避免多实例冲突
      '--disable-background-timer-throttling', // 禁用后台定时器限制
      '--disable-renderer-backgrounding', // 禁用渲染器后台化
      '--disable-blink-features=AutomationControlled', // 避免被检测为自动化
    ];

    // 添加代理配置（仅添加代理服务器，认证在页面级处理）
    if (account.proxy != null) {
      try {
        // 使用百度指数的简化代理配置方式：直接使用地址:端口
        args.add('--proxy-server=${account.proxy!.address}:${account.proxy!.port}');
        print('🔒 已添加代理配置: ${account.proxy!.address}:${account.proxy!.port}');

        // 注意：代理认证将在页面级通过page.authenticate()处理
        if (account.proxy!.username != null && account.proxy!.password != null) {
          print('🔐 代理认证将在页面级处理');
        }
      } catch (e) {
        print('⚠️ 代理配置添加失败: $e，将跳过代理设置');
      }
    }

    print('⚙️ 浏览器启动参数: ${args.join(' ')}');

    try {
      print('🌐 正在启动Puppeteer浏览器...');
      print('🔧 Puppeteer配置:');
      print('   - headless: false');
      print('   - userDataDir: $userDataDir');

      // 启动浏览器（先测试最简单的配置）
      print('🔄 启动Puppeteer浏览器...');

      // 获取推荐的Chrome版本
      var recommendedChrome = await _chromeManager.getRecommendedVersion();
      String? executablePath = recommendedChrome?.executablePath;

      if (executablePath != null) {
        print('🎯 使用推荐的Chrome版本: ${recommendedChrome!.version}');
        print('📍 Chrome路径: $executablePath');
      } else {
        print('⚠️ 未找到推荐的Chrome版本，使用Puppeteer默认配置');
      }

      var browser = await puppeteer.launch(
        headless: false,
        executablePath: executablePath,
        // userDataDir 已在 args 中指定，避免重复配置
        args: args,
      );

      print('✅ Puppeteer浏览器启动成功！');

      // 等待浏览器完全启动（增加等待时间）
      print('⏳ 等待浏览器完全启动...');
      await Future.delayed(Duration(seconds: 3));

      // 验证浏览器连接状态（增强版验证）
      try {
        print('🔍 验证浏览器连接状态...');

        // 第一步：检查浏览器版本（基础连接测试）
        var version = await browser.version.timeout(Duration(seconds: 10));
        print('✅ 浏览器连接验证成功，版本: $version');

        // 第二步：检查页面列表（确保浏览器功能正常）
        var pages = await browser.pages.timeout(Duration(seconds: 10));
        print('✅ 浏览器页面检查通过，当前页面数: ${pages.length}');

        // 第三步：检查WebSocket连接是否稳定
        print('🔍 检查WebSocket连接稳定性...');
        await Future.delayed(Duration(seconds: 1));

        // 再次检查版本，确保连接持续稳定
        await browser.version.timeout(Duration(seconds: 5));
        print('✅ WebSocket连接稳定性检查通过');

      } catch (e) {
        print('❌ 浏览器连接验证失败: $e');
        print('❌ 错误类型: ${e.runtimeType}');

        // 尝试优雅关闭浏览器
        try {
          await browser.close();
          print('✅ 已关闭失败的浏览器实例');
        } catch (closeError) {
          print('⚠️ 关闭失败的浏览器时出错: $closeError');
        }

        // 释放端口
        _usedPorts.remove(debugPort);

        throw Exception('浏览器启动后连接验证失败: $e');
      }

      // 保存映射关系
      _accountBrowsers[account.id] = browser;
      _debugPorts[account.id] = debugPort;
      account.debugPort = debugPort;

      print('✅ 为账号 ${account.maskedMobile.isNotEmpty ? account.maskedMobile : account.id} 创建浏览器实例成功，端口: $debugPort');

      return browser;
    } catch (e) {
      // 释放端口
      _usedPorts.remove(debugPort);
      print('❌ 创建浏览器实例失败: $e');
      print('❌ 错误类型: ${e.runtimeType}');
      print('❌ 错误堆栈: ${StackTrace.current}');
      rethrow;
    }
  }

  /// 关闭账号对应的浏览器
  Future<void> closeBrowserForAccount(String accountId) async {
    var browser = _accountBrowsers[accountId];
    if (browser != null) {
      try {
        await browser.close();
        print('已关闭账号 $accountId 的浏览器实例');
      } catch (e) {
        print('关闭浏览器失败: $e');
      }
      
      // 清理映射关系
      _accountBrowsers.remove(accountId);
      
      // 释放端口
      int? port = _debugPorts.remove(accountId);
      if (port != null) {
        _usedPorts.remove(port);
      }
    }
  }

  /// 重新连接到现有浏览器实例
  Future<Browser?> reconnectToBrowser(String accountId, int debugPort) async {
    try {
      String browserWSEndpoint = 'ws://localhost:$debugPort';
      var browser = await puppeteer.connect(
        browserUrl: browserWSEndpoint,
      );
      
      // 更新映射关系
      _accountBrowsers[accountId] = browser;
      _debugPorts[accountId] = debugPort;
      _usedPorts.add(debugPort);
      
      print('成功重连到账号 $accountId 的浏览器实例，端口: $debugPort');
      return browser;
    } catch (e) {
      print('重连浏览器失败: $e');
      return null;
    }
  }

  /// 检查浏览器实例是否存在（简单检查）
  bool isBrowserRunning(String accountId) {
    // 只检查实例是否存在，不进行任何网络操作
    return _accountBrowsers.containsKey(accountId);
  }

  /// 重启浏览器实例（应用新的代理配置）
  Future<Browser> restartBrowserWithNewProxy(String accountId, JlzsAccountModel account) async {
    // 先关闭现有浏览器
    await closeBrowserForAccount(accountId);
    
    // 等待一下确保完全关闭
    await Future.delayed(Duration(seconds: 2));
    
    // 创建新的浏览器实例
    return await createBrowserForAccount(account);
  }

  /// 获取所有活跃的浏览器实例
  Map<String, Browser> get activeBrowsers => Map.unmodifiable(_accountBrowsers);

  /// 获取端口映射
  Map<String, int> get debugPorts => Map.unmodifiable(_debugPorts);

  /// 关闭所有浏览器实例
  Future<void> closeAllBrowsers() async {
    List<String> accountIds = _accountBrowsers.keys.toList();
    for (String accountId in accountIds) {
      await closeBrowserForAccount(accountId);
    }
    _usedPorts.clear();
    print('已关闭所有浏览器实例');
  }

  /// 获取可用端口
  Future<int> _getAvailablePort(String accountId) async {
    // 如果已经分配过端口，先检查是否还可用
    if (_debugPorts.containsKey(accountId)) {
      int existingPort = _debugPorts[accountId]!;
      if (await _isPortAvailable(existingPort)) {
        return existingPort;
      } else {
        // 端口不可用，移除并重新分配
        _debugPorts.remove(accountId);
        _usedPorts.remove(existingPort);
      }
    }

    // 寻找可用端口，从一个更高的端口开始避免冲突
    int port = _basePort + 100; // 从9322开始
    while (_usedPorts.contains(port) || !(await _isPortAvailable(port))) {
      port++;
      if (port > 65535) {
        throw Exception('无法找到可用端口');
      }
    }

    _usedPorts.add(port);
    return port;
  }

  /// 检查端口是否真正可用
  Future<bool> _isPortAvailable(int port) async {
    try {
      // 尝试绑定端口来检查是否可用
      ServerSocket server = await ServerSocket.bind('127.0.0.1', port);
      await server.close();

      // 额外等待一小段时间，确保端口完全释放
      await Future.delayed(Duration(milliseconds: 100));
      return true;
    } catch (e) {
      // 端口被占用
      print('🔍 端口 $port 不可用: $e');
      return false;
    }
  }

  /// 创建用户数据目录
  Future<String> _createUserDataDir(String accountId) async {
    String userDataDir = './jlzs_data/account_$accountId';
    
    try {
      Directory dir = Directory(userDataDir);
      if (!await dir.exists()) {
        await dir.create(recursive: true);
        print('创建用户数据目录: $userDataDir');
      }
    } catch (e) {
      print('创建用户数据目录失败: $e');
      // 使用临时目录作为备选
      userDataDir = './temp/jlzs_account_$accountId';
      Directory dir = Directory(userDataDir);
      await dir.create(recursive: true);
    }
    
    return userDataDir;
  }

  /// 清理无效的浏览器实例
  void cleanupInactiveBrowsers() {
    List<String> accountIds = _accountBrowsers.keys.toList();
    for (String accountId in accountIds) {
      bool isRunning = isBrowserRunning(accountId);
      if (!isRunning) {
        print('清理无效的浏览器实例: $accountId');
      }
    }
  }

  /// 获取浏览器实例统计信息
  Map<String, int> getBrowserStats() {
    return {
      'total': _accountBrowsers.length,
      'ports_used': _usedPorts.length,
      'next_available_port': _basePort + _usedPorts.length,
    };
  }































  /// 清理资源
  void dispose() {
    closeAllBrowsers();
  }
}
