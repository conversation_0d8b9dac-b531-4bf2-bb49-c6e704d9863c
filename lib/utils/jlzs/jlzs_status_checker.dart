import 'dart:async';
import '../../model/jlzs_account_new_model.dart';

/// 状态检查器 - 负责批量状态检查和账号健康检查
class JlzsStatusChecker {
  
  /// 检查账号登录状态（简化版本）
  Future<bool> checkLoginStatus(JlzsAccountModel account) async {
    try {
      print('🔍 检查账号 ${account.maskedMobile.isNotEmpty ? account.maskedMobile : account.id} 的登录状态...');

      // 只检查session状态，不进行网络操作
      if (account.session?.isActive == true &&
          account.session?.userInfo['user_id'] != null &&
          account.session?.userInfo['user_id'] != 0) {
        print('✅ 登录状态：有效（通过session）');
        return true;
      }

      print('⚠️ 登录状态：无效或未登录');
      return false;
    } catch (e) {
      print('❌ 检查登录状态失败: $e');
      return false;
    }
  }

  /// 批量检查账号状态（并行处理）
  Future<Map<String, bool>> batchCheckLoginStatus(List<JlzsAccountModel> accounts) async {
    if (accounts.isEmpty) {
      return {};
    }

    print('🔍 开始批量检查 ${accounts.length} 个账号的登录状态...');
    Map<String, bool> results = {};

    // 并行检查所有账号
    List<Future<void>> futures = accounts.map((account) async {
      bool status = await checkLoginStatus(account);
      results[account.id] = status;
    }).toList();

    await Future.wait(futures);

    int successCount = results.values.where((v) => v).length;
    print('✅ 批量状态检查完成: $successCount/${accounts.length} 个账号在线');

    return results;
  }

  /// 检查msToken有效性
  bool checkMsTokenValidity(JlzsAccountModel account) {
    try {
      if (account.session?.userInfo['msToken'] == null) {
        return false;
      }

      var expiry = account.session?.userInfo['msTokenExpiry'];
      if (expiry != null) {
        DateTime expiryDate = DateTime.fromMillisecondsSinceEpoch(expiry);
        return DateTime.now().isBefore(expiryDate);
      }

      // 如果没有过期时间，认为有效
      return true;
    } catch (e) {
      print('⚠️ 检查msToken有效性失败: $e');
      return false;
    }
  }

  /// 批量检查msToken有效性
  Map<String, bool> batchCheckMsTokenValidity(List<JlzsAccountModel> accounts) {
    Map<String, bool> results = {};
    
    for (var account in accounts) {
      results[account.id] = checkMsTokenValidity(account);
    }
    
    return results;
  }

  /// 检查账号健康状态
  Map<String, dynamic> checkAccountHealth(JlzsAccountModel account) {
    Map<String, dynamic> health = {
      'overall': 'healthy',
      'issues': <String>[],
      'warnings': <String>[],
      'score': 100,
    };

    try {
      // 检查基本信息
      if (account.mobile.isEmpty) {
        health['warnings'].add('手机号为空');
        health['score'] -= 10;
      }

      // 检查session状态
      if (account.session == null) {
        health['issues'].add('Session未初始化');
        health['score'] -= 30;
        health['overall'] = 'unhealthy';
      } else {
        // 检查session活跃状态
        if (!account.session!.isActive) {
          health['issues'].add('Session未激活');
          health['score'] -= 20;
          health['overall'] = 'warning';
        }

        // 检查用户信息
        if (account.session!.userInfo['user_id'] == null || 
            account.session!.userInfo['user_id'] == 0) {
          health['issues'].add('用户ID无效');
          health['score'] -= 25;
          health['overall'] = 'unhealthy';
        }

        // 检查msToken
        if (account.session!.userInfo['msToken'] == null) {
          health['warnings'].add('msToken缺失');
          health['score'] -= 15;
          if (health['overall'] == 'healthy') {
            health['overall'] = 'warning';
          }
        } else {
          // 检查msToken有效性
          if (!checkMsTokenValidity(account)) {
            health['warnings'].add('msToken已过期');
            health['score'] -= 10;
            if (health['overall'] == 'healthy') {
              health['overall'] = 'warning';
            }
          }
        }

        // 检查最后活跃时间
        var timeDiff = DateTime.now().difference(account.session!.lastActive);
        if (timeDiff.inHours > 24) {
          health['warnings'].add('超过24小时未活跃');
          health['score'] -= 5;
        }
      }

      // 检查账号状态
      if (account.status != AccountStatus.active) {
        health['issues'].add('账号状态异常: ${account.status}');
        health['score'] -= 20;
        health['overall'] = 'unhealthy';
      }

      // 根据分数调整整体状态
      if (health['score'] < 60) {
        health['overall'] = 'unhealthy';
      } else if (health['score'] < 80) {
        health['overall'] = 'warning';
      }

    } catch (e) {
      health['issues'].add('健康检查异常: $e');
      health['overall'] = 'error';
      health['score'] = 0;
    }

    return health;
  }

  /// 批量检查账号健康状态
  Map<String, Map<String, dynamic>> batchCheckAccountHealth(List<JlzsAccountModel> accounts) {
    Map<String, Map<String, dynamic>> results = {};
    
    for (var account in accounts) {
      results[account.id] = checkAccountHealth(account);
    }
    
    return results;
  }

  /// 生成健康报告摘要
  Map<String, dynamic> generateHealthSummary(List<JlzsAccountModel> accounts) {
    var healthResults = batchCheckAccountHealth(accounts);
    
    int healthyCount = 0;
    int warningCount = 0;
    int unhealthyCount = 0;
    int errorCount = 0;
    
    List<String> commonIssues = [];
    List<String> commonWarnings = [];
    
    for (var health in healthResults.values) {
      switch (health['overall']) {
        case 'healthy':
          healthyCount++;
          break;
        case 'warning':
          warningCount++;
          break;
        case 'unhealthy':
          unhealthyCount++;
          break;
        case 'error':
          errorCount++;
          break;
      }
      
      // 收集常见问题
      for (var issue in health['issues']) {
        if (!commonIssues.contains(issue)) {
          commonIssues.add(issue);
        }
      }
      
      for (var warning in health['warnings']) {
        if (!commonWarnings.contains(warning)) {
          commonWarnings.add(warning);
        }
      }
    }
    
    return {
      'total': accounts.length,
      'healthy': healthyCount,
      'warning': warningCount,
      'unhealthy': unhealthyCount,
      'error': errorCount,
      'healthyRate': accounts.isNotEmpty ? (healthyCount / accounts.length * 100).round() : 0,
      'commonIssues': commonIssues,
      'commonWarnings': commonWarnings,
    };
  }
}
