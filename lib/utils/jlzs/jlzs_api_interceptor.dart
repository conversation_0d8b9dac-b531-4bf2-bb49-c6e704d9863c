import 'dart:async';
import 'package:puppeteer/puppeteer.dart';
import '../../model/jlzs_account_new_model.dart';

/// API拦截器 - 负责拦截和处理巨量指数的API请求
class JlzsApiInterceptor {
  // 账号信息API端点
  static const String _accountInfoApi = '/passport/account/info/v2/';
  // 热度趋势API端点
  static const String _hotTrendApi = '/api/v2/index/get_multi_keyword_hot_trend';
  // 关键词解读API端点
  static const String _keywordInterpretationApi = '/api/v2/index/get_multi_keyword_interpretation';

  // 用于跟踪已设置拦截的浏览器
  final Set<String> _interceptedBrowsers = {};
  // 用于跟踪已设置拦截的页面
  final Set<String> _interceptedPages = {};

  /// 设置浏览器级别的API拦截
  Future<void> setupBrowserInterception(
    Browser browser,
    JlzsAccountModel account,
    Function(String, {String? error})? onStatusUpdate,
    Function(JlzsAccountModel) onLoginSuccess,
  ) async {
    String browserId = browser.hashCode.toString();

    // 检查是否已经设置过全局拦截
    if (_interceptedBrowsers.contains(browserId)) {
      print('⚠️ 浏览器已设置全局拦截，跳过重复设置 (ID: $browserId)');
      return;
    }

    print('🔧 设置浏览器级别全局API拦截 (ID: $browserId)...');
    _interceptedBrowsers.add(browserId);

    // 监听浏览器的所有页面创建 - 对所有目标网站页面设置拦截
    browser.onTargetCreated.listen((target) async {
      if (target.type == 'page') {
        try {
          print('📄 检测到新页面创建...');
          var page = await target.page;
          if (page != null) {
            String? url = page.url;
            print('📄 新页面URL: $url');

            // 对所有目标网站页面设置拦截（不再限制页面类型）
            if (url != null && url.contains('trendinsight.oceanengine.com')) {
              print('🎯 目标网站页面，设置API拦截: $url');
              await _setupPageInterception(page, account, onStatusUpdate, onLoginSuccess);
              print('✅ 新页面API拦截设置完成');
            } else {
              print('⚠️ 非目标网站，跳过拦截: $url');
            }
          }
        } catch (e) {
          print('⚠️ 设置新页面API拦截失败: $e');
        }
      }
    });
    
    // 监听页面导航事件
    browser.onTargetChanged.listen((target) async {
      if (target.type == 'page') {
        try {
          var page = await target.page;
          if (page != null) {
            String? url = page.url;
            print('🔄 页面导航检测: $url');

            // 检测抖音OAuth页面是否空白
            if (url != null && url.contains('open.douyin.com/platform/oauth/connect')) {
              print('🔍 检测到抖音OAuth页面，检查是否需要刷新...');
              try {
                // 等待页面加载
                await Future.delayed(Duration(seconds: 2));

                // 检查页面内容
                bool hasContent = await _checkOAuthPageContent(page);
                if (!hasContent) {
                  print('⚠️ 抖音OAuth页面内容为空，尝试刷新...');
                  await page.reload();
                  await Future.delayed(Duration(seconds: 3));

                  hasContent = await _checkOAuthPageContent(page);
                  if (hasContent) {
                    print('✅ 刷新后抖音OAuth页面内容加载完成');
                  } else {
                    print('⚠️ 刷新后抖音OAuth页面仍为空白');
                  }
                } else {
                  print('✅ 抖音OAuth页面内容正常');
                }
              } catch (e) {
                print('⚠️ 检查抖音OAuth页面失败: $e');
              }
            }

            // 对所有目标网站页面重新设置拦截
            if (url != null && url.contains('trendinsight.oceanengine.com')) {
              print('🎯 检测到目标网站页面导航，重新设置API拦截...');
              print('   - URL: $url');
              await _setupPageInterception(page, account, onStatusUpdate, onLoginSuccess);
              print('✅ 页面导航API拦截设置完成');

              // 延迟再次设置，确保页面完全加载后拦截生效
              Future.delayed(Duration(seconds: 2), () async {
                try {
                  await _setupPageInterception(page, account, onStatusUpdate, onLoginSuccess);
                  print('✅ 延迟API拦截设置完成');
                } catch (e) {
                  print('⚠️ 延迟API拦截设置失败: $e');
                }
              });
            }
          }
        } catch (e) {
          print('⚠️ 页面导航拦截设置失败: $e');
        }
      }
    });
    
    // 为现有页面设置拦截（对所有目标网站页面）
    var pages = await browser.pages;
    int interceptedPagesCount = 0;

    for (var page in pages) {
      try {
        String? url = page.url;
        print('📄 检查现有页面: $url');

        // 对所有目标网站页面设置拦截
        if (url != null && url.contains('trendinsight.oceanengine.com')) {
          print('🎯 现有目标网站页面，设置API拦截: $url');
          await _setupPageInterception(page, account, onStatusUpdate, onLoginSuccess);
          interceptedPagesCount++;
          print('✅ 现有页面API拦截设置完成');
        } else {
          print('⚠️ 现有页面不是目标网站，跳过拦截: $url');
        }
      } catch (e) {
        print('⚠️ 设置现有页面API拦截失败: $e');
      }
    }

    if (interceptedPagesCount > 0) {
      print('🎯 浏览器级别API拦截设置完成，已拦截 $interceptedPagesCount 个页面');
    } else {
      print('📝 浏览器级别API拦截初始化完成，当前无需拦截的页面');
    }
    
    // 启动主动检测机制
    _startActiveMonitoring(browser, account, onStatusUpdate, onLoginSuccess);
  }

  /// 设置单个页面的API拦截
  Future<void> _setupPageInterception(
    Page page, 
    JlzsAccountModel account,
    Function(String, {String? error})? onStatusUpdate,
    Function(JlzsAccountModel) onLoginSuccess,
  ) async {
    try {
      String? pageUrl = page.url;
      String pageId = page.hashCode.toString();
      
      print('🔧 为页面设置API拦截: $pageUrl (ID: $pageId)');
      
      // 检查是否已经设置过拦截，避免重复设置
      if (_interceptedPages.contains(pageId)) {
        print('⚠️ 页面已设置拦截，跳过重复设置 (ID: $pageId)');
        return;
      }
      
      await page.setRequestInterception(true);
      _interceptedPages.add(pageId);
      print('✅ 页面请求拦截已启用 (ID: $pageId)');
      
      // 拦截请求
      page.onRequest.listen((request) async {
        String url = request.url;

        // 允许所有请求通过，但记录关键请求
        if (url.contains(_accountInfoApi)) {
          print('🎯 检测到账号信息API请求: $url');
          print('📍 请求来源页面: $pageUrl');
          print('🔧 请求方法: ${request.method}');

          // 打印请求头信息
          print("📤 请求Headers:");
          try {
            final headers = request.headers;
            if (headers != null && headers.isNotEmpty) {
              print("   📊 Headers总数: ${headers.length}");
              headers.forEach((key, value) {
                print("   $key: $value");
              });
            } else {
              print("   (无请求头或请求头为空)");
            }
          } catch (e) {
            print("   ❌ 获取请求头失败: $e");
            print("   🔍 Request对象类型: ${request.runtimeType}");
          }
        }

        // 拦截热度趋势API请求
        if (url.contains(_hotTrendApi)) {
          print('🔥 检测到热度趋势API请求: $url');
          print('📍 请求来源页面: $pageUrl');
          print('🔧 请求方法: ${request.method}');

          // 打印请求头信息
          print("📤 热度趋势API请求Headers:");
          try {
            final headers = request.headers;
            if (headers != null && headers.isNotEmpty) {
              print("   📊 Headers总数: ${headers.length}");
              headers.forEach((key, value) {
                print("   $key: $value");
              });
            } else {
              print("   (无请求头或请求头为空)");
            }
          } catch (e) {
            print("   ❌ 获取请求头失败: $e");
            print("   🔍 Request对象类型: ${request.runtimeType}");
          }
        }

        // 拦截关键词解读API请求
        if (url.contains(_keywordInterpretationApi)) {
          print('📖 检测到关键词解读API请求: $url');
          print('📍 请求来源页面: $pageUrl');
          print('🔧 请求方法: ${request.method}');

          // 打印请求头信息
          print("📤 关键词解读API请求Headers:");
          try {
            final headers = request.headers;
            if (headers != null && headers.isNotEmpty) {
              print("   📊 Headers总数: ${headers.length}");
              headers.forEach((key, value) {
                print("   $key: $value");
              });
            } else {
              print("   (无请求头或请求头为空)");
            }
          } catch (e) {
            print("   ❌ 获取请求头失败: $e");
            print("   🔍 Request对象类型: ${request.runtimeType}");
          }
        }

        try {
          await request.continueRequest();
        } catch (e) {
          // 如果请求已经被处理，忽略错误
          if (!e.toString().contains('Request is already handled')) {
            print('⚠️ 请求处理异常: $e');
          }
        }
      });
      
      // 拦截响应
      page.onResponse.listen((response) async {
        String url = response.url;
        
        // 只记录关键响应，避免日志刷屏
        if (url.contains(_accountInfoApi)) {
          print("🎯 拦截到账号信息API响应: $url");
          print("📍 响应状态: ${response.status}");

          // 打印响应头信息
          print("📥 响应Headers:");
          try {
            // 尝试多种方式获取响应头
            Map<String, String>? headers;

            // 方法1：直接访问headers属性
            try {
              headers = response.headers;
            } catch (e1) {
              print("   ⚠️ 方法1失败: $e1");

              // 方法2：尝试其他可能的属性名
              try {
                // 有些版本可能使用不同的属性名
                final responseHeaders = response.toString();
                print("   📄 Response对象信息: $responseHeaders");
              } catch (e2) {
                print("   ⚠️ 方法2失败: $e2");
              }
            }

            if (headers != null && headers.isNotEmpty) {
              print("   📊 Headers总数: ${headers.length}");
              headers.forEach((key, value) {
                print("   $key: $value");
              });
            } else {
              print("   (无响应头或响应头为空)");

              // 尝试获取一些常见的单个header
              try {
                final contentType = response.headers['content-type'];
                final setCookie = response.headers['set-cookie'];
                print("   🔍 尝试获取常见headers:");
                print("   content-type: $contentType");
                print("   set-cookie: $setCookie");
              } catch (e) {
                print("   ❌ 获取常见headers也失败: $e");
              }
            }
          } catch (e) {
            print("   ❌ 获取响应头失败: $e");
            print("   🔍 Response对象类型: ${response.runtimeType}");
          }
        }

        // 拦截热度趋势API响应
        if (url.contains(_hotTrendApi)) {
          print("🔥 拦截到热度趋势API响应: $url");
          print("📍 响应状态: ${response.status}");

          if (response.status == 200) {
            // 提取cookies
            await _extractCookiesFromResponse(page, url, account);
          }

          // 打印响应头信息
          print("📥 热度趋势API响应Headers:");
          try {
            // 尝试多种方式获取响应头
            Map<String, String>? headers;

            // 方法1：直接访问headers属性
            try {
              headers = response.headers;
            } catch (e1) {
              print("   ⚠️ 方法1失败: $e1");

              // 方法2：尝试其他可能的属性名
              try {
                // 有些版本可能使用不同的属性名
                final responseHeaders = response.toString();
                print("   📄 Response对象信息: $responseHeaders");
              } catch (e2) {
                print("   ⚠️ 方法2失败: $e2");
              }
            }

            if (headers != null && headers.isNotEmpty) {
              print("   📊 Headers总数: ${headers.length}");
              headers.forEach((key, value) {
                print("   $key: $value");
              });
            } else {
              print("   (无响应头或响应头为空)");

              // 尝试获取一些常见的单个header
              try {
                final contentType = response.headers['content-type'];
                final setCookie = response.headers['set-cookie'];
                print("   🔍 尝试获取常见headers:");
                print("   content-type: $contentType");
                print("   set-cookie: $setCookie");
              } catch (e) {
                print("   ❌ 获取常见headers也失败: $e");
              }
            }
          } catch (e) {
            print("   ❌ 获取响应头失败: $e");
            print("   🔍 Response对象类型: ${response.runtimeType}");
          }
        }

        // 拦截关键词解读API响应
        if (url.contains(_keywordInterpretationApi)) {
          print("📖 拦截到关键词解读API响应: $url");
          print("📍 响应状态: ${response.status}");

          if (response.status == 200) {
            // 提取cookies
            await _extractCookiesFromResponse(page, url, account);
          }

          // 打印响应头信息
          print("📥 关键词解读API响应Headers:");
          try {
            // 尝试多种方式获取响应头
            Map<String, String>? headers;

            // 方法1：直接访问headers属性
            try {
              headers = response.headers;
            } catch (e1) {
              print("   ⚠️ 方法1失败: $e1");

              // 方法2：尝试其他可能的属性名
              try {
                // 有些版本可能使用不同的属性名
                final responseHeaders = response.toString();
                print("   📄 Response对象信息: $responseHeaders");
              } catch (e2) {
                print("   ⚠️ 方法2失败: $e2");
              }
            }

            if (headers != null && headers.isNotEmpty) {
              print("   📊 Headers总数: ${headers.length}");
              headers.forEach((key, value) {
                print("   $key: $value");
              });
            } else {
              print("   (无响应头或响应头为空)");

              // 尝试获取一些常见的单个header
              try {
                final contentType = response.headers['content-type'];
                final setCookie = response.headers['set-cookie'];
                print("   🔍 尝试获取常见headers:");
                print("   content-type: $contentType");
                print("   set-cookie: $setCookie");
              } catch (e) {
                print("   ❌ 获取常见headers也失败: $e");
              }
            }
          } catch (e) {
            print("   ❌ 获取响应头失败: $e");
            print("   🔍 Response对象类型: ${response.runtimeType}");
          }
        }

        // 拦截账号信息API响应
        if (url.contains(_accountInfoApi) && response.status == 200) {
          try {
            var responseData = await response.json;
            await _handleAccountInfoResponse(account, responseData, onStatusUpdate, onLoginSuccess);
          } catch (e) {
            print('❌ 处理账号信息API响应失败: $e');
          }
        }
      });
    } catch (e) {
      print('⚠️ 设置API拦截失败: $e');
    }
  }

  /// 处理账号信息API响应
  Future<void> _handleAccountInfoResponse(
    JlzsAccountModel account,
    Map<String, dynamic> responseData,
    Function(String, {String? error})? onStatusUpdate,
    Function(JlzsAccountModel) onLoginSuccess,
  ) async {
    // 打印完整的响应数据用于调试
    print('🔍 账号信息API完整响应数据:');
    print('   响应内容: $responseData');
    print('   message字段: ${responseData['message']}');
    print('   data字段: ${responseData['data']}');

    // 检查响应是否成功
    if (responseData['message'] == 'success' && responseData['data'] != null) {
      onStatusUpdate?.call('检测到登录成功，正在提取账号信息...');

      // 更新账号信息
      await _updateAccountInfo(account, responseData);

      onStatusUpdate?.call('账号信息提取完成');

      // 通知登录成功
      onLoginSuccess(account);
    } else {
      print('❌ 账号信息API响应异常:');
      print('   message: ${responseData['message']}');
      print('   data: ${responseData['data']}');
      print('   完整响应: $responseData');

      // 如果是认证问题，提示用户
      if (responseData['message'] == 'error') {
        onStatusUpdate?.call('认证失败，可能需要重新登录', error: '账号信息获取失败');
      }
    }
  }

  /// 更新账号信息
  Future<void> _updateAccountInfo(JlzsAccountModel account, Map<String, dynamic> responseData) async {
    try {
      var data = responseData['data'];
      
      // 创建或更新session
      if (account.session == null) {
        account.session = JlzsSession(account.id);
      }
      
      // 更新用户信息
      account.session!.userInfo = {
        'user_id': data['user_id'] ?? 0,
        'sec_user_id': data['sec_user_id'] ?? '',
        'nickname': data['nickname'] ?? '',
        'mobile': data['mobile'] ?? '',
        'email': data['email'] ?? '',
        'avatar_url': data['avatar_url'] ?? '',
        'company_name': data['company_name'] ?? '',
        'connected_platforms': data['connected_platforms'] ?? [],
      };
      
      // 更新手机号（如果可用）
      if (data['mobile'] != null && data['mobile'].toString().isNotEmpty) {
        account.mobile = data['mobile'].toString();
      }
      
      // 标记session为活跃
      account.session!.isActive = true;
      account.session!.lastActive = DateTime.now();
      
      print('✅ 账号信息更新完成');
      print('   - 用户ID: ${account.session!.userInfo['user_id']}');
      print('   - 昵称: ${account.session!.userInfo['nickname']}');
      print('   - 手机号: ${account.maskedMobile}');
    } catch (e) {
      print('❌ 更新账号信息失败: $e');
    }
  }

  /// 启动主动拦截监控
  void _startActiveMonitoring(
    Browser browser,
    JlzsAccountModel account,
    Function(String, {String? error})? onStatusUpdate,
    Function(JlzsAccountModel) onLoginSuccess,
  ) {
    print('🔄 启动主动拦截监控...');
    
    // 每5秒检查一次页面状态
    Timer.periodic(Duration(seconds: 5), (timer) async {
      try {
        // 检查账号是否已经登录成功，如果是则停止监控
        if (account.status == AccountStatus.active) {
          print('✅ 账号已登录成功，停止拦截监控');
          timer.cancel();
          return;
        }
        
        var pages = await browser.pages;
        for (var page in pages) {
          if (!page.isClosed) {
            String? url = page.url;
            
            // 对所有目标网站页面重新设置拦截
            if (url != null && url.contains('trendinsight.oceanengine.com')) {
              print('🔄 主动检测到目标网站页面，重新设置API拦截: $url');
              try {
                await _setupPageInterception(page, account, onStatusUpdate, onLoginSuccess);
                print('✅ 主动重新设置API拦截完成');
              } catch (e) {
                print('⚠️ 主动重新设置API拦截失败: $e');
              }
            }
          }
        }
      } catch (e) {
        print('⚠️ 主动拦截监控异常: $e');
      }
    });
  }

  /// 检查抖音OAuth页面内容
  Future<bool> _checkOAuthPageContent(Page page) async {
    try {
      // 检查页面实际渲染内容
      var pageContent = await page.evaluate('''
        function() {
          var body = document.body;
          if (!body) return { hasContent: false, reason: 'no body element' };

          // 检查root元素是否有实际内容
          var root = document.getElementById('root');
          var rootHasContent = false;
          var rootInfo = '';

          if (root) {
            var rootChildren = root.children.length;
            var rootText = (root.innerText || '').trim();
            rootHasContent = rootChildren > 0 && rootText.length > 10;
            rootInfo = 'root元素: ' + rootChildren + '个子元素, ' + rootText.length + '字符文本';
          } else {
            rootInfo = '未找到root元素';
          }

          // 检查是否有OAuth相关的可见元素
          var oauthElements = document.querySelectorAll('button, [class*="qr"], [class*="code"], [class*="scan"], [class*="auth"]');
          var hasOAuthElements = oauthElements.length > 0;

          // 检查页面是否真正渲染了内容
          var visibleText = body.innerText || body.textContent || '';
          var hasRealContent = visibleText.length > 50 && (
            visibleText.includes('扫码') ||
            visibleText.includes('授权') ||
            visibleText.includes('登录') ||
            visibleText.includes('确认') ||
            visibleText.includes('同意')
          );

          return {
            hasContent: rootHasContent && hasOAuthElements && hasRealContent,
            textLength: visibleText.length,
            rootInfo: rootInfo,
            oauthElementsCount: oauthElements.length,
            hasRealContent: hasRealContent,
            reason: !rootHasContent ? 'root元素无内容' :
                   (!hasOAuthElements ? '无OAuth元素' :
                   (!hasRealContent ? '无实际内容' : 'has content'))
          };
        }
      ''');

      print('📊 抖音OAuth页面内容检查: $pageContent');

      if (pageContent['hasContent'] == true) {
        return true;
      } else {
        print('⚠️ 抖音OAuth页面内容为空: ${pageContent['reason']}');
        return false;
      }
    } catch (e) {
      print('⚠️ 检查抖音OAuth页面内容失败: $e');
      return false;
    }
  }

  /// 简化的页面级API拦截（参考百度指数）
  Future<void> setupPageInterception(
    Page page,
    JlzsAccountModel account,
    Function(String, {String? error})? onStatusUpdate,
    Function(JlzsAccountModel) onLoginSuccess,
  ) async {
    try {
      print('🔧 设置页面级API拦截（简化版本）...');

      // 启用请求拦截
      await page.setRequestInterception(true);

      // 监听请求
      page.onRequest.listen((request) async {
        // 放行所有请求
        await request.continueRequest();
      });

      // 监听响应
      page.onResponse.listen((response) async {
        String url = response.url;

        // 拦截账号信息API响应
        if (url.contains(_accountInfoApi)) {
          print("👤 拦截到账号信息API响应: $url");
          if (response.status == 200) {
            try {
              var responseData = await response.json;
              await _handleAccountInfoResponse(account, responseData, onStatusUpdate, onLoginSuccess);
            } catch (e) {
              print("⚠️ 解析账号信息响应失败: $e");
            }
          }
        }

        // 拦截热度趋势API响应
        if (url.contains(_hotTrendApi)) {
          print("🔥 拦截到热度趋势API响应: $url");
          if (response.status == 200) {
            await _extractCookiesFromResponse(page, url, account);
          }
        }

        // 拦截关键词解读API响应
        if (url.contains(_keywordInterpretationApi)) {
          print("📖 拦截到关键词解读API响应: $url");
          if (response.status == 200) {
            await _extractCookiesFromResponse(page, url, account);
          }
        }
      });

      print('✅ 页面级API拦截设置完成');

    } catch (e) {
      print('❌ 设置页面级API拦截失败: $e');
    }
  }

  /// 从API响应中提取cookies
  Future<void> _extractCookiesFromResponse(Page page, String apiUrl, JlzsAccountModel account) async {
    try {
      print('🍪 开始提取API响应cookies: $apiUrl');

      // 获取特定 URL 的 cookies
      var cookies = await page.cookies(urls: [apiUrl]);
      print('🍪 获取到 ${cookies.length} 个cookies');

      Map<String, String> extractedCookies = {};
      List<String> cookieStrings = [];

      for (var cookie in cookies) {
        print('🍪 Cookie: Name = ${cookie.name}, Value = ${cookie.value}, Domain = ${cookie.domain}, Path = ${cookie.path}');

        extractedCookies[cookie.name] = cookie.value;
        // 拼接成 "name=value" 格式
        cookieStrings.add('${cookie.name}=${cookie.value}');
      }

      if (extractedCookies.isNotEmpty) {
        // 拼接完整的Cookie字符串，用分号和空格分隔
        String fullCookieString = cookieStrings.join('; ');
        print('🍪 完整Cookie字符串: $fullCookieString');

        // 更新账号的cookies信息
        if (account.session != null) {
          // 合并新的cookies到现有cookies中
          account.session!.cookies.addAll(extractedCookies);

          // 保存完整的Cookie字符串到session中
          account.session!.userInfo['fullCookieString'] = fullCookieString;

          print('✅ 账号cookies已更新: ${account.session!.cookies}');
          print('✅ 完整Cookie字符串已保存: $fullCookieString');
        } else {
          print('⚠️ 账号session为空，无法保存cookies');
        }
      } else {
        print('⚠️ 未提取到有效的cookies');
      }

    } catch (e) {
      print('❌ 提取cookies失败: $e');
    }
  }

  /// 清理资源
  void dispose() {
    _interceptedPages.clear();
    _interceptedBrowsers.clear();
  }
}
