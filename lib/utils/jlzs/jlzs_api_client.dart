import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:http/http.dart' as http;
import 'package:http/io_client.dart';
import 'package:encrypt/encrypt.dart' as encrypt;
import 'package:puppeteer/puppeteer.dart';
import '../../model/jlzs_task_data_model.dart';
import '../../model/jlzs_account_new_model.dart';
import '../jlzs_browser_manager.dart';

/// 巨量指数API客户端
class JlzsApiClient {
  static const String API_BASE_URL = 'https://trendinsight.oceanengine.com';
  static const String API_ENDPOINT = '/api/v2/index/get_multi_keyword_hot_trend';
  
  // AES解密配置
  static const String AES_KEY = 'SjXbYTJb7zXoUToSicUL3A==';
  static const String AES_IV = 'OekMLjghRg8vlX/PemLc+Q==';
  
  /// 执行API请求任务
  static Future<Map<String, dynamic>> executeTask(
    JlzsApiTaskData task,
    JlzsAccountModel account,
  ) async {
    try {
      // 构建请求
      final request = await _buildRequest(task, account);
      
      // 发送请求
      final response = await _sendRequest(request, account);
      
      // 处理响应
      final result = await _processResponse(response);
      
      return {
        'success': true,
        'data': result,
        'task_id': task.id,
      };
    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
        'task_id': task.id,
      };
    }
  }

  /// 构建HTTP请求
  static Future<http.Request> _buildRequest(
    JlzsApiTaskData task,
    JlzsAccountModel account,
  ) async {
    final uri = Uri.parse('$API_BASE_URL$API_ENDPOINT');
    final request = http.Request('POST', uri);
    
    // 设置请求头
    request.headers.addAll({
      'Content-Type': 'application/json',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      'Referer': 'https://trendinsight.oceanengine.com/',
      'Origin': 'https://trendinsight.oceanengine.com',
    });
    
    // 添加认证信息 - 通过浏览器实例动态获取cookie
    try {
      // 优先使用保存的完整Cookie字符串
      if (account.session != null && account.session!.userInfo.containsKey('fullCookieString')) {
        final fullCookieString = account.session!.userInfo['fullCookieString'] as String?;
        if (fullCookieString != null && fullCookieString.isNotEmpty) {
          request.headers['Cookie'] = fullCookieString;
          print('✅ 使用保存的完整Cookie字符串: $fullCookieString');

          // 从完整Cookie字符串中提取msToken
          String? msToken = JlzsApiClient._extractMsTokenFromCookieString(fullCookieString);
          if (msToken != null && msToken.isNotEmpty) {
            request.headers['msToken'] = msToken;
            print('✅ 从Cookie字符串提取msToken: $msToken');
          }
        } else {
          print('⚠️ 完整Cookie字符串为空，尝试其他方式');
          await JlzsApiClient._fallbackToCookieData(request, account);
        }
      } else {
        print('⚠️ 未找到保存的完整Cookie字符串，尝试其他方式');
        await JlzsApiClient._fallbackToCookieData(request, account);
      }
    } catch (e) {
      print('❌ 获取实时Cookie失败: $e，使用存储的session数据');
      // 备用方案：使用存储的session数据
      if (account.session != null) {
        String cookieString = JlzsApiClient._buildCookieString(account.session!);
        request.headers['Cookie'] = cookieString;
        print('🔄 使用备用Cookie: $cookieString');

        String? msToken = JlzsApiClient._extractMsToken(account.session!);
        if (msToken != null) {
          request.headers['msToken'] = msToken;
        }
      }
    }
    
    // 构建请求体
    final requestBody = task.buildRequestBody();
    request.body = json.encode(requestBody);
    
    return request;
  }

  /// 发送HTTP请求（支持代理）
  static Future<http.Response> _sendRequest(
    http.Request request,
    JlzsAccountModel account,
  ) async {
    http.Client client;
    
    // 如果配置了代理，使用代理客户端
    if (account.proxy != null) {
      client = _createProxyClient(account.proxy!);
    } else {
      client = http.Client();
    }
    
    try {
      final streamedResponse = await client.send(request);
      final response = await http.Response.fromStream(streamedResponse);
      return response;
    } finally {
      client.close();
    }
  }

  /// 创建代理HTTP客户端
  static http.Client _createProxyClient(JlzsProxyConfig proxy) {
    final httpClient = HttpClient();
    
    // 设置代理
    httpClient.findProxy = (uri) {
      return 'PROXY ${proxy.address}:${proxy.port}';
    };
    
    // 如果有认证信息，设置代理认证
    if (proxy.username != null && proxy.password != null) {
      httpClient.addProxyCredentials(
        proxy.address,
        proxy.port,
        'realm',
        HttpClientBasicCredentials(proxy.username!, proxy.password!),
      );
    }
    
    return IOClient(httpClient);
  }

  /// 处理API响应
  static Future<Map<String, dynamic>> _processResponse(http.Response response) async {
    if (response.statusCode != 200) {
      throw Exception('API请求失败: ${response.statusCode} ${response.reasonPhrase}');
    }
    
    try {
      final responseData = json.decode(response.body);
      
      // 检查响应格式
      if (responseData['code'] != null && responseData['code'] != 0) {
        throw Exception('API返回错误: ${responseData['message'] ?? '未知错误'}');
      }
      
      // 解密数据（如果需要）
      if (responseData['data'] is String) {

        print(responseData['data']);
        final decryptedData = _decryptResponseData(responseData['data']);
        responseData['data'] = decryptedData;
      }
      
      return responseData;
    } catch (e) {
      throw Exception('解析响应数据失败: $e');
    }
  }

  /// 解密响应数据（完全匹配Python版本的实现）
  static Map<String, dynamic> _decryptResponseData(String encryptedData) {
    try {
      print('🔐 开始解密数据，原始长度: ${encryptedData}');

      // 完全按照Python版本的步骤
      // 1. base64解码key和iv
      final keyBytes = base64.decode(AES_KEY);
      final ivBytes = base64.decode(AES_IV);
      print('🔐 Key长度: ${keyBytes.length}, IV长度: ${ivBytes.length}');

      // 2. base64解码加密数据
      final encryptedBytes = base64.decode(encryptedData);
      print('🔐 加密数据字节长度: ${encryptedBytes.length}');

      // 3. 使用encrypt包但禁用自动填充处理
      final key = encrypt.Key(keyBytes);
      final iv = encrypt.IV(ivBytes);

      // 创建AES加密器
      final encrypter = encrypt.Encrypter(encrypt.AES(key, mode: encrypt.AESMode.cbc));

      // 4. 解密
      final encrypted = encrypt.Encrypted(encryptedBytes);
      final decryptedBytes = encrypter.decryptBytes(encrypted, iv: iv);
      print('🔐 解密后字节长度: ${decryptedBytes.length}');

      // 5. 转换为UTF-8字符串
      String decryptedString = utf8.decode(decryptedBytes, allowMalformed: true);
      print('🔐 解密后原始字符串长度: ${decryptedString.length}');

      // 6. 去除控制字符（完全匹配Python的正则表达式）
      // Python: re.sub(r"\f|[\x00-\x1F\x7F-\x9F]", "", decryptedData.decode("utf-8"))
      String cleanedString = decryptedString.replaceAll(RegExp(r'[\x00-\x1F\x7F-\x9F]'), '');
      print('🔐 清理后字符串: ${cleanedString}');
      // print('🔐 清理后内容前100字符: ${cleanedString.length > 100 ? cleanedString.substring(0, 100) : cleanedString}');

      return json.decode(cleanedString);
    } catch (e) {
      print('❌ 解密失败详情: $e');
      print('❌ 错误类型: ${e.runtimeType}');
      print('❌ 加密数据前50字符: ${encryptedData.length > 50 ? encryptedData.substring(0, 50) : encryptedData}...');

      // 尝试备用解密方法
      try {
        print('🔄 尝试备用解密方法...');
        return _decryptResponseDataFallback(encryptedData);
      } catch (fallbackError) {
        print('❌ 备用方法也失败: $fallbackError');
        throw Exception('数据解密失败: $e');
      }
    }
  }

  /// 备用解密方法（使用标准填充）
  static Map<String, dynamic> _decryptResponseDataFallback(String encryptedData) {
    try {
      print('🔄 使用标准AES-CBC解密...');

      final key = encrypt.Key.fromBase64(AES_KEY);
      final iv = encrypt.IV.fromBase64(AES_IV);
      final encrypter = encrypt.Encrypter(encrypt.AES(key));

      final encrypted = encrypt.Encrypted.fromBase64(encryptedData);
      final decrypted = encrypter.decrypt(encrypted, iv: iv);

      // 去除控制字符
      String cleanedString = decrypted.replaceAll(RegExp(r'[\x00-\x1F\x7F-\x9F]'), '');
      print('🔄 备用方法清理后内容: $cleanedString');

      return json.decode(cleanedString);
    } catch (e) {
      throw Exception('备用解密方法失败: $e');
    }
  }

  /// 构建Cookie字符串
  static String _buildCookieString(JlzsSession session) {
    List<String> cookies = [];
    print(session.cookies);
    session.cookies.forEach((name, value) {
      cookies.add('$name=$value');
    });
    
    return cookies.join('; ');
  }

  /// 从会话中提取msToken
  static String? _extractMsToken(JlzsSession session) {
    return session.cookies['msToken'];
  }

  /// 验证账号可用性
  static Future<bool> validateAccount(JlzsAccountModel account) async {
    try {
      // 创建测试任务
      final testTask = JlzsApiTaskData(
        id: 'test_${DateTime.now().millisecondsSinceEpoch}',
        keywords: ['测试'],
        regions: ['全国'],
        startDate: _formatDate(DateTime.now().subtract(Duration(days: 1))),
        endDate: _formatDate(DateTime.now()),
        platform: 'aweme',
        regionType: 'separate',
      );
      
      // 执行测试请求
      final result = await executeTask(testTask, account);
      
      return result['success'] == true;
    } catch (e) {
      print('账号验证失败: $e');
      return false;
    }
  }

  /// 格式化日期为YYYYMMDD
  static String _formatDate(DateTime date) {
    return '${date.year}${date.month.toString().padLeft(2, '0')}${date.day.toString().padLeft(2, '0')}';
  }

  /// 通过浏览器实例获取实时Cookie和msToken
  static Future<Map<String, String>?> _getCookieFromBrowser(JlzsAccountModel account) async {
    try {
      // 获取浏览器管理器实例
      final browserManager = JlzsBrowserManager();

      // 获取账号对应的浏览器实例
      final browser = browserManager.getBrowserByAccountId(account.id);
      if (browser == null) {
        print('⚠️ 账号 ${account.maskedMobile} 没有对应的浏览器实例');
        return null;
      }

      // 获取浏览器页面
      final pages = await browser.pages;
      if (pages.isEmpty) {
        print('⚠️ 浏览器没有可用页面');
        return null;
      }

      // 查找正确的页面（任何巨量指数页面都可以）
      Page? targetPage;
      print('🔍 查找可用页面，总页面数: ${pages.length}');

      for (int i = 0; i < pages.length; i++) {
        final page = pages[i];
        if (!page.isClosed) {
          final url = page.url;
          print('📄 页面 $i: URL=$url, isClosed=${page.isClosed}');

          if (url != null && url.contains('trendinsight.oceanengine.com')) {
            // 任何巨量指数页面都可以用来获取cookie
            targetPage = page;
            print('✅ 找到可用页面: $url');
            break;
          }
        } else {
          print('! 页面 $i 已关闭: ${page.url}');
        }
      }

      if (targetPage == null) {
        print('⚠️ 未找到任何巨量指数页面');
        // 尝试使用第一个未关闭的页面
        for (final page in pages) {
          if (!page.isClosed) {
            targetPage = page;
            print('🔄 使用备用页面: ${page.url}');
            break;
          }
        }

        if (targetPage == null) {
          print('❌ 所有页面都已关闭');
          return null;
        }
      }

      // 方法1：使用Puppeteer API获取所有cookie（包括HttpOnly）
      try {
        print('🍪 使用Puppeteer API获取所有cookie...');
        final cookies = await targetPage.cookies();

        if (cookies.isNotEmpty) {
          // 构建完整的cookie字符串
          final cookiePairs = <String>[];
          String? msToken;

          for (final cookie in cookies) {
            // 添加到cookie字符串
            cookiePairs.add('${cookie.name}=${cookie.value}');

            // 提取msToken
            if (cookie.name == 'msToken') {
              msToken = cookie.value;
            }
          }

          final cookieString = cookiePairs.join('; ');

          print('✅ 成功获取所有Cookie，总数: ${cookies.length}，长度: ${cookieString.length}');
          if (msToken != null && msToken.isNotEmpty) {
            print('✅ 成功获取msToken: ${msToken.substring(0, 10)}...');
          }

          return {
            'cookieString': cookieString,
            'msToken': msToken ?? '',
          };
        }
      } catch (puppeteerError) {
        print('⚠️ Puppeteer API获取失败: $puppeteerError，尝试JavaScript方式');
      }

      // 方法2：备用JavaScript方式（只能获取非HttpOnly cookie）
      try {
        print('🍪 使用JavaScript document.cookie作为备用...');
        final result = await targetPage.evaluate('''
          (function() {
            try {
              // 获取document.cookie（仅非HttpOnly）
              var cookieString = document.cookie;

              // 解析msToken
              var msToken = null;
              var cookies = cookieString.split(';');
              for (var i = 0; i < cookies.length; i++) {
                var cookie = cookies[i].trim();
                if (cookie.startsWith('msToken=')) {
                  msToken = cookie.substring('msToken='.length);
                  break;
                }
              }

              return {
                success: true,
                cookieString: cookieString,
                msToken: msToken
              };
            } catch(e) {
              return {
                success: false,
                error: e.toString()
              };
            }
          })();
        ''');

        if (result['success'] == true) {
          final cookieString = result['cookieString']?.toString();
          final msToken = result['msToken']?.toString();

          if (cookieString != null && cookieString.isNotEmpty) {
            print('✅ JavaScript备用方式获取Cookie，长度: ${cookieString.length}');
            if (msToken != null && msToken.isNotEmpty) {
              print('✅ JavaScript方式获取msToken: ${msToken.substring(0, 10)}...');
            }

            return {
              'cookieString': cookieString,
              'msToken': msToken ?? '',
            };
          }
        } else {
          print('❌ JavaScript执行失败: ${result['error']}');
        }
      } catch (jsError) {
        print('❌ JavaScript方式也失败: $jsError');
      }

      return null;
    } catch (e) {
      print('❌ 获取实时Cookie异常: $e');
      return null;
    }
  }

  /// 从Cookie字符串中提取msToken
  static String? _extractMsTokenFromCookieString(String cookieString) {
    try {
      // 分割Cookie字符串，查找msToken
      final cookies = cookieString.split(';');
      for (final cookie in cookies) {
        final trimmedCookie = cookie.trim();
        if (trimmedCookie.startsWith('msToken=')) {
          final msToken = trimmedCookie.substring('msToken='.length);
          return msToken.isNotEmpty ? msToken : null;
        }
      }
      return null;
    } catch (e) {
      print('❌ 从Cookie字符串提取msToken失败: $e');
      return null;
    }
  }

  /// 备用方案：使用其他方式获取Cookie
  static Future<void> _fallbackToCookieData(http.Request request, JlzsAccountModel account) async {
    try {
      final cookieData = await JlzsApiClient._getCookieFromBrowser(account);
      if (cookieData != null) {
        // 添加Cookie
        final cookieString = cookieData['cookieString'];
        if (cookieString != null) {
          request.headers['Cookie'] = cookieString;
          print('✅ 使用实时Cookie: $cookieString');
        }

        // 添加msToken
        final msToken = cookieData['msToken'];
        if (msToken != null && msToken.isNotEmpty) {
          request.headers['msToken'] = msToken;
          print('✅ 使用实时msToken: $msToken');
        }
      } else {
        print('⚠️ 无法获取实时Cookie，使用存储的session数据作为备用');
        // 最后备用方案：使用存储的session数据
        if (account.session != null) {
          String cookieString = JlzsApiClient._buildCookieString(account.session!);
          request.headers['Cookie'] = cookieString;
          print('🔄 使用备用Cookie: $cookieString');

          String? msToken = JlzsApiClient._extractMsToken(account.session!);
          if (msToken != null) {
            request.headers['msToken'] = msToken;
          }
        }
      }
    } catch (e) {
      print('❌ 备用Cookie获取失败: $e');
    }
  }

  /// 批量执行任务
  static Future<List<Map<String, dynamic>>> executeBatchTasks(
    List<JlzsApiTaskData> tasks,
    List<JlzsAccountModel> accounts,
    {Function(String)? onProgress}
  ) async {
    List<Map<String, dynamic>> results = [];
    
    if (accounts.isEmpty) {
      throw Exception('没有可用账号');
    }
    
    // 将任务分配给账号
    for (int i = 0; i < tasks.length; i++) {
      final task = tasks[i];
      final account = accounts[i % accounts.length]; // 轮询分配
      
      onProgress?.call('执行任务 ${i + 1}/${tasks.length}: ${task.description}');
      
      final result = await executeTask(task, account);
      results.add(result);
      
      // 如果不是最后一个任务，等待间隔
      if (i < tasks.length - 1) {
        await Future.delayed(Duration(seconds: 2)); // 默认2秒间隔
      }
    }
    
    return results;
  }
}
