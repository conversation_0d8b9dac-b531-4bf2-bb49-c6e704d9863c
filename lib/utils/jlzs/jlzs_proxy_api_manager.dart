import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:http/io_client.dart';
import '../../model/jlzs_account_new_model.dart';

/// 巨量指数代理API管理器
class JlzsProxyApiManager {
  static const String DEFAULT_API_BASE_URL = 'https://api.proxy-service.com'; // 示例API地址
  
  final String apiBaseUrl;
  final String? apiKey;
  final String? apiSecret;
  
  JlzsProxyApiManager({
    this.apiBaseUrl = DEFAULT_API_BASE_URL,
    this.apiKey,
    this.apiSecret,
  });

  /// 获取新代理
  Future<JlzsProxyConfig> getNewProxy({
    String? region,
    int? duration, // 使用时长（分钟）
  }) async {
    try {
      final response = await http.post(
        Uri.parse('$apiBaseUrl/proxy/create'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $apiKey',
        },
        body: json.encode({
          'region': region ?? 'CN',
          'duration': duration ?? 60, // 默认60分钟
          'protocol': 'http',
        }),
      );

      if (response.statusCode != 200) {
        throw Exception('获取代理失败: ${response.statusCode} ${response.reasonPhrase}');
      }

      final data = json.decode(response.body);
      
      if (data['code'] != 0) {
        throw Exception('API返回错误: ${data['message']}');
      }

      final proxyData = data['data'];
      
      return JlzsProxyConfig(
        address: proxyData['host'],
        port: proxyData['port'],
        username: proxyData['username'],
        password: proxyData['password'],
        validTime: duration ?? 60,
        startTime: DateTime.now(),
      );
    } catch (e) {
      throw Exception('获取代理失败: $e');
    }
  }

  /// 续期代理
  Future<JlzsProxyConfig> renewProxy(JlzsProxyConfig proxy, {int? additionalMinutes}) async {
    try {
      final response = await http.post(
        Uri.parse('$apiBaseUrl/proxy/renew'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $apiKey',
        },
        body: json.encode({
          'proxy_address': '${proxy.address}:${proxy.port}',
          'additional_minutes': additionalMinutes ?? 60,
        }),
      );

      if (response.statusCode != 200) {
        throw Exception('续期代理失败: ${response.statusCode} ${response.reasonPhrase}');
      }

      final data = json.decode(response.body);
      
      if (data['code'] != 0) {
        throw Exception('API返回错误: ${data['message']}');
      }

      final proxyData = data['data'];
      
      return JlzsProxyConfig(
        address: proxyData['host'],
        port: proxyData['port'],
        username: proxyData['username'],
        password: proxyData['password'],
        validTime: proxyData['valid_time'],
        startTime: DateTime.parse(proxyData['start_time']),
      );
    } catch (e) {
      throw Exception('续期代理失败: $e');
    }
  }

  /// 释放代理
  Future<bool> releaseProxy(JlzsProxyConfig proxy) async {
    try {
      final response = await http.post(
        Uri.parse('$apiBaseUrl/proxy/release'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $apiKey',
        },
        body: json.encode({
          'proxy_address': '${proxy.address}:${proxy.port}',
        }),
      );

      if (response.statusCode != 200) {
        return false;
      }

      final data = json.decode(response.body);
      return data['code'] == 0;
    } catch (e) {
      print('释放代理失败: $e');
      return false;
    }
  }

  /// 验证代理可用性
  Future<bool> validateProxy(JlzsProxyConfig proxy) async {
    try {
      // 创建代理HTTP客户端
      final httpClient = HttpClient();
      httpClient.findProxy = (uri) => 'PROXY ${proxy.address}:${proxy.port}';
      
      // 如果有认证信息，设置代理认证
      if (proxy.username != null && proxy.password != null) {
        httpClient.addProxyCredentials(
          proxy.address,
          proxy.port,
          'realm',
          HttpClientBasicCredentials(proxy.username!, proxy.password!),
        );
      }

      final client = IOClient(httpClient);
      
      try {
        // 测试连接到百度
        final response = await client.get(
          Uri.parse('https://www.baidu.com'),
        ).timeout(Duration(seconds: 10));
        
        return response.statusCode == 200;
      } finally {
        client.close();
        httpClient.close();
      }
    } catch (e) {
      print('代理验证失败: $e');
      return false;
    }
  }

  /// 获取代理状态
  Future<Map<String, dynamic>?> getProxyStatus(JlzsProxyConfig proxy) async {
    try {
      final response = await http.get(
        Uri.parse('$apiBaseUrl/proxy/status/${proxy.address}:${proxy.port}'),
        headers: {
          'Authorization': 'Bearer $apiKey',
        },
      );

      if (response.statusCode != 200) {
        return null;
      }

      final data = json.decode(response.body);
      
      if (data['code'] != 0) {
        return null;
      }

      return data['data'];
    } catch (e) {
      print('获取代理状态失败: $e');
      return null;
    }
  }

  /// 批量获取代理
  Future<List<JlzsProxyConfig>> getBatchProxies(int count, {
    String? region,
    int? duration,
  }) async {
    List<JlzsProxyConfig> proxies = [];
    
    for (int i = 0; i < count; i++) {
      try {
        final proxy = await getNewProxy(region: region, duration: duration);
        proxies.add(proxy);
        
        // 避免请求过于频繁
        if (i < count - 1) {
          await Future.delayed(Duration(milliseconds: 500));
        }
      } catch (e) {
        print('获取第${i + 1}个代理失败: $e');
        // 继续获取下一个
      }
    }
    
    return proxies;
  }

  /// 检查代理是否即将过期
  static bool isProxyExpiringSoon(JlzsProxyConfig proxy, {int warningMinutes = 10}) {
    if (proxy.startTime == null || proxy.validTime == null) return false;

    final expiryTime = proxy.startTime!.add(Duration(minutes: proxy.validTime!));
    final warningTime = expiryTime.subtract(Duration(minutes: warningMinutes));

    return DateTime.now().isAfter(warningTime);
  }

  /// 检查代理是否已过期
  static bool isProxyExpired(JlzsProxyConfig proxy) {
    if (proxy.startTime == null || proxy.validTime == null) return true;

    final expiryTime = proxy.startTime!.add(Duration(minutes: proxy.validTime!));
    return DateTime.now().isAfter(expiryTime);
  }

  /// 获取代理剩余时间（分钟）
  static int getProxyRemainingMinutes(JlzsProxyConfig proxy) {
    if (proxy.startTime == null || proxy.validTime == null) return 0;

    final expiryTime = proxy.startTime!.add(Duration(minutes: proxy.validTime!));
    final remaining = expiryTime.difference(DateTime.now());

    return remaining.inMinutes.clamp(0, proxy.validTime!);
  }

  /// 自动续期代理（如果即将过期）
  Future<JlzsProxyConfig?> autoRenewIfNeeded(JlzsProxyConfig proxy, {
    int warningMinutes = 10,
    int renewMinutes = 60,
  }) async {
    if (!isProxyExpiringSoon(proxy, warningMinutes: warningMinutes)) {
      return null; // 不需要续期
    }
    
    try {
      return await renewProxy(proxy, additionalMinutes: renewMinutes);
    } catch (e) {
      print('自动续期失败: $e');
      return null;
    }
  }
}
