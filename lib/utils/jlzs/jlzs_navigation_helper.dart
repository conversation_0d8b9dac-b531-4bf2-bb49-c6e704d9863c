import 'dart:async';
import 'package:puppeteer/puppeteer.dart';

/// 页面导航助手 - 负责页面导航、状态检测和用户提示
class JlzsNavigationHelper {
  static const String _loginUrl = 'https://trendinsight.oceanengine.com/login';

  /// 检查页面是否有实际内容
  Future<bool> _checkPageContent(Page page) async {
    try {
      // 检查页面标题
      String? title = await page.title;
      print('📄 页面标题: $title');

      // 检查页面实际渲染内容
      var bodyContent = await page.evaluate('''
        function() {
          var body = document.body;
          if (!body) return { hasContent: false, reason: 'no body element' };

          // 检查root元素是否有实际内容
          var root = document.getElementById('root');
          var rootHasContent = false;
          var rootInfo = '';

          if (root) {
            var rootChildren = root.children.length;
            var rootText = (root.innerText || '').trim();
            rootHasContent = rootChildren > 0 && rootText.length > 10;
            rootInfo = 'root元素: ' + rootChildren + '个子元素, ' + rootText.length + '字符文本';
          } else {
            rootInfo = '未找到root元素';
          }

          // 检查是否有登录相关的可见元素
          var loginElements = document.querySelectorAll('input[type="text"], input[type="tel"], input[placeholder*="手机"], [class*="phone"], [class*="login"], button');
          var hasLoginElements = loginElements.length > 0;

          // 检查页面是否真正渲染了内容（不只是JavaScript代码）
          var visibleText = body.innerText || body.textContent || '';
          var hasRealContent = visibleText.length > 100 && (
            visibleText.includes('手机号') ||
            visibleText.includes('登录') ||
            visibleText.includes('验证码') ||
            visibleText.includes('扫码')
          );

          return {
            hasContent: rootHasContent && hasLoginElements && hasRealContent,
            textLength: visibleText.length,
            childrenCount: body.children.length,
            rootInfo: rootInfo,
            loginElementsCount: loginElements.length,
            hasRealContent: hasRealContent,
            reason: !rootHasContent ? 'root元素无内容' :
                   (!hasLoginElements ? '无登录元素' :
                   (!hasRealContent ? '无实际内容' : 'has content'))
          };
        }
      ''');

      print('📊 页面内容检查: $bodyContent');

      if (bodyContent['hasContent'] == true) {
        return true;
      } else {
        print('⚠️ 页面内容为空: ${bodyContent['reason']}');
        return false;
      }
    } catch (e) {
      print('⚠️ 检查页面内容失败: $e');
      return false;
    }
  }

  /// 导航到登录页面
  Future<bool> navigateToLoginPage(
    Page page,
    Function(String, {String? error})? onStatusUpdate,
  ) async {
    print('🔗 登录页面导航到: $_loginUrl');
    bool navigationSuccess = false;

    try {
      // 先检查页面状态
      print('🔍 检查页面初始状态...');
      if (page.isClosed) {
        throw Exception('页面已关闭，无法导航');
      }

      // 检查浏览器连接状态
      print('🔍 检查浏览器连接状态...');
      await page.browser.version.timeout(Duration(seconds: 5));
      print('✅ 浏览器连接正常，开始导航');

      // 等待页面稳定
      await Future.delayed(Duration(seconds: 1));

      // 第一次尝试：简单导航
      try {
        print('🔄 第一次尝试导航...');
        await page.goto(_loginUrl, timeout: Duration(seconds: 30));

        // 等待页面加载并检查内容
        await Future.delayed(Duration(seconds: 3));

        // 检查页面URL
        String? currentUrl = page.url;
        if (currentUrl != null && currentUrl.contains('trendinsight.oceanengine.com')) {
          print('✅ 登录页面URL导航成功: $currentUrl');

          // 检查页面是否真正加载了内容
          bool hasContent = await _checkPageContent(page);
          if (hasContent) {
            navigationSuccess = true;
            print('✅ 登录页面内容加载完成');

            // 注入提示信息
            try {
              await _injectStatusNotification(page, '页面已打开，请完成登录');
            } catch (e) {
              print('⚠️ 注入提示失败: $e');
            }
          } else {
            print('⚠️ 页面URL正确但内容为空，等待更长时间...');
            await Future.delayed(Duration(seconds: 5));

            // 再次检查内容
            hasContent = await _checkPageContent(page);
            if (hasContent) {
              navigationSuccess = true;
              print('✅ 延迟后页面内容加载完成');

              try {
                await _injectStatusNotification(page, '页面已打开，请完成登录');
              } catch (e) {
                print('⚠️ 注入提示失败: $e');
              }
            } else {
              print('⚠️ 页面内容仍为空，可能是React应用未渲染，尝试刷新...');
              // 尝试刷新页面来触发JavaScript执行
              try {
                print('🔄 刷新页面以触发React应用渲染...');
                await page.reload();
                await Future.delayed(Duration(seconds: 5)); // 给React更多时间渲染

                hasContent = await _checkPageContent(page);
                if (hasContent) {
                  navigationSuccess = true;
                  print('✅ 刷新后React应用渲染完成');
                } else {
                  print('⚠️ 刷新后仍无内容，可能是反自动化检测');
                  // 尝试禁用一些自动化特征
                  try {
                    await page.evaluate('''
                      function() {
                        // 移除webdriver标识
                        delete window.navigator.webdriver;
                        delete window.navigator.__webdriver_script_fn;

                        // 模拟真实用户行为
                        Object.defineProperty(navigator, 'webdriver', {
                          get: function() { return false; }
                        });
                      }
                    ''');

                    print('🔄 已尝试移除自动化标识，再次刷新...');
                    await page.reload();
                    await Future.delayed(Duration(seconds: 5));

                    hasContent = await _checkPageContent(page);
                    if (hasContent) {
                      navigationSuccess = true;
                      print('✅ 移除自动化标识后页面渲染成功');
                    }
                  } catch (e) {
                    print('⚠️ 移除自动化标识失败: $e');
                  }
                }
              } catch (e) {
                print('⚠️ 页面刷新失败: $e');
              }
            }
          }
        }

      } catch (navError) {
        print('⚠️ 第一次导航失败: $navError');
        
        // 如果第一次失败，检查是否是连接问题
        if (navError.toString().contains('Session closed') ||
            navError.toString().contains('Connection.dispose') ||
            navError.toString().contains('browser has disconnected')) {
          print('❌ 检测到浏览器连接断开，停止导航尝试');
          throw Exception('Navigation failed because browser has disconnected!');
        }

        // 第二次尝试：等待后重试
        try {
          print('🔄 等待3秒后第二次尝试导航...');
          await Future.delayed(Duration(seconds: 3));

          // 再次检查页面状态
          if (page.isClosed) {
            throw Exception('页面在重试前已关闭');
          }

          await page.goto(_loginUrl, timeout: Duration(seconds: 30));
          await Future.delayed(Duration(seconds: 2));

          String? currentUrl = page.url;
          if (currentUrl != null && currentUrl.contains('trendinsight.oceanengine.com')) {
            navigationSuccess = true;
            print('✅ 第二次导航成功: $currentUrl');

            try {
              await _injectStatusNotification(page, '页面已打开，请完成登录');
            } catch (e) {
              print('⚠️ 注入提示失败: $e');
            }
          }

        } catch (navError2) {
          print('⚠️ 第二次导航也失败: $navError2');
          throw Exception('Navigation failed after 2 attempts: $navError2');
        }
      }
      
    } catch (e) {
      print('⚠️ 登录页面导航异常: $e');
      print('⚠️ 异常类型: ${e.runtimeType}');
      
      // 检查是否是浏览器断开连接的问题
      if (e.toString().contains('browser has disconnected') || 
          e.toString().contains('WebSocket') ||
          e.toString().contains('Connection closed')) {
        print('❌ 检测到浏览器连接断开，这是一个严重问题');
        onStatusUpdate?.call('浏览器连接断开，请检查代理配置', error: '浏览器连接失败');
        throw Exception('浏览器连接断开: $e');
      } else {
        // 其他导航错误不影响登录流程
        print('💡 导航失败但不影响登录，用户可以手动导航到登录页');
        onStatusUpdate?.call('自动导航失败，请手动访问登录页面');
      }
    }
    
    if (navigationSuccess) {
      onStatusUpdate?.call('页面已打开，请完成登录...');
    } else {
      onStatusUpdate?.call('请手动访问登录页面并完成登录...');
    }

    return navigationSuccess;
  }

  /// 快速页面加载检测（检测多个可能的元素，任一成功即可）
  Future<void> _quickPageLoadCheck(Page page) async {
    // 首先验证URL是否正确
    String? currentUrl = page.url;
    print('🔍 当前页面URL: $currentUrl');
    
    if (currentUrl == null || !currentUrl.contains('trendinsight.oceanengine.com')) {
      throw Exception('页面URL不正确: $currentUrl');
    }

    // 定义多个可能的选择器，任一成功即认为页面加载完成
    List<String> selectors = [
      '.account-center-header-sub-title',  // 原始选择器
      '.account-center-header',            // 更通用的选择器
      '.leftContainer-rwASbJ',             // 左侧容器
      '.top-YGVNHB',                       // 顶部元素
      '.topItem-zB9bgo',                   // 顶部项目
      '.bottom-A0PAnM',                    // 底部元素
      'input[type="text"]',                // 手机号输入框
      'input[placeholder*="手机"]',         // 包含"手机"的输入框
      'input[placeholder*="号码"]',         // 包含"号码"的输入框
      'button',                            // 任何按钮
      '.login',                            // 登录相关类名
      'form',                              // 表单元素
      '[class*="login"]',                  // 包含login的类名
      '[class*="account"]',                // 包含account的类名
      '[class*="phone"]',                  // 包含phone的类名
    ];
    
    // 并行检测所有选择器，任一成功即返回
    List<Future> futures = selectors.map((selector) => 
      page.waitForSelector(selector, timeout: Duration(seconds: 2))
        .then((_) => selector)
        .catchError((e) => null)
    ).toList();
    
    // 等待任一选择器成功，最多等待5秒
    try {
      var result = await Future.any(futures.where((f) => f != null))
          .timeout(Duration(seconds: 5));
      print('✅ 快速页面检测成功，检测到元素');
    } catch (e) {
      // 如果所有选择器都失败，检查页面基本状态
      try {
        var readyState = await page.evaluate('document.readyState');
        if (readyState == 'complete') {
          print('✅ 页面基本加载完成');
          return;
        }
      } catch (evalError) {
        print('⚠️ 页面状态检查失败: $evalError');
      }
      throw Exception('页面加载检测失败');
    }
  }

  /// 注入状态提示到页面
  Future<void> _injectStatusNotification(Page page, String message) async {
    try {
      await page.evaluate('''
        (function() {
          // 移除之前的提示
          var existingNotification = document.getElementById('jlzs-status-notification');
          if (existingNotification) {
            existingNotification.remove();
          }
          
          // 创建新的提示元素
          var notification = document.createElement('div');
          notification.id = 'jlzs-status-notification';
          notification.style.cssText = \`
            position: fixed;
            top: 20px;
            right: 20px;
            background: #4CAF50;
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            font-size: 14px;
            font-family: Arial, sans-serif;
            z-index: 10000;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            max-width: 300px;
            word-wrap: break-word;
          \`;
          notification.textContent = '$message';
          
          // 添加到页面
          document.body.appendChild(notification);
          
          // 5秒后自动消失
          setTimeout(function() {
            if (notification && notification.parentNode) {
              notification.parentNode.removeChild(notification);
            }
          }, 5000);
        })();
      ''');
      print('✅ 已注入状态提示: $message');
    } catch (e) {
      print('⚠️ 注入状态提示失败: $e');
    }
  }
}
