import 'dart:async';
import 'package:puppeteer/puppeteer.dart';
import '../../model/jlzs_account_new_model.dart';
import '../jlzs_browser_manager.dart';
import 'jlzs_api_interceptor.dart';
import 'jlzs_token_manager.dart';
import 'jlzs_navigation_helper.dart';
import 'jlzs_status_checker.dart';

/// 登录状态回调函数类型
typedef LoginStatusCallback = void Function(String message, {String? error});

/// 登录结果枚举
enum LoginResult {
  success,      // 登录成功
  failed,       // 登录失败
  cancelled,    // 用户取消
  error,        // 错误
}

/// 巨量指数登录管理器（重构版）
class JlzsLoginManagerNew {
  // 浏览器管理器
  final JlzsBrowserManager _browserManager;

  // 各个功能模块
  final JlzsApiInterceptor _apiInterceptor = JlzsApiInterceptor();
  final JlzsTokenManager _tokenManager = JlzsTokenManager();
  final JlzsNavigationHelper _navigationHelper = JlzsNavigationHelper();
  final JlzsStatusChecker _statusChecker = JlzsStatusChecker();

  // 登录状态管理 - 支持多账号并发登录
  final Map<String, Completer<LoginResult>> _accountCompleters = {};

  // 简化版本：保存当前的浏览器和页面引用
  final Map<String, Browser> _accountBrowsers = {};
  final Map<String, Page> _accountPages = {};

  // 连接保活定时器
  final Map<String, Timer> _keepAliveTimers = {};

  JlzsLoginManagerNew(this._browserManager);

  /// 开始登录流程
  Future<LoginResult> startLogin(
    JlzsAccountModel account,
    {LoginStatusCallback? onStatusUpdate}
  ) async {
    try {
      print('🚀 开始账号登录流程: ${account.maskedMobile}');
      onStatusUpdate?.call('正在启动浏览器...');

      // 先初始化等待状态
      print('⏳ 初始化登录等待状态...');
      _accountCompleters[account.id] = Completer<LoginResult>();

      // 简化浏览器创建（参考百度指数）
      print('🌐 创建浏览器实例...');

      // 创建参数列表
      List<String> args = [
        '--disable-infobars',         // 隐藏Chrome提示栏
        '--window-size=1366,768',     // 设置常见分辨率（避免默认800×600）
        '--disable-web-security',     // 可选：绕过CSP限制
        '--disable-features=IsolateOrigins,site-per-process' // 禁用沙箱隔离特征
      ];

      // 如果配置了代理，添加代理设置
      if (account.proxy != null && account.proxy!.address != null && account.proxy!.port != null) {
        args.add('--proxy-server=${account.proxy!.address}:${account.proxy!.port}');
        print('🔒 代理配置: ${account.proxy!.address}:${account.proxy!.port}');
      }

      // 启动浏览器，直接使用 args（参考百度指数的简单方式）
      var browser = await puppeteer.launch(
        headless: false,
        args: args,  // 直接传入参数列表
        ignoreDefaultArgs: ['--enable-automation'], // 禁用自动化提示
      );

      // 保存浏览器引用
      _accountBrowsers[account.id] = browser;

      // 启动连接保活机制
      _startKeepAlive(account.id, browser);

      // 创建登录页面
      print('📄 创建登录页面...');
      var loginPage = await browser.newPage();

      // 保存页面引用
      _accountPages[account.id] = loginPage;

      // 如果配置了代理认证，设置认证信息
      if (account.proxy != null &&
          account.proxy!.username != null &&
          account.proxy!.username!.isNotEmpty &&
          account.proxy!.password != null) {
        print('🔐 设置代理认证...');
        await loginPage.authenticate(
          username: account.proxy!.username!,
          password: account.proxy!.password!
        );
      }

      // 先导航到登录页面
      onStatusUpdate?.call('正在打开登录页面...');
      await _navigationHelper.navigateToLoginPage(loginPage, onStatusUpdate);

      // 直接在页面上设置API拦截（参考百度指数的简单方式）
      print('🔍 页面导航完成，现在设置API拦截...');
      await _apiInterceptor.setupPageInterception(
        loginPage,
        account,
        onStatusUpdate,
        _onLoginSuccess,
      );
      print('✅ 浏览器级API拦截设置完成');

      // 等待登录完成
      print('⏳ 等待用户完成登录...');
      onStatusUpdate?.call('请在浏览器中完成登录...');

      var result = await _accountCompleters[account.id]!.future;
      print('🎉 登录流程完成，结果: $result');

      return result;

    } catch (e) {
      print('❌ 登录流程异常: $e');
      onStatusUpdate?.call('登录失败', error: e.toString());
      
      // 清理状态
      _accountCompleters.remove(account.id);
      
      return LoginResult.error;
    }
  }

  /// 登录成功回调
  void _onLoginSuccess(JlzsAccountModel account) {
    print('🎉 登录成功回调触发: ${account.maskedMobile}');

    // 检查登录是否已经完成
    var completer = _accountCompleters[account.id];
    if (completer == null) {
      print('⚠️ 登录completer为空，可能已经完成');
      return;
    }

    if (!completer.isCompleted) {
      print('🎉 API响应检测到登录成功，立即完成！');
      completer.complete(LoginResult.success);

      // 延迟清理，确保主流程能正常完成
      Future.delayed(Duration(milliseconds: 100), () {
        _accountCompleters.remove(account.id);
      });

      // 延迟跳转，等待浏览器连接稳定
      print('⏳ 等待浏览器连接稳定后跳转...');
      Future.delayed(Duration(seconds: 20), () {
        _navigateToAnalysisPage(account);
      });
    } else {
      print('✅ 登录已完成，忽略重复的API调用');
    }
  }

  // 简化版本：移除复杂的页面跳转和msToken提取逻辑
  // 现在API拦截器会自动在API响应时提取cookies

  /// 登录成功后跳转到分析页面
  void _navigateToAnalysisPage(JlzsAccountModel account) {
    Future.microtask(() async {
      try {
        var page = _accountPages[account.id];
        var browser = _accountBrowsers[account.id];

        if (page == null || browser == null) {
          print('⚠️ 未找到页面或浏览器实例，无法跳转');
          return;
        }

        // 改进的浏览器连接检查逻辑
        print('🔍 检查浏览器连接状态...');
        if (!await _checkBrowserConnection(browser)) {
          print('❌ 浏览器连接已断开，尝试重新建立连接...');

          // 尝试重新建立连接
          if (!await _reconnectBrowser(account)) {
            print('❌ 重连失败，无法继续跳转');
            return;
          }

          // 更新引用
          browser = _accountBrowsers[account.id];
          if (browser == null) {
            print('❌ 重连后仍无法获取浏览器实例');
            return;
          }
        }

        // 检查页面状态
        print('🔍 检查页面状态...');
        if (page.isClosed) {
          print('⚠️ 页面已关闭，尝试创建新页面...');
          try {
            page = await browser.newPage();
            _accountPages[account.id] = page; // 更新页面引用

            // 为新页面重新设置API拦截
            await _apiInterceptor.setupPageInterception(
              page,
              account,
              null,
              _onLoginSuccess,
            );
            print('✅ 新页面创建成功并设置API拦截');
          } catch (e) {
            print('❌ 创建新页面失败: $e');
            return;
          }
        }

        print('🔄 开始跳转到分析页面...');
        await page.goto('https://trendinsight.oceanengine.com/arithmetic-index/analysis?keyword=%E6%9D%A8%E7%80%9A&appName=aweme');

        // 等待页面加载
        await Future.delayed(Duration(seconds: 3));

        print('✅ 成功跳转到分析页面，等待API拦截...');

      } catch (e) {
        print('⚠️ 跳转到分析页面失败: $e');

        // 延迟重试，给浏览器更多时间稳定
        print('⏳ 等待5秒后重试...');
        await Future.delayed(Duration(seconds: 5));

        try {
          var browser = _accountBrowsers[account.id];
          if (browser != null) {
            print('🔄 重试：检查浏览器状态...');
            var pages = await browser.pages;
            print('🔄 重试：浏览器有 ${pages.length} 个页面');

            var newPage = await browser.newPage();
            _accountPages[account.id] = newPage;

            // 重新设置API拦截
            await _apiInterceptor.setupPageInterception(
              newPage,
              account,
              null,
              _onLoginSuccess,
            );

            // 重新尝试跳转
            await newPage.goto('https://trendinsight.oceanengine.com/arithmetic-index/analysis?keyword=%E6%9D%A8%E7%80%9A&appName=aweme');
            print('✅ 重试成功：页面跳转完成');
          }
        } catch (retryError) {
          print('❌ 重试也失败: $retryError');
          print('💡 建议：请手动在浏览器中访问分析页面来测试API拦截');
        }
      }
    });
  }

  /// 检查账号登录状态
  Future<bool> checkLoginStatus(JlzsAccountModel account) async {
    return await _statusChecker.checkLoginStatus(account);
  }

  /// 批量检查账号状态
  Future<Map<String, bool>> batchCheckLoginStatus(List<JlzsAccountModel> accounts) async {
    return await _statusChecker.batchCheckLoginStatus(accounts);
  }

  /// 批量检查msToken有效性
  Map<String, bool> batchCheckMsTokenValidity(List<JlzsAccountModel> accounts) {
    return _tokenManager.batchCheckMsTokenValidity(accounts);
  }

  /// 刷新msToken
  Future<bool> refreshMsToken(JlzsAccountModel account) async {
    var browser = _browserManager.getBrowserByAccountId(account.id);
    if (browser != null) {
      return await _tokenManager.refreshMsToken(account, browser);
    }
    return false;
  }

  /// 检查msToken有效性
  bool checkMsTokenValidity(JlzsAccountModel account) {
    return _statusChecker.checkMsTokenValidity(account);
  }

  /// 检查账号健康状态
  Map<String, dynamic> checkAccountHealth(JlzsAccountModel account) {
    return _statusChecker.checkAccountHealth(account);
  }

  /// 批量检查账号健康状态
  Map<String, Map<String, dynamic>> batchCheckAccountHealth(List<JlzsAccountModel> accounts) {
    return _statusChecker.batchCheckAccountHealth(accounts);
  }

  /// 生成健康报告摘要
  Map<String, dynamic> generateHealthSummary(List<JlzsAccountModel> accounts) {
    return _statusChecker.generateHealthSummary(accounts);
  }

  /// 取消登录
  void cancelLogin(String accountId) {
    var completer = _accountCompleters[accountId];
    if (completer != null && !completer.isCompleted) {
      completer.complete(LoginResult.cancelled);
      _accountCompleters.remove(accountId);
    }
  }

  /// 启动连接保活机制
  void _startKeepAlive(String accountId, Browser browser) {
    print('🔄 启动账号 $accountId 的连接保活机制...');

    // 取消现有的保活定时器（如果有）
    _keepAliveTimers[accountId]?.cancel();

    // 每15秒检查一次连接状态（降低检查频率，减少干扰）
    _keepAliveTimers[accountId] = Timer.periodic(Duration(seconds: 15), (timer) async {
      bool isConnected = await _performLightweightConnectionCheck(accountId, browser);

      if (isConnected) {
        print('💓 账号 $accountId 连接保活检查通过');
      } else {
        print('💔 账号 $accountId 连接已断开，停止保活检查');

        // 连接断开，取消定时器
        timer.cancel();
        _keepAliveTimers.remove(accountId);
      }
    });
  }

  /// 执行轻量级连接检查
  Future<bool> _performLightweightConnectionCheck(String accountId, Browser browser) async {
    try {
      // 第一层检查：尝试获取浏览器目标信息（最轻量级）
      try {
        var targets = browser.targets;
        if (targets.isNotEmpty) {
          return true; // 如果能获取到targets，说明连接正常
        }
      } catch (e) {
        print('⚠️ 账号 $accountId targets检查失败，尝试version检查: $e');
      }

      // 第二层检查：尝试获取版本信息
      try {
        await browser.version.timeout(Duration(seconds: 8));
        return true; // 版本检查通过
      } catch (e) {
        print('⚠️ 账号 $accountId version检查失败，尝试pages检查: $e');
      }

      // 第三层检查：尝试获取页面列表（最后的检查）
      try {
        await browser.pages.timeout(Duration(seconds: 10));
        return true; // 页面检查通过
      } catch (e) {
        print('❌ 账号 $accountId 所有连接检查都失败: $e');
        return false; // 所有检查都失败，认为连接断开
      }

    } catch (e) {
      print('❌ 账号 $accountId 连接检查异常: $e');
      return false;
    }
  }

  /// 停止连接保活机制
  void _stopKeepAlive(String accountId) {
    var timer = _keepAliveTimers.remove(accountId);
    if (timer != null) {
      timer.cancel();
      print('🛑 已停止账号 $accountId 的连接保活机制');
    }
  }

  /// 检查浏览器连接状态
  Future<bool> _checkBrowserConnection(Browser browser) async {
    try {
      // 先检查版本信息（轻量级检查）
      await browser.version.timeout(Duration(seconds: 5));

      // 再检查页面列表（确保功能正常）
      await browser.pages.timeout(Duration(seconds: 5));

      print('✅ 浏览器连接检查通过');
      return true;
    } catch (e) {
      print('❌ 浏览器连接检查失败: $e');
      return false;
    }
  }

  /// 重新建立浏览器连接
  Future<bool> _reconnectBrowser(JlzsAccountModel account) async {
    try {
      print('🔄 尝试重新建立浏览器连接...');

      // 清理旧的连接
      _stopKeepAlive(account.id);
      _accountBrowsers.remove(account.id);
      _accountPages.remove(account.id);

      // 创建新的浏览器实例
      List<String> args = [];

      // 如果配置了代理，添加代理设置
      if (account.proxy != null && account.proxy!.address != null && account.proxy!.port != null) {
        args.add('--proxy-server=${account.proxy!.address}:${account.proxy!.port}');
        print('🔒 重连时使用代理配置: ${account.proxy!.address}:${account.proxy!.port}');
      }

      var browser = await puppeteer.launch(
        headless: false,
        args: args,
      );

      // 保存新的浏览器引用
      _accountBrowsers[account.id] = browser;

      // 重新启动保活机制
      _startKeepAlive(account.id, browser);

      // 创建新页面
      var newPage = await browser.newPage();
      _accountPages[account.id] = newPage;

      // 如果配置了代理认证，重新设置
      if (account.proxy != null &&
          account.proxy!.username != null &&
          account.proxy!.username!.isNotEmpty &&
          account.proxy!.password != null) {
        await newPage.authenticate(
          username: account.proxy!.username!,
          password: account.proxy!.password!
        );
      }

      print('✅ 浏览器重连成功');
      return true;
    } catch (e) {
      print('❌ 浏览器重连失败: $e');
      return false;
    }
  }

  /// 清理资源
  void dispose() {
    // 停止所有保活定时器
    for (var accountId in _keepAliveTimers.keys.toList()) {
      _stopKeepAlive(accountId);
    }

    _accountCompleters.clear();
    _apiInterceptor.dispose();
    _browserManager.closeAllBrowsers();
  }
}
