import '../../model/jlzs_task_data_model.dart';

/// 巨量指数任务生成引擎
class JlzsTaskGenerator {
  /// 生成API请求任务列表
  static List<JlzsApiTaskData> generateTasks(JlzsTaskGenerationConfig config) {
    List<JlzsApiTaskData> tasks = [];
    
    // 获取关键词分组
    List<List<String>> keywordGroups = config.getKeywordGroups();
    
    if (config.regionType == 'separate') {
      // 分开查询模式：每个地区单独请求所有关键词组
      tasks = _generateSeparateTasks(config, keywordGroups);
    } else {
      // 合并查询模式：所有地区合并请求所有关键词组
      tasks = _generateMergedTasks(config, keywordGroups);
    }
    
    return tasks;
  }

  /// 生成分开查询模式任务
  static List<JlzsApiTaskData> _generateSeparateTasks(
    JlzsTaskGenerationConfig config, 
    List<List<String>> keywordGroups
  ) {
    List<JlzsApiTaskData> tasks = [];
    int taskIndex = 1;
    
    // 遍历每个地区
    for (String region in config.regions) {
      // 为每个地区生成所有关键词组的任务
      for (List<String> keywordGroup in keywordGroups) {
        String taskId = 'jlzs_separate_${DateTime.now().millisecondsSinceEpoch}_$taskIndex';
        
        JlzsApiTaskData task = JlzsApiTaskData(
          id: taskId,
          keywords: keywordGroup,
          regions: [region], // 分开查询模式每个任务只包含一个地区
          startDate: config.startDate,
          endDate: config.endDate,
          platform: config.platform,
          regionType: config.regionType,
        );
        
        tasks.add(task);
        taskIndex++;
      }
    }
    
    return tasks;
  }

  /// 生成合并查询模式任务
  static List<JlzsApiTaskData> _generateMergedTasks(
    JlzsTaskGenerationConfig config, 
    List<List<String>> keywordGroups
  ) {
    List<JlzsApiTaskData> tasks = [];
    int taskIndex = 1;
    
    // 获取优化后的地区列表
    List<String> optimizedRegions = config.getOptimizedRegions();
    
    // 为每个关键词组生成一个任务，包含所有地区
    for (List<String> keywordGroup in keywordGroups) {
      String taskId = 'jlzs_merged_${DateTime.now().millisecondsSinceEpoch}_$taskIndex';
      
      JlzsApiTaskData task = JlzsApiTaskData(
        id: taskId,
        keywords: keywordGroup,
        regions: optimizedRegions, // 合并查询模式包含所有地区
        startDate: config.startDate,
        endDate: config.endDate,
        platform: config.platform,
        regionType: config.regionType,
      );
      
      tasks.add(task);
      taskIndex++;
    }
    
    return tasks;
  }

  /// 计算任务总数
  static int calculateTaskCount(JlzsTaskGenerationConfig config) {
    List<List<String>> keywordGroups = config.getKeywordGroups();
    
    if (config.regionType == 'separate') {
      // 分开查询：地区数 × 关键词组数
      return config.regions.length * keywordGroups.length;
    } else {
      // 合并查询：关键词组数
      return keywordGroups.length;
    }
  }

  /// 生成任务摘要信息
  static Map<String, dynamic> generateTaskSummary(JlzsTaskGenerationConfig config) {
    List<List<String>> keywordGroups = config.getKeywordGroups();
    int taskCount = calculateTaskCount(config);
    
    return {
      'total_keywords': config.keywords.length,
      'keyword_groups': keywordGroups.length,
      'keywords_per_group': keywordGroups.map((group) => group.length).toList(),
      'total_regions': config.regions.length,
      'region_type': config.regionType,
      'platform': config.platform,
      'date_range': '${config.startDate} - ${config.endDate}',
      'total_tasks': taskCount,
      'estimated_requests': taskCount,
    };
  }

  /// 验证任务生成配置
  static bool validateConfig(JlzsTaskGenerationConfig config) {
    // 检查关键词
    if (config.keywords.isEmpty) {
      return false;
    }
    
    // 检查地区
    if (config.regions.isEmpty) {
      return false;
    }
    
    // 检查日期格式
    if (config.startDate.length != 8 || config.endDate.length != 8) {
      return false;
    }
    
    // 检查平台
    if (!['aweme', 'toutiao'].contains(config.platform)) {
      return false;
    }
    
    // 检查地区类型
    if (!['separate', 'merged'].contains(config.regionType)) {
      return false;
    }
    
    return true;
  }

  /// 生成任务执行计划
  static Map<String, dynamic> generateExecutionPlan(
    List<JlzsApiTaskData> tasks, 
    int availableAccounts,
    int extractInterval
  ) {
    if (availableAccounts <= 0) {
      return {
        'error': '没有可用账号',
        'can_execute': false,
      };
    }
    
    int totalTasks = tasks.length;
    int concurrency = availableAccounts; // 并发数等于可用账号数
    int rounds = (totalTasks / concurrency).ceil(); // 执行轮数
    
    // 计算预估执行时间
    int estimatedTimePerTask = 10; // 假设每个任务10秒
    int totalExecutionTime = rounds * (estimatedTimePerTask + extractInterval);
    
    return {
      'total_tasks': totalTasks,
      'available_accounts': availableAccounts,
      'concurrency': concurrency,
      'execution_rounds': rounds,
      'extract_interval': extractInterval,
      'estimated_time_per_task': estimatedTimePerTask,
      'estimated_total_time': totalExecutionTime,
      'estimated_completion_time': DateTime.now().add(Duration(seconds: totalExecutionTime)),
      'can_execute': true,
    };
  }

  /// 将任务分配给账号
  static List<List<JlzsApiTaskData>> distributeTasksToAccounts(
    List<JlzsApiTaskData> tasks, 
    List<String> accountIds
  ) {
    if (accountIds.isEmpty) {
      return [];
    }
    
    List<List<JlzsApiTaskData>> distribution = [];
    for (int i = 0; i < accountIds.length; i++) {
      distribution.add([]);
    }
    
    // 轮询分配任务
    for (int i = 0; i < tasks.length; i++) {
      int accountIndex = i % accountIds.length;
      distribution[accountIndex].add(tasks[i]);
    }
    
    return distribution;
  }
}
