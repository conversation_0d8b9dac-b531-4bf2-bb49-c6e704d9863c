import 'dart:async';
import 'package:puppeteer/puppeteer.dart';
import '../../model/jlzs_account_new_model.dart';

/// msToken管理器 - 负责提取、验证和刷新msToken
class JlzsTokenManager {
  
  /// 提取msToken
  Future<void> extractMsToken(JlzsAccountModel account, Browser browser) async {
    try {
      print('🍪 开始提取msToken...');

      var pages = await browser.pages;
      if (pages.isEmpty) {
        print('⚠️ 未找到可用页面，无法提取msToken');
        return;
      }

      // 查找正确的页面（主页面，不是登录页面）
      Page? targetPage;
      print('🔍 开始查找正确页面，总页面数: ${pages.length}');

      for (int i = 0; i < pages.length; i++) {
        var page = pages[i];
        bool isClosed = page.isClosed;
        String? url = page.url;
        print('📄 页面 $i: URL=$url, isClosed=$isClosed');

        if (!isClosed) {
          if (url != null &&
              url.contains('trendinsight.oceanengine.com') &&
              !url.contains('/login')) {
            targetPage = page;
            print('🎯 找到目标页面: $url');
            break;
          } else {
            print('⚠️ 页面不符合条件: $url');
          }
        } else {
          print('⚠️ 页面已关闭: $url');
        }
      }

      if (targetPage == null) {
        print('⚠️ 未找到正确的页面，尝试创建新页面导航到主页...');
        try {
          // 创建新页面并导航到主页
          var newPage = await browser.newPage();
          await newPage.goto('https://trendinsight.oceanengine.com/');
          print('✅ 成功创建新页面并导航到主页');

          // 等待页面加载
          await Future.delayed(Duration(seconds: 3));
          targetPage = newPage;
        } catch (e) {
          print('❌ 创建新页面失败: $e');
          return;
        }
      }

      var page = targetPage;

      // 等待页面完全加载
      try {
        await page.waitForFunction('document.readyState === "complete"', timeout: Duration(seconds: 10));
      } catch (e) {
        print('⚠️ 等待页面加载超时，继续提取msToken: $e');
      }

      String? msToken;
      int? msTokenExpiry;
      int maxRetries = 30; // 最多重试30次
      int retryCount = 0;
      
      print('🔄 开始循环检测msToken，最多尝试 $maxRetries 次...');

      // 循环检测msToken直到找到为止
      while (msToken == null && retryCount < maxRetries) {
        retryCount++;
        print('🍪 第 $retryCount 次尝试获取msToken...');
        
        try {
          // 检查页面是否仍然可用，如果关闭则重新查找正确页面
          if (page.isClosed) {
            print('⚠️ 页面已关闭，重新查找正确页面...');
            var newPages = await browser.pages;
            Page? newTargetPage;
            for (var newPage in newPages) {
              if (!newPage.isClosed) {
                String? url = newPage.url;
                if (url != null &&
                    url.contains('trendinsight.oceanengine.com') &&
                    !url.contains('/login')) {
                  newTargetPage = newPage;
                  print('✅ 已切换到新的正确页面: $url');
                  break;
                }
              }
            }
            if (newTargetPage != null) {
              page = newTargetPage;
            } else {
              print('❌ 无可用的正确页面，停止msToken检测');
              break;
            }
          }

          // 优先使用JavaScript注入document.cookie方式获取msToken
          print('🍪 使用JavaScript document.cookie获取msToken...');
          try {
            var result = await page.evaluate('''
              (function() {
                try {
                  // 方法1：从document.cookie解析msToken
                  var cookies = document.cookie.split(';');
                  for (var i = 0; i < cookies.length; i++) {
                    var cookie = cookies[i].trim();
                    if (cookie.startsWith('msToken=')) {
                      var msTokenValue = cookie.substring('msToken='.length);
                      if (msTokenValue && msTokenValue.length > 0) {
                        return {
                          success: true,
                          msToken: msTokenValue,
                          method: 'document.cookie'
                        };
                      }
                    }
                  }

                  // 方法2：从localStorage获取
                  var localMsToken = localStorage.getItem('msToken');
                  if (localMsToken) {
                    return {
                      success: true,
                      msToken: localMsToken,
                      method: 'localStorage'
                    };
                  }

                  // 方法3：从sessionStorage获取
                  var sessionMsToken = sessionStorage.getItem('msToken');
                  if (sessionMsToken) {
                    return {
                      success: true,
                      msToken: sessionMsToken,
                      method: 'sessionStorage'
                    };
                  }

                  // 方法4：从window对象获取
                  if (window.msToken) {
                    return {
                      success: true,
                      msToken: window.msToken,
                      method: 'window.msToken'
                    };
                  }

                  return {
                    success: false,
                    method: 'none',
                    error: 'msToken not found in any location'
                  };
                } catch(e) {
                  return {
                    success: false,
                    error: e.toString(),
                    method: 'error'
                  };
                }
              })();
            ''');

            if (result['success'] == true) {
              msToken = result['msToken']?.toString();
              if (msToken != null && msToken!.isNotEmpty) {
                msTokenExpiry = DateTime.now().add(Duration(days: 7)).millisecondsSinceEpoch;
                print('✅ 第 $retryCount 次尝试成功找到msToken: ${msToken!.substring(0, 10)}... (通过${result['method']})');
                break;
              }
            } else {
              if (retryCount == 1) {
                print('⚠️ 第 $retryCount 次JavaScript方式未找到msToken: ${result['error'] ?? 'unknown'}');
              }
            }
          } catch (evalError) {
            print('⚠️ JavaScript执行失败: $evalError');
          }

          // 如果JavaScript方式失败，尝试Puppeteer API作为备用方案
          if (msToken == null) {
            try {
              print('🍪 JavaScript方式失败，尝试Puppeteer API备用方案...');
              var cookies = await page.cookies();

              for (var cookie in cookies) {
                if (cookie.name == 'msToken') {
                  msToken = cookie.value;
                  msTokenExpiry = cookie.expires != null && cookie.expires! > 0
                      ? (cookie.expires! * 1000).round()
                      : DateTime.now().add(Duration(days: 7)).millisecondsSinceEpoch;
                  print('✅ 第 $retryCount 次尝试成功找到msToken: ${msToken!.substring(0, 10)}... (通过Puppeteer API备用)');
                  break;
                }
              }
            } catch (apiError) {
              print('⚠️ Puppeteer API备用方案也失败: $apiError');
            }
          }

          if (msToken != null) {
            break; // 找到msToken，退出循环
          }


          // 如果没有找到msToken，等待2秒后重试
          if (msToken == null) {
            await Future.delayed(Duration(seconds: 2));
          }
          
        } catch (e) {
          print('⚠️ 第 $retryCount 次获取msToken失败: $e');
          
          // 如果是会话关闭错误，尝试重新查找正确页面
          if (e.toString().contains('Session closed') || e.toString().contains('page has been closed')) {
            try {
              var newPages = await browser.pages;
              Page? newTargetPage;
              for (var newPage in newPages) {
                if (!newPage.isClosed) {
                  String? url = newPage.url;
                  if (url != null &&
                      url.contains('trendinsight.oceanengine.com') &&
                      !url.contains('/login')) {
                    newTargetPage = newPage;
                    print('✅ 已切换到新的正确页面: $url');
                    break;
                  }
                }
              }
              if (newTargetPage != null) {
                page = newTargetPage;
              } else {
                print('⚠️ 无法找到正确的页面');
              }
            } catch (pageError) {
              print('⚠️ 无法获取新页面: $pageError');
            }
          }
          
          await Future.delayed(Duration(seconds: 2));
        }
      }

      // 检查最终结果
      if (msToken != null) {
        print('✅ msToken提取成功，共尝试 $retryCount 次');
      } else {
        print('❌ 经过 $maxRetries 次尝试仍未找到msToken，可能用户还未完成登录');
      }

      // 保存msToken到账号信息
      if (msToken != null) {
        if (account.session?.userInfo != null) {
          account.session!.userInfo['msToken'] = msToken;
          account.session!.userInfo['msTokenExpiry'] = msTokenExpiry;
          print('✅ msToken已保存到账号信息');
        }
      }
    } catch (e) {
      print('❌ 提取msToken失败: $e');
    }
  }

  /// 检查msToken有效性
  bool checkMsTokenValidity(JlzsAccountModel account) {
    try {
      if (account.session?.userInfo['msToken'] == null) {
        return false;
      }

      var expiry = account.session?.userInfo['msTokenExpiry'];
      if (expiry != null) {
        DateTime expiryDate = DateTime.fromMillisecondsSinceEpoch(expiry);
        return DateTime.now().isBefore(expiryDate);
      }

      // 如果没有过期时间，认为有效
      return true;
    } catch (e) {
      print('⚠️ 检查msToken有效性失败: $e');
      return false;
    }
  }

  /// 批量检查msToken有效性
  Map<String, bool> batchCheckMsTokenValidity(List<JlzsAccountModel> accounts) {
    Map<String, bool> results = {};
    
    for (var account in accounts) {
      results[account.id] = checkMsTokenValidity(account);
    }
    
    return results;
  }

  /// 刷新msToken
  Future<bool> refreshMsToken(JlzsAccountModel account, Browser browser) async {
    try {
      print('🔄 正在刷新账号 ${account.maskedMobile} 的msToken...');
      await extractMsToken(account, browser);
      print('✅ msToken刷新完成');
      return account.session?.userInfo['msToken'] != null;
    } catch (e) {
      print('❌ 刷新msToken失败: $e');
      return false;
    }
  }
}
