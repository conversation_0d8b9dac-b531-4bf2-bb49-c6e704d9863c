import 'package:bd/widgets/custom_checkbox.dart';
import 'package:flutter/material.dart';
import 'package:tekflat_design/tekflat_design.dart';

class CheckboxGroupSelection extends StatefulWidget {
  final Function(String group, List selectedOptions) onSelectionChanged;

  const CheckboxGroupSelection({
    Key? key,
    required this.onSelectionChanged,
  }) : super(key: key);

  @override
  _CheckboxGroupSelectionState createState() => _CheckboxGroupSelectionState();
}

class _CheckboxGroupSelectionState extends State<CheckboxGroupSelection> {
  String? selectedGroup = "search"; // 当前选中的组 ("search", "brand", "consult")

  Map<String,dynamic> data = {
    'search':[false,false,false],
    'brand':[false,false,false],
    'consult':[false,],
  };


  List dataItemStr = ["全部","PC端","移动端"];


  // // 处理当某个组被选中时的逻辑
  // void _onGroupSelected(String group) {
  //   setState(() {
  //     if (selectedGroup != group) {
  //       selectedGroup = group; // 切换到新的组
  //     }
  //   });
  //   _notifySelectionChange();
  // }

  // 通知父组件选中的组和选项
  void _notifySelectionChange(String group) {
    var selectedOptions = [];
    if (group == "search") {
      selectedOptions = data[group];
    } else if (group == "brand") {
      selectedOptions = data[group];
    } else if (group == "consult") {
      selectedOptions = data[group];
    }

    // 调用父组件的回调函数，返回当前的选项
    widget.onSelectionChanged(group, selectedOptions);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 搜索指数
        Padding(
          padding: EdgeInsets.symmetric(vertical: 12),
          child: TekTypography(
            text: "搜索指数",
            type: TekTypographyType.bodyMedium,
            color: Colors.grey,
          ),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [

            for(var i=0;i<data['search'].length ; i++)
              CustomCheckbox(
                isChecked: data['search'][i],
                onChanged:  (value) {

                  bool hasTrueInBrand = data['brand'].contains(true);
                  bool hasTrueInConsult = data['consult'].contains(true);

                  if(hasTrueInBrand||hasTrueInConsult){
                    return;
                  }else{

                    if(!value){
                      setState(() {
                        data['search'][i] = false;
                      });
                    }else{
                      setState(() {
                        data['search'][i] = true;
                      });
                    }
                    _notifySelectionChange('search');

                  }

                  // _notifySelectionChange();
                }, // 禁用其他组的复选框
                label: "${dataItemStr[i]}",
              ),
          ],
        ),

        // 品牌词
        Padding(
          padding: EdgeInsets.symmetric(vertical: 12),
          child: TekTypography(
            text: "品牌词",
            type: TekTypographyType.bodyMedium,
            color: Colors.grey,
          ),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            for(var i=0;i<data['brand'].length ; i++)
              CustomCheckbox(
                isChecked: data['brand'][i],
                onChanged:  (value) {
                  bool hasTrueInBrand = data['search'].contains(true);
                  bool hasTrueInConsult = data['consult'].contains(true);
                  print(hasTrueInBrand);
                  print(hasTrueInConsult);
                  if(hasTrueInBrand||hasTrueInConsult){
                    return;
                  }else{
                    print(value);
                    if(!value){
                      setState(() {
                        data['brand'][i] = false;
                      });
                    }else{
                      setState(() {
                        data['brand'][i] = true;
                      });
                    }
                    _notifySelectionChange('brand');
                  }

                  // _notifySelectionChange();
                }, // 禁用其他组的复选框
                label: "${dataItemStr[i]}",
              ),
          ],
        ),

        // 咨询指数
        Padding(
          padding: EdgeInsets.symmetric(vertical: 12),
          child: TekTypography(
            text: "咨询指数",
            type: TekTypographyType.bodyMedium,
            color: Colors.grey,
          ),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            for(var i=0;i<data['consult'].length ; i++)
              CustomCheckbox(
                isChecked: data['consult'][i],
                onChanged:  (value) {
                  bool hasTrueInSearch = data['search'].contains(true);
                  bool hasTrueInConsult = data['brand'].contains(true);
                  if(hasTrueInSearch||hasTrueInConsult){
                    return;
                  }else{
                    if(!value){
                      setState(() {
                        data['consult'][i] = false;
                      });
                    }else{
                      setState(() {
                        data['consult'][i] = true;
                      });
                    }
                    _notifySelectionChange('consult');
                  }

                  // _notifySelectionChange();
                }, // 禁用其他组的复选框
                label: "${dataItemStr[i]}",
              ),

          ],
        ),
      ],
    );
  }
}
