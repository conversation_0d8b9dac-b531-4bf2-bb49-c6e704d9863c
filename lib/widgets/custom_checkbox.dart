import 'package:flutter/material.dart';

class CustomCheckbox extends StatelessWidget {
  final bool isChecked;
  final Function(bool v)? onChanged; // 可选的回调函数
  final String label;

  const CustomCheckbox({
    Key? key,
    required this.isChecked,
    this.onChanged, // 回调函数现在是可选的
    required this.label,
  }) : super(key: key);

  void _toggleCheckbox() {
    if (onChanged != null) {
      onChanged?.call(!isChecked); // 通知父组件切换状态
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onChanged != null ? _toggleCheckbox : null, // 如果 onChanged 为空，则禁用点击
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 15,
            height: 15,
            decoration: BoxDecoration(
              border: Border.all(
                color: isChecked ? Colors.blue : Colors.grey,
                width: 1,
              ),
              color: isChecked ? Colors.blue : Colors.transparent,
              borderRadius: BorderRadius.circular(2), // 设置圆角
            ),
            child: isChecked
                ? const Icon(
              Icons.check,
              color: Colors.white,
              size: 12,
            )
                : null, // 如果没有选中，显示为空
          ),
          const SizedBox(width: 3), // 标签与复选框之间的间隔
          Text(
            label,
            style: const TextStyle(fontSize: 14, fontFamily: "Alibaba"),
            overflow: TextOverflow.ellipsis, // 溢出时显示省略号
            maxLines: 1, // 限制为一行
          ),
        ],
      ),
    );
  }
}