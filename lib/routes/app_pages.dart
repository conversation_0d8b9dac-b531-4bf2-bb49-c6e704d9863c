// ignore_for_file: avoid_classes_with_only_static_members

import 'package:get/get.dart';

import '../pages/baidu_index/baidu_index_binding.dart';
import '../pages/baidu_index/baidu_index_view.dart';
import '../pages/home/<USER>';
import '../pages/home/<USER>';

import 'app_routes.dart';

class AppPages {

  static GetPage _pageBuilder({
    required String name,
    required GetPageBuilder page,
    Bindings? binding,
    bool preventDuplicates = true,
  }) =>
      GetPage(
        name: name,
        page: page,
        binding: binding,
        preventDuplicates: preventDuplicates,
        transition: Transition.cupertino,
        popGesture: true,
      );

  static final routes = <GetPage>[

    // ...HPages.pages, // 首页
    _pageBuilder(
      name: AppRoutes.home,
      page: () => HomePage(),
      binding: HomeBinding(),
    ),
    // ...OPages.pages, // 百度指数
    _pageBuilder(
      name: AppRoutes.bdIndex,
      page: () => BaiduIndexPage(),
      binding: BaiduIndexBinding(),

    ),
    // ...WPages.pages, // 工作圈
    // ...MPages.pages, //
  ];
}