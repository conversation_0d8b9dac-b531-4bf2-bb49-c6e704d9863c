import 'dart:ui';

import 'package:get/get.dart';

import '../exports.dart';

class AppNavigator {
  AppNavigator._();

  static void startMain() {
    Get.offAllNamed(
      AppRoutes.home,
    );
  }

  static void startSplashToMain() {
    Get.offAndToNamed(
      AppRoutes.home,
    );
  }


  static void startBdIndex() {
    Get.toNamed(
      AppRoutes.bdIndex,
    );
    if (GetPlatform.isDesktop) {
      WindowManagerUtils.instance.setSize(1000, 800);
    }
  }

}