import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:desktop_multi_window/desktop_multi_window.dart';
import 'jlzs_profile_page.dart';
import 'jlzs_profile_binding.dart';

/// 巨量指数人群画像窗口应用
class JlzsProfileWindowApp extends StatelessWidget {
  final Map<String, dynamic> args;
  final WindowController? windowController;

  const JlzsProfileWindowApp({
    Key? key,
    required this.args,
    this.windowController,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      title: '巨量指数人群画像',
      theme: ThemeData(
        fontFamily: "Alibaba",
        primarySwatch: Colors.teal,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      home: JlzsProfilePage(windowController: windowController),
      initialBinding: JlzsProfileBinding(windowController: windowController),
      debugShowCheckedModeBanner: false,
    );
  }
}
