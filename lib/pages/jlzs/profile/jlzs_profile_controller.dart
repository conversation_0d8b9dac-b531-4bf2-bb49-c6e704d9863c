import 'package:get/get.dart';
import 'package:desktop_multi_window/desktop_multi_window.dart';
import 'package:flutter/services.dart';

/// 巨量指数人群画像控制器
class JlzsProfileController extends GetxController {
  // 画像状态
  final isLoading = false.obs;
  final selectedDimension = '年龄分布'.obs;
  final selectedKeyword = ''.obs;
  final selectedRegion = ''.obs;
  
  // 人群画像数据
  final ageDistribution = <Map<String, dynamic>>[].obs;
  final genderDistribution = <Map<String, dynamic>>[].obs;
  final interestDistribution = <Map<String, dynamic>>[].obs;
  final deviceDistribution = <Map<String, dynamic>>[].obs;
  final timeDistribution = <Map<String, dynamic>>[].obs;
  final locationDistribution = <Map<String, dynamic>>[].obs;
  
  // 画像摘要
  final profileSummary = <String, dynamic>{}.obs;
  
  // 窗口相关
  final isWindowClosing = false.obs;
  WindowController? windowController;

  JlzsProfileController({this.windowController});

  @override
  void onInit() {
    super.onInit();
    _loadProfileData();
  }

  /// 加载人群画像数据
  void _loadProfileData() {
    isLoading.value = true;
    
    // 模拟数据加载
    Future.delayed(const Duration(seconds: 1), () {
      _generateMockProfileData();
      isLoading.value = false;
    });
  }

  /// 生成模拟人群画像数据
  void _generateMockProfileData() {
    // 年龄分布
    ageDistribution.value = [
      {'range': '18-24', 'percentage': 25.6, 'count': 12800},
      {'range': '25-34', 'percentage': 35.2, 'count': 17600},
      {'range': '35-44', 'percentage': 22.8, 'count': 11400},
      {'range': '45-54', 'percentage': 12.4, 'count': 6200},
      {'range': '55+', 'percentage': 4.0, 'count': 2000},
    ];

    // 性别分布
    genderDistribution.value = [
      {'gender': '女性', 'percentage': 58.3, 'count': 29150},
      {'gender': '男性', 'percentage': 41.7, 'count': 20850},
    ];

    // 兴趣分布
    interestDistribution.value = [
      {'interest': '美食', 'percentage': 32.5, 'count': 16250},
      {'interest': '旅游', 'percentage': 28.7, 'count': 14350},
      {'interest': '时尚', 'percentage': 24.3, 'count': 12150},
      {'interest': '科技', 'percentage': 18.9, 'count': 9450},
      {'interest': '运动', 'percentage': 15.6, 'count': 7800},
    ];

    // 设备分布
    deviceDistribution.value = [
      {'device': 'iPhone', 'percentage': 42.1, 'count': 21050},
      {'device': 'Android', 'percentage': 55.3, 'count': 27650},
      {'device': 'iPad', 'percentage': 2.6, 'count': 1300},
    ];

    // 时间分布
    timeDistribution.value = [
      {'time': '早晨(6-9)', 'percentage': 15.2, 'count': 7600},
      {'time': '上午(9-12)', 'percentage': 22.8, 'count': 11400},
      {'time': '下午(12-18)', 'percentage': 28.5, 'count': 14250},
      {'time': '晚上(18-22)', 'percentage': 25.7, 'count': 12850},
      {'time': '深夜(22-6)', 'percentage': 7.8, 'count': 3900},
    ];

    // 地域分布
    locationDistribution.value = [
      {'location': '北京', 'percentage': 18.5, 'count': 9250},
      {'location': '上海', 'percentage': 16.2, 'count': 8100},
      {'location': '广州', 'percentage': 12.8, 'count': 6400},
      {'location': '深圳', 'percentage': 11.3, 'count': 5650},
      {'location': '杭州', 'percentage': 8.7, 'count': 4350},
      {'location': '其他', 'percentage': 32.5, 'count': 16250},
    ];

    // 画像摘要
    profileSummary.value = {
      'totalUsers': 50000,
      'primaryAge': '25-34岁',
      'primaryGender': '女性',
      'primaryInterest': '美食',
      'primaryDevice': 'Android',
      'primaryTime': '下午时段',
      'primaryLocation': '北京',
      'engagementRate': 78.5,
      'retentionRate': 65.2,
    };

    update();
  }

  /// 切换维度
  void changeDimension(String dimension) {
    selectedDimension.value = dimension;
    update();
  }

  /// 设置筛选条件
  void setFilters({String? keyword, String? region}) {
    if (keyword != null) selectedKeyword.value = keyword;
    if (region != null) selectedRegion.value = region;
    _loadProfileData();
  }

  /// 获取当前维度数据
  List<Map<String, dynamic>> getCurrentDimensionData() {
    switch (selectedDimension.value) {
      case '年龄分布':
        return ageDistribution;
      case '性别分布':
        return genderDistribution;
      case '兴趣分布':
        return interestDistribution;
      case '设备分布':
        return deviceDistribution;
      case '时间分布':
        return timeDistribution;
      case '地域分布':
        return locationDistribution;
      default:
        return ageDistribution;
    }
  }

  /// 导出人群画像报告
  void exportProfileReport() {
    Get.snackbar(
      '导出成功',
      '人群画像报告已导出到本地文件',
      snackPosition: SnackPosition.TOP,
    );
    
    // 通知主窗口
    _notifyMainWindow('profileExported', {
      'fileName': 'jlzs_profile_${DateTime.now().millisecondsSinceEpoch}.pdf',
      'dimension': selectedDimension.value,
      'totalUsers': profileSummary['totalUsers'],
    });
  }

  /// 生成个性化推荐
  void generateRecommendations() {
    Get.snackbar(
      '分析中',
      '正在生成个性化推荐策略...',
      snackPosition: SnackPosition.TOP,
    );
    
    // 模拟推荐生成
    Future.delayed(const Duration(seconds: 2), () {
      Get.snackbar(
        '推荐完成',
        '个性化推荐策略已生成',
        snackPosition: SnackPosition.TOP,
      );
    });
  }

  /// 刷新画像数据
  void refreshProfileData() {
    _loadProfileData();
    Get.snackbar(
      '刷新完成',
      '人群画像数据已更新',
      snackPosition: SnackPosition.TOP,
    );
  }

  /// 对比分析
  void compareProfiles() {
    Get.snackbar(
      '对比分析',
      '人群画像对比分析功能开发中',
      snackPosition: SnackPosition.TOP,
    );
  }

  /// 获取画像摘要
  Map<String, dynamic> getProfileSummary() {
    return {
      'dimension': selectedDimension.value,
      'keyword': selectedKeyword.value,
      'region': selectedRegion.value,
      'totalUsers': profileSummary['totalUsers'],
      'lastUpdated': DateTime.now().toIso8601String(),
    };
  }

  /// 通知主窗口
  void _notifyMainWindow(String action, Map<String, dynamic> data) {
    DesktopMultiWindow.invokeMethod(
      0, // 主窗口ID
      'jlzsAction',
      {
        'action': action,
        'data': data,
        'source': 'profile',
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  /// 关闭窗口
  void closeWindow() {
    if (isWindowClosing.value) return;

    isWindowClosing.value = true;

    // 通知主窗口
    _notifyMainWindow('windowClosed', {
      'windowType': 'jlzsProfile',
      'summary': getProfileSummary(),
    });

    // 关闭窗口
    if (windowController != null) {
      windowController!.close();
    } else {
      SystemNavigator.pop();
    }
  }
}
