import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:desktop_multi_window/desktop_multi_window.dart';
import 'jlzs_profile_controller.dart';

/// 巨量指数人群画像页面
class JlzsProfilePage extends StatelessWidget {
  final WindowController? windowController;

  const JlzsProfilePage({Key? key, this.windowController}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<JlzsProfileController>();

    return Scaffold(
      appBar: AppBar(
        title: const Text('巨量指数人群画像'),
        backgroundColor: Colors.teal.shade50,
        foregroundColor: Colors.teal.shade800,
        elevation: 1,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: controller.refreshProfileData,
            tooltip: '刷新数据',
          ),
          IconButton(
            icon: const Icon(Icons.compare),
            onPressed: controller.compareProfiles,
            tooltip: '对比分析',
          ),
          IconButton(
            icon: const Icon(Icons.download),
            onPressed: controller.exportProfileReport,
            tooltip: '导出报告',
          ),
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: controller.closeWindow,
            tooltip: '关闭窗口',
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // 控制面板
            _buildControlPanel(controller),
            const SizedBox(height: 16),
            
            // 主要内容区域
            Expanded(
              child: Row(
                children: [
                  // 左侧：画像摘要
                  Expanded(
                    flex: 1,
                    child: _buildProfileSummary(controller),
                  ),
                  const SizedBox(width: 16),
                  
                  // 中间：维度选择和图表
                  Expanded(
                    flex: 2,
                    child: _buildDimensionChart(controller),
                  ),
                  const SizedBox(width: 16),
                  
                  // 右侧：详细数据和推荐
                  Expanded(
                    flex: 1,
                    child: _buildDetailPanel(controller),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建控制面板
  Widget _buildControlPanel(JlzsProfileController controller) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Row(
        children: [
          // 维度选择
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('分析维度', style: TextStyle(fontSize: 12, fontWeight: FontWeight.w500)),
                const SizedBox(height: 4),
                Obx(() => DropdownButton<String>(
                  value: controller.selectedDimension.value,
                  isExpanded: true,
                  items: ['年龄分布', '性别分布', '兴趣分布', '设备分布', '时间分布', '地域分布'].map((dimension) {
                    return DropdownMenuItem(value: dimension, child: Text(dimension, style: const TextStyle(fontSize: 12)));
                  }).toList(),
                  onChanged: (value) => controller.changeDimension(value!),
                )),
              ],
            ),
          ),
          const SizedBox(width: 16),
          
          // 筛选条件
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('筛选条件', style: TextStyle(fontSize: 12, fontWeight: FontWeight.w500)),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Expanded(
                      child: TextField(
                        decoration: const InputDecoration(
                          hintText: '关键词',
                          border: OutlineInputBorder(),
                          contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                        ),
                        style: const TextStyle(fontSize: 12),
                        onChanged: (value) => controller.setFilters(keyword: value),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: TextField(
                        decoration: const InputDecoration(
                          hintText: '地区',
                          border: OutlineInputBorder(),
                          contentPadding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                        ),
                        style: const TextStyle(fontSize: 12),
                        onChanged: (value) => controller.setFilters(region: value),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(width: 16),
          
          // 操作按钮
          ElevatedButton.icon(
            onPressed: controller.generateRecommendations,
            icon: const Icon(Icons.lightbulb, size: 16),
            label: const Text('生成推荐', style: TextStyle(fontSize: 12)),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.teal.shade600,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建画像摘要
  Widget _buildProfileSummary(JlzsProfileController controller) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.people, color: Colors.teal.shade600, size: 20),
              const SizedBox(width: 8),
              const Text('人群画像摘要', style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600)),
            ],
          ),
          const SizedBox(height: 16),
          
          Expanded(
            child: Obx(() => SingleChildScrollView(
              child: Column(
                children: [
                  _buildSummaryCard('总用户数', '${controller.profileSummary['totalUsers'] ?? 0}', Icons.group),
                  const SizedBox(height: 12),
                  _buildSummaryCard('主要年龄段', controller.profileSummary['primaryAge'] ?? '-', Icons.cake),
                  const SizedBox(height: 12),
                  _buildSummaryCard('主要性别', controller.profileSummary['primaryGender'] ?? '-', Icons.person),
                  const SizedBox(height: 12),
                  _buildSummaryCard('主要兴趣', controller.profileSummary['primaryInterest'] ?? '-', Icons.favorite),
                  const SizedBox(height: 12),
                  _buildSummaryCard('主要设备', controller.profileSummary['primaryDevice'] ?? '-', Icons.phone_android),
                  const SizedBox(height: 12),
                  _buildSummaryCard('活跃时段', controller.profileSummary['primaryTime'] ?? '-', Icons.access_time),
                  const SizedBox(height: 12),
                  _buildSummaryCard('主要地区', controller.profileSummary['primaryLocation'] ?? '-', Icons.location_on),
                  const SizedBox(height: 12),
                  _buildSummaryCard('参与度', '${controller.profileSummary['engagementRate'] ?? 0}%', Icons.trending_up),
                  const SizedBox(height: 12),
                  _buildSummaryCard('留存率', '${controller.profileSummary['retentionRate'] ?? 0}%', Icons.repeat),
                ],
              ),
            )),
          ),
        ],
      ),
    );
  }

  /// 构建摘要卡片
  Widget _buildSummaryCard(String title, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.teal.shade50,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: Colors.teal.shade200),
      ),
      child: Row(
        children: [
          Icon(icon, color: Colors.teal.shade600, size: 16),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(title, style: TextStyle(fontSize: 11, color: Colors.grey.shade600)),
                const SizedBox(height: 2),
                Text(value, style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w600)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建维度图表
  Widget _buildDimensionChart(JlzsProfileController controller) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.pie_chart, color: Colors.teal.shade600, size: 20),
              const SizedBox(width: 8),
              Obx(() => Text(
                '${controller.selectedDimension.value}分析',
                style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
              )),
            ],
          ),
          const SizedBox(height: 16),
          
          Expanded(
            child: Obx(() {
              if (controller.isLoading.value) {
                return const Center(child: CircularProgressIndicator());
              }
              
              final data = controller.getCurrentDimensionData();
              
              return Column(
                children: [
                  // 图表占位符
                  Expanded(
                    flex: 2,
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.grey.shade100,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.donut_large, size: 64, color: Colors.grey.shade400),
                            const SizedBox(height: 8),
                            Text(
                              '${controller.selectedDimension.value}图表',
                              style: TextStyle(color: Colors.grey.shade600),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // 数据列表
                  Expanded(
                    flex: 1,
                    child: ListView.builder(
                      itemCount: data.length,
                      itemBuilder: (context, index) {
                        final item = data[index];
                        final label = _getItemLabel(item);
                        final percentage = item['percentage'] ?? 0.0;
                        final count = item['count'] ?? 0;
                        
                        return Padding(
                          padding: const EdgeInsets.symmetric(vertical: 4),
                          child: Row(
                            children: [
                              Container(
                                width: 12,
                                height: 12,
                                decoration: BoxDecoration(
                                  color: _getColorForIndex(index),
                                  borderRadius: BorderRadius.circular(2),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(label, style: const TextStyle(fontSize: 12)),
                              ),
                              Text('$percentage%', style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500)),
                              const SizedBox(width: 8),
                              Text('($count)', style: TextStyle(fontSize: 11, color: Colors.grey.shade600)),
                            ],
                          ),
                        );
                      },
                    ),
                  ),
                ],
              );
            }),
          ),
        ],
      ),
    );
  }

  /// 构建详细面板
  Widget _buildDetailPanel(JlzsProfileController controller) {
    return Column(
      children: [
        // 推荐策略
        Expanded(
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.lightbulb, color: Colors.teal.shade600, size: 20),
                    const SizedBox(width: 8),
                    const Text('推荐策略', style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600)),
                  ],
                ),
                const SizedBox(height: 16),
                
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildRecommendationCard('内容策略', '基于主要兴趣"美食"，建议增加美食相关内容'),
                        const SizedBox(height: 12),
                        _buildRecommendationCard('时间策略', '下午时段活跃度最高，建议在此时段发布内容'),
                        const SizedBox(height: 12),
                        _buildRecommendationCard('地域策略', '北京地区用户最多，可针对性投放广告'),
                        const SizedBox(height: 12),
                        _buildRecommendationCard('设备策略', 'Android用户占比较高，优化Android端体验'),
                        const SizedBox(height: 12),
                        _buildRecommendationCard('年龄策略', '25-34岁用户为主力，内容风格应符合此年龄段'),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// 构建推荐卡片
  Widget _buildRecommendationCard(String title, String content) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.amber.shade50,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: Colors.amber.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(title, style: TextStyle(fontSize: 12, fontWeight: FontWeight.w600, color: Colors.amber.shade800)),
          const SizedBox(height: 4),
          Text(content, style: TextStyle(fontSize: 11, color: Colors.amber.shade700)),
        ],
      ),
    );
  }

  /// 获取项目标签
  String _getItemLabel(Map<String, dynamic> item) {
    if (item.containsKey('range')) return item['range'];
    if (item.containsKey('gender')) return item['gender'];
    if (item.containsKey('interest')) return item['interest'];
    if (item.containsKey('device')) return item['device'];
    if (item.containsKey('time')) return item['time'];
    if (item.containsKey('location')) return item['location'];
    return '';
  }

  /// 获取索引对应的颜色
  Color _getColorForIndex(int index) {
    final colors = [
      Colors.blue.shade400,
      Colors.green.shade400,
      Colors.orange.shade400,
      Colors.purple.shade400,
      Colors.red.shade400,
      Colors.teal.shade400,
    ];
    return colors[index % colors.length];
  }
}
