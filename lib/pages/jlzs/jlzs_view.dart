import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:desktop_multi_window/desktop_multi_window.dart';
import 'jlzs_logic.dart';
import 'widgets/keyword_input_widget.dart';
import 'widgets/jlzs_region_tree_widget.dart';
import 'widgets/config_panel_widget.dart';
import 'widgets/collection_control_widget.dart';

/// 巨量指数主页面
class JlzsPage extends StatelessWidget {
  final WindowController? windowController;

  const JlzsPage({Key? key, this.windowController}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final logic = Get.find<JlzsLogic>();

    return Scaffold(
      appBar: AppBar(
        title: const Text('巨量指数 - 数据采集工具'),
        backgroundColor: Colors.blue.shade50,
        foregroundColor: Colors.blue.shade800,
        elevation: 1,
        actions: [
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: logic.closeWindow,
            tooltip: '关闭窗口',
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 第一列 - 数据输入区域
            Expanded(
              flex: 3,
              child: Container(
                padding: const EdgeInsets.all(12.0),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(8.0),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '数据输入区域',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue.shade700,
                      ),
                    ),
                    const SizedBox(height: 12),
                    // 关键词输入组件
                    const KeywordInputWidget(),
                    const SizedBox(height: 16),
                    // 地区选择组件
                    const Expanded(child: JlzsRegionTreeWidget()),
                  ],
                ),
              ),
            ),
            const SizedBox(width: 12),

            // 第二列 - 配置管理区域
            Expanded(
              flex: 2,
              child: Container(
                padding: const EdgeInsets.all(12.0),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(8.0),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '配置管理区域',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.purple.shade700,
                      ),
                    ),
                    const SizedBox(height: 12),
                    // 配置面板组件
                    const Expanded(child: ConfigPanelWidget()),
                  ],
                ),
              ),
            ),
            const SizedBox(width: 12),

            // 第三列 - 采集控制区域
            Expanded(
              flex: 4,
              child: Container(
                padding: const EdgeInsets.all(12.0),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(8.0),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '采集控制区域',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: Colors.green.shade700,
                      ),
                    ),
                    const SizedBox(height: 12),
                    // 采集控制组件
                    const Expanded(child: CollectionControlWidget()),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
