import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:desktop_multi_window/desktop_multi_window.dart';
import 'jlzs_view.dart';
import 'jlzs_binding.dart';

/// 巨量指数窗口应用 - 完全使用GetX
class JlzsWindowApp extends StatelessWidget {
  final Map<String, dynamic> args;
  final WindowController? windowController;

  const JlzsWindowApp({
    Key? key,
    required this.args,
    this.windowController,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      title: '巨量指数',
      theme: ThemeData(
        fontFamily: "Alibaba",
        primarySwatch: Colors.blue,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      home: JlzsPage(windowController: windowController),
      initialBinding: JlzsBinding(windowController: windowController),
      debugShowCheckedModeBanner: false,
    );
  }
}
