import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:desktop_multi_window/desktop_multi_window.dart';
import 'package:file_picker/file_picker.dart';
import 'package:csv/csv.dart';
import 'package:intl/intl.dart';
import '../../model/jlzs_config_model.dart';
import '../../model/jlzs_account_new_model.dart';
import '../../model/jlzs_task_model.dart';
import '../../model/jlzs_data_model.dart';
import '../../model/jlzs_task_data_model.dart';
import '../../utils/multi_window_manager.dart';
import '../../utils/jlzs_browser_manager.dart';
import '../../utils/jlzs/jlzs_login_manager_new.dart';
import '../../utils/jlzs/jlzs_task_generator.dart';
import '../../utils/jlzs/jlzs_api_client.dart';
import '../../utils/jlzs/jlzs_proxy_api_manager.dart';
import '../../utils/store_util.dart';

import 'widgets/account_manage_dialog.dart';

/// 巨量指数逻辑控制器
class JlzsLogic extends GetxController {
  // 窗口相关
  final WindowController? windowController;
  final isWindowClosing = false.obs;

  // 配置数据
  final config = JlzsConfigModel().obs;

  // 账号管理 (新版本)
  final JlzsBrowserManager browserManager = JlzsBrowserManager();
  final JlzsLoginManagerNew loginManager = JlzsLoginManagerNew(JlzsBrowserManager());
  final RxList<JlzsAccountModel> accounts = <JlzsAccountModel>[].obs;

  // UI状态
  final keywordController = TextEditingController();
  final cityInputController = TextEditingController();
  final savePathController = TextEditingController();

  // 关键词转换规则
  final keywordConversionRules = KeywordConversionRules().obs;

  // 地区数据
  final areaData = <TreeNode>[].obs;
  final selectedRegions = <String>[].obs;

  // 任务相关
  final currentTask = Rxn<JlzsTaskModel>();
  final taskLogs = <String>[].obs;
  final ScrollController logScrollController = ScrollController();

  // API任务相关
  final RxList<JlzsApiTaskData> apiTasks = <JlzsApiTaskData>[].obs;
  final RxList<JlzsApiTaskData> completedTasks = <JlzsApiTaskData>[].obs;
  final RxList<JlzsApiTaskData> failedTasks = <JlzsApiTaskData>[].obs;

  // 数据存储
  final RxList<JlzsDataModel> separateData = <JlzsDataModel>[].obs;
  final RxList<JlzsMergedData> mergedData = <JlzsMergedData>[].obs;

  // 代理管理
  final JlzsProxyApiManager proxyManager = JlzsProxyApiManager();

  // 按钮状态
  final isStartButtonEnabled = true.obs;
  final startButtonText = '开始'.obs;
  final isStopButtonEnabled = false.obs;

  JlzsLogic({this.windowController});

  @override
  void onInit() {
    super.onInit();
    _initializeData();
    _initializeAccounts();
    _setupMessageHandler();
  }

  @override
  void onClose() {
    keywordController.dispose();
    cityInputController.dispose();
    savePathController.dispose();
    logScrollController.dispose();

    // 关闭所有浏览器实例
    browserManager.closeAllBrowsers();
    super.onClose();
  }

  /// 初始化账号数据
  Future<void> _initializeAccounts() async {
    try {
      // TODO: 从本地存储加载账号数据
      // 这里可以添加从文件或数据库加载账号的逻辑

      // 尝试重连现有的浏览器实例
      await _reconnectExistingBrowsers();

      // 更新UI
      update(['account_stats']);

      addLog('账号管理器初始化完成');
    } catch (e) {
      addLog('初始化账号管理器失败: $e');
    }
  }

  /// 重连现有的浏览器实例
  Future<void> _reconnectExistingBrowsers() async {
    for (var account in accounts) {
      if (account.debugPort != null) {
        try {
          var browser = await browserManager.reconnectToBrowser(
            account.id,
            account.debugPort!
          );

          if (browser != null) {
            // 检查登录状态
            bool isLoggedIn = await loginManager.checkLoginStatus(account);
            if (isLoggedIn) {
              account.status = AccountStatus.active;
              addLog('重连账号 ${account.maskedMobile} 成功');
            } else {
              account.status = AccountStatus.offline;
              addLog('账号 ${account.maskedMobile} 需要重新登录');
            }
          }
        } catch (e) {
          account.status = AccountStatus.error;
          addLog('重连账号 ${account.maskedMobile} 失败: $e');
        }
      }
    }
  }

  /// 初始化数据
  void _initializeData() {
    _loadProvincesData();
    _initializeConfig();
  }

  /// 加载省市数据
  Future<void> _loadProvincesData() async {
    try {
      final String data = await rootBundle.loadString('assets/jlzs_provinces_cities.json');
      final Map<String, dynamic> jsonData = json.decode(data);
      final List<Map<String, dynamic>> provinces = List<Map<String, dynamic>>.from(jsonData['provinces']);

      // 转换为TreeNode结构
      List<TreeNode> treeNodes = [];
      int nodeId = 1;

      for (var province in provinces) {
        String provinceName = province['name'];
        List<String> cities = List<String>.from(province['cities']);

        // 创建城市节点
        List<TreeNode> cityNodes = [];
        for (var city in cities) {
          cityNodes.add(TreeNode(city, nodeId++, []));
        }

        // 创建省份节点
        TreeNode provinceNode = TreeNode(provinceName, nodeId++, cityNodes);
        treeNodes.add(provinceNode);
      }

      areaData.value = treeNodes;
      print('成功加载省市数据，共 ${areaData.length} 个省份');
      update(['region_tree']); // 强制更新UI
    } catch (e) {
      print('加载省市数据失败: $e');
      // 提供备用数据
      _createDefaultAreaData();
      update(['region_tree']);
      Get.snackbar('提示', '使用默认地区数据', snackPosition: SnackPosition.TOP);
    }
  }

  /// 创建默认地区数据
  void _createDefaultAreaData() {
    areaData.value = [
      TreeNode('北京', 1, [TreeNode('北京市', 2, [])]),
      TreeNode('上海', 3, [TreeNode('上海市', 4, [])]),
      TreeNode('广东', 5, [
        TreeNode('广州', 6, []),
        TreeNode('深圳', 7, []),
        TreeNode('珠海', 8, []),
        TreeNode('汕头', 9, []),
        TreeNode('佛山', 10, []),
        TreeNode('韶关', 11, []),
        TreeNode('湛江', 12, []),
        TreeNode('肇庆', 13, []),
        TreeNode('江门', 14, []),
        TreeNode('茂名', 15, []),
        TreeNode('惠州', 16, []),
        TreeNode('梅州', 17, []),
        TreeNode('汕尾', 18, []),
        TreeNode('河源', 19, []),
        TreeNode('阳江', 20, []),
        TreeNode('清远', 21, []),
        TreeNode('东莞', 22, []),
        TreeNode('中山', 23, []),
        TreeNode('潮州', 24, []),
        TreeNode('揭阳', 25, []),
        TreeNode('云浮', 26, []),
      ]),
    ];
  }

  /// 初始化配置
  void _initializeConfig() {
    config.value = JlzsConfigModel();
    savePathController.text = config.value.savePath;
  }

  /// 设置消息处理器
  void _setupMessageHandler() {
    // 子窗口不需要设置消息处理器
  }

  /// 处理关键词输入变化 (现在只用于手动转换)
  void onKeywordChanged(String value) {
    // 移除自动转换逻辑，现在只在用户点击转换按钮时才处理
    // 这个方法保留是为了兼容性，但不再自动处理关键词
  }

  /// 手动转换关键词 (由转换按钮触发)
  void convertKeywordsManually(String inputText) {
    if (inputText.isEmpty) {
      config.value = config.value.copyWith(keywords: []);
    } else {
      // 解析关键词
      List<String> keywords = _parseKeywords(inputText);

      // 应用转换规则
      keywords = keywordConversionRules.value.applyToList(keywords);

      config.value = config.value.copyWith(keywords: keywords);
    }
    update(['collection_details']);
  }

  /// 解析关键词字符串 (公开方法)
  List<String> parseKeywords(String value) {
    return _parseKeywords(value);
  }

  /// 解析关键词字符串 (内部方法)
  List<String> _parseKeywords(String value) {
    List<String> keywords = [];

    // 支持多种分隔符：换行、空格、逗号、分号
    String normalizedValue = value
        .replaceAll('\n', ',')
        // .replaceAll(' ', ',')
        .replaceAll('，', ',')  // 中文逗号
        .replaceAll(';', ',')
        .replaceAll('；', ','); // 中文分号

    // 按逗号分割
    List<String> parts = normalizedValue.split(',');

    for (String part in parts) {
      String trimmed = part.trim();
      if (trimmed.isNotEmpty) {
        keywords.add(trimmed);
      }
    }

    // 去重
    return keywords.toSet().toList();
  }

  /// 处理地区选择变化
  void onRegionSelectionChanged(String regionName) {
    // 更新选中的地区列表
    _updateSelectedRegions();
    // 更新配置
    config.value = config.value.copyWith(selectedRegions: selectedRegions.toList());
    // 更新UI
    update(['region_tree', 'collection_details']);
  }

  /// 更新选中的地区列表
  void _updateSelectedRegions() {
    selectedRegions.clear();
    for (var province in areaData) {
      selectedRegions.addAll(province.getCheckedNames());
    }
  }

  /// 获取选中的地区数量
  int getSelectedRegionCount() {
    return selectedRegions.length;
  }

  /// 更新转换规则
  void updateConversionRule(String ruleType, dynamic value) {
    switch (ruleType) {
      case 'toLowerCase':
        keywordConversionRules.value = keywordConversionRules.value.copyWith(toLowerCase: value);
        break;
      case 'addPrefix':
        keywordConversionRules.value = keywordConversionRules.value.copyWith(addPrefix: value);
        break;
      case 'prefix':
        keywordConversionRules.value = keywordConversionRules.value.copyWith(prefix: value);
        break;
      case 'addSuffix':
        keywordConversionRules.value = keywordConversionRules.value.copyWith(addSuffix: value);
        break;
      case 'suffix':
        keywordConversionRules.value = keywordConversionRules.value.copyWith(suffix: value);
        break;
    }
  }

  /// 重新应用转换规则
  void reapplyConversionRules() {
    final currentInput = keywordController.text;
    if (currentInput.isNotEmpty) {
      onKeywordChanged(currentInput);
    }
  }

  /// 全选所有地区
  void selectAllRegions() {
    for (var province in areaData) {
      province.setChildrenChecked(true);
    }
    _updateSelectedRegions();
    config.value = config.value.copyWith(selectedRegions: selectedRegions.toList());
    update(['region_tree', 'collection_details']);
  }

  /// 取消全选
  void unselectAllRegions() {
    for (var province in areaData) {
      province.setChildrenChecked(false);
    }
    _updateSelectedRegions();
    config.value = config.value.copyWith(selectedRegions: selectedRegions.toList());
    update(['region_tree', 'collection_details']);
  }

  /// 检查是否全部选中
  bool isAllRegionsSelected() {
    if (areaData.isEmpty) return false;
    for (var province in areaData) {
      if (!province.areAllChildrenChecked() && !province.isChecked) {
        return false;
      }
    }
    return true;
  }

  /// 检查是否有部分选中
  bool isSomeRegionsSelected() {
    for (var province in areaData) {
      if (province.hasCheckedChildren() || province.isChecked) {
        return true;
      }
    }
    return false;
  }

  /// 手动输入城市列表
  void showCityInputDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('手动输入城市'),
        content: SizedBox(
          width: 400,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('请输入城市名称，多个城市用逗号或换行分隔：'),
              const SizedBox(height: 10),
              TextField(
                controller: cityInputController,
                maxLines: 5,
                decoration: const InputDecoration(
                  hintText: '例如：北京,上海,广州\n或每行一个城市',
                  border: OutlineInputBorder(),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: _processCityInput,
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  /// 处理城市输入
  void _processCityInput() {
    final input = cityInputController.text.trim();
    if (input.isEmpty) {
      Get.back();
      return;
    }

    // 解析输入的城市
    List<String> inputCities = [];

    // 先按换行分割
    List<String> lines = input.split('\n');
    for (String line in lines) {
      // 再按逗号分割
      List<String> citiesInLine = line.split(',');
      inputCities.addAll(citiesInLine);
    }

    // 清理城市名称
    inputCities = inputCities
        .map((city) => city.trim())
        .where((city) => city.isNotEmpty)
        .toList();

    // 匹配并选中城市
    int matchedCount = 0;
    for (String inputCity in inputCities) {
      for (var province in areaData) {
        if (province.selectByName(inputCity)) {
          matchedCount++;
        }
        for (var city in province.children) {
          if (city.name.contains(inputCity) || inputCity.contains(city.name)) {
            if (!city.isChecked) {
              city.isChecked = true;
              matchedCount++;
            }
          }
        }
      }
    }

    _updateSelectedRegions();
    config.value = config.value.copyWith(selectedRegions: selectedRegions.toList());
    update(['region_tree', 'collection_details']);

    cityInputController.clear();
    Get.back();

    Get.snackbar(
      '城市匹配完成',
      '成功匹配并选中 $matchedCount 个城市',
      snackPosition: SnackPosition.TOP,
    );
  }

  /// 选择保存路径
  Future<void> selectSavePath() async {
    try {
      String? selectedDirectory = await FilePicker.platform.getDirectoryPath();
      if (selectedDirectory != null) {
        savePathController.text = selectedDirectory;
        config.value = config.value.copyWith(savePath: selectedDirectory);
        update(['save_path']);
      }
    } catch (e) {
      print('选择路径失败: $e');
      Get.snackbar('错误', '选择路径失败', snackPosition: SnackPosition.TOP);
    }
  }

  /// 手动输入保存路径
  void onSavePathChanged(String path) {
    config.value = config.value.copyWith(savePath: path);
    update(['collection_details']);
  }

  /// 设置开始日期
  void setStartDate(DateTime date) {
    config.value = config.value.copyWith(startDate: date);
    update(['collection_details']);
  }

  /// 设置结束日期
  void setEndDate(DateTime date) {
    config.value = config.value.copyWith(endDate: date);
    update(['collection_details']);
  }

  /// 设置平台
  void setPlatform(String platform) {
    config.value = config.value.copyWith(platform: platform);
    update(['collection_details']);
  }

  /// 设置地区类型
  void setRegionType(String regionType) {
    config.value = config.value.copyWith(regionType: regionType);
    update(['collection_details']);
  }

  /// 设置提取间隔
  void setExtractInterval(int interval) {
    config.value = config.value.copyWith(extractInterval: interval);
    update(['collection_details']);
  }

  /// 设置处理周期 (多选)
  void setProcessingPeriods(List<String> periods) {
    config.value = config.value.copyWith(processingPeriods: periods);
    update(['collection_details']);
  }

  /// 切换处理周期选择状态
  void toggleProcessingPeriod(String period) {
    List<String> currentPeriods = List.from(config.value.processingPeriods);

    if (currentPeriods.contains(period)) {
      // 如果已选中，则取消选择（但至少保留一个）
      if (currentPeriods.length > 1) {
        currentPeriods.remove(period);
      }
    } else {
      // 如果未选中，则添加选择
      currentPeriods.add(period);
    }

    config.value = config.value.copyWith(processingPeriods: currentPeriods);
    update(['collection_details']);
  }

  /// 检查处理周期是否被选中
  bool isProcessingPeriodSelected(String period) {
    return config.value.processingPeriods.contains(period);
  }

  /// 设置聚合方式
  void setAggregationMethod(String method) {
    config.value = config.value.copyWith(aggregationMethod: method);
    update(['collection_details']);
  }

  /// 打开账号管理
  void openAccountManagement() {
    Get.dialog(
      const AccountManageDialog(),
      barrierDismissible: true,
    );
  }

  /// 添加账号（需要手机号）
  Future<void> addAccount(String mobile, {JlzsProxyConfig? proxy, String? remark}) async {
    try {
      // 生成账号ID
      String accountId = 'jlzs_${DateTime.now().millisecondsSinceEpoch}';

      // 创建账号对象
      var account = JlzsAccountModel(
        id: accountId,
        mobile: mobile,
        proxy: proxy,
        remark: remark,
      );

      // 添加到列表
      accounts.add(account);

      // 开始登录流程
      await startLoginProcess(account);

      // 更新UI
      update(['account_stats']);

      addLog('账号 ${account.maskedMobile} 添加成功');
    } catch (e) {
      addLog('添加账号失败: $e');
      Get.snackbar('错误', '添加账号失败: $e', snackPosition: SnackPosition.TOP);
    }
  }

  /// 开始登录流程（不需要预先输入手机号）
  Future<void> startLoginProcessWithoutMobile({JlzsProxyConfig? proxy, String? remark}) async {
    print('🚀 开始登录流程（无需预输入手机号）');

    try {
      // 生成临时账号ID
      String accountId = 'jlzs_temp_${DateTime.now().millisecondsSinceEpoch}';
      print('🆔 生成临时账号ID: $accountId');

      // 创建临时账号对象（手机号暂时为空）
      var account = JlzsAccountModel(
        id: accountId,
        mobile: '', // 暂时为空，登录成功后从API获取
        proxy: proxy,
        remark: remark,
      );

      print('📱 创建临时账号对象完成');
      if (proxy != null) {
        print('🔒 代理配置: ${proxy.address}:${proxy.port}');
      }

      addLog('正在启动浏览器，请在浏览器中输入手机号完成登录...');
      print('📝 添加日志: 正在启动浏览器...');

      // 开始登录流程
      print('🔐 调用登录管理器执行登录...');
      var result = await loginManager.startLogin(
        account,
        onStatusUpdate: (status, {error}) {
          addLog('登录状态: $status');
          print('📝 登录状态更新: $status');
          if (error != null) {
            addLog('错误: $error');
            print('❌ 登录错误: $error');
          }
        },
      );

      print('🎯 登录管理器返回结果: $result');

      switch (result) {
        case LoginResult.success:
          // 登录成功后，检查是否有有效的用户信息
          if (account.session?.userInfo['user_id'] != null &&
              account.session?.userInfo['user_id'] != 0) {

            // 从session中获取手机号（可能是脱敏的）
            String? mobile = account.session?.userInfo['mobile'];
            if (mobile == null || mobile.isEmpty) {
              // 如果没有手机号，使用用户名作为标识
              mobile = account.session?.displayName ?? 'user_${account.session?.userInfo['user_id']}';
            }

            account.mobile = mobile;

            // 检查是否已存在相同的用户ID（更准确的重复检查）
            bool exists = accounts.any((a) =>
              a.session?.userInfo['user_id'] == account.session?.userInfo['user_id'] &&
              a.id != account.id
            );

            if (exists) {
              // 如果用户ID已存在，提示用户（简化版本不关闭浏览器）
              addLog('该账号已存在，登录已取消');
              Get.snackbar('提示', '该账号已存在', snackPosition: SnackPosition.TOP);
              return;
            }

            // 添加到账号列表
            accounts.add(account);
            account.status = AccountStatus.active;

            addLog('账号 ${account.displayName} (${account.maskedMobile}) 登录成功并已添加');
            Get.snackbar('成功', '账号登录成功', snackPosition: SnackPosition.TOP);
          } else {
            addLog('无法获取有效的账号信息，登录失败');
            // 简化版本：不关闭浏览器，让用户手动处理
            Get.snackbar('错误', '无法获取账号信息', snackPosition: SnackPosition.TOP);
          }
          break;



        case LoginResult.failed:
        case LoginResult.error:
          addLog('登录失败');
          // 简化版本：不关闭浏览器，让用户手动处理
          Get.snackbar('失败', '登录失败，请检查网络或重试', snackPosition: SnackPosition.TOP);
          break;

        case LoginResult.cancelled:
          addLog('登录被取消');
          // 简化版本：不关闭浏览器，让用户手动处理
          break;
      }

      // 更新UI
      update(['account_stats']);
    } catch (e) {
      addLog('登录过程发生错误: $e');
      Get.snackbar('错误', '登录失败: $e', snackPosition: SnackPosition.TOP);
    }
  }

  /// 开始登录流程
  Future<void> startLoginProcess(JlzsAccountModel account) async {
    try {
      addLog('开始为账号 ${account.maskedMobile} 启动登录流程...');

      var result = await loginManager.startLogin(
        account,
        onStatusUpdate: (status, {error}) {
          addLog('${account.maskedMobile}: $status');
          if (error != null) {
            addLog('错误: $error');
          }
        },
      );

      switch (result) {
        case LoginResult.success:
          account.status = AccountStatus.active;
          addLog('账号 ${account.maskedMobile} 登录成功');
          Get.snackbar('成功', '账号登录成功', snackPosition: SnackPosition.TOP);
          break;

        case LoginResult.failed:
        case LoginResult.error:
          account.status = AccountStatus.error;
          addLog('账号 ${account.maskedMobile} 登录失败');
          Get.snackbar('失败', '登录失败，请检查网络或重试', snackPosition: SnackPosition.TOP);
          break;
        case LoginResult.cancelled:
          account.status = AccountStatus.inactive;
          addLog('账号 ${account.maskedMobile} 登录被取消');
          break;
      }

      update(['account_stats']);
    } catch (e) {
      account.status = AccountStatus.error;
      addLog('登录过程发生错误: $e');
      update(['account_stats']);
    }
  }

  /// 删除账号
  Future<void> removeAccount(String accountId) async {
    try {
      // 找到账号
      var account = accounts.firstWhereOrNull((a) => a.id == accountId);
      if (account == null) return;

      // 关闭浏览器实例
      await browserManager.closeBrowserForAccount(accountId);

      // 从列表中移除
      accounts.removeWhere((a) => a.id == accountId);

      // 更新UI
      update(['account_stats']);

      addLog('账号 ${account.maskedMobile} 已删除');
      Get.snackbar('成功', '账号已删除', snackPosition: SnackPosition.TOP);
    } catch (e) {
      addLog('删除账号失败: $e');
      Get.snackbar('错误', '删除账号失败: $e', snackPosition: SnackPosition.TOP);
    }
  }

  /// 刷新所有账号状态
  Future<void> refreshAllAccountStatus() async {
    try {
      if (accounts.isEmpty) {
        addLog('当前没有账号需要刷新');
        Get.snackbar('提示', '当前没有账号', snackPosition: SnackPosition.TOP);
        return;
      }

      addLog('开始刷新 ${accounts.length} 个账号的状态...');

      // 显示刷新进度
      Get.snackbar('刷新中', '正在检查账号状态...',
        snackPosition: SnackPosition.TOP,
        duration: Duration(seconds: 2),
      );

      var results = await loginManager.batchCheckLoginStatus(accounts.toList());

      int successCount = 0;
      int errorCount = 0;
      for (var entry in results.entries) {
        if (entry.value) {
          successCount++;
        } else {
          // 检查是否是错误状态
          var account = accounts.firstWhereOrNull((a) => a.id == entry.key);
          if (account?.status == AccountStatus.error) {
            errorCount++;
          }
        }
      }

      // 更新UI
      update(['account_stats']);

      String statusMsg = '状态刷新完成：${successCount} 个在线';
      if (errorCount > 0) {
        statusMsg += '，${errorCount} 个错误';
      }
      int offlineCount = accounts.length - successCount - errorCount;
      if (offlineCount > 0) {
        statusMsg += '，${offlineCount} 个离线';
      }

      addLog(statusMsg);
      Get.snackbar('完成', statusMsg, snackPosition: SnackPosition.TOP);
    } catch (e) {
      addLog('刷新账号状态失败: $e');
      Get.snackbar('错误', '刷新失败: $e', snackPosition: SnackPosition.TOP);
    }
  }

  /// 获取账号统计信息
  Map<String, int> getAccountStats() {
    int total = accounts.length;
    int loggedIn = accounts.where((a) => a.isOnline).length;
    int active = accounts.where((a) => a.status == AccountStatus.active).length;

    return {
      'total': total,
      'loggedIn': loggedIn,
      'active': active,
    };
  }

  /// 打开分析窗口
  void openAnalysisWindow() {
    MultiWindowManager().openJlzsAnalysisWindow();
  }

  /// 打开人群画像窗口
  void openProfileWindow() {
    MultiWindowManager().openJlzsProfileWindow();
  }

  /// 开始采集
  void startCollection() async {
    // 执行全面的配置验证
    if (!_validateStartConditions()) {
      return;
    }

    // 清空旧数据
    _clearOldData();

    // 验证通过，输出配置数据供观察
    _outputValidationResult();

    // 生成任务
    if (!await _generateCollectionTasks()) {
      return;
    }

    // 执行任务
    await _executeCollectionTasks();
  }

  /// 清空旧数据
  void _clearOldData() {
    separateData.clear();
    mergedData.clear();
    apiTasks.clear();
    completedTasks.clear();
    failedTasks.clear();
    addLog('🗑️ 已清空旧数据');
  }

  /// 停止采集
  void stopCollection() {
    // TODO: 实现具体的停止逻辑
    startButtonText.value = '开始';
    isStartButtonEnabled.value = true;
    isStopButtonEnabled.value = false;

    addLog('停止数据采集');
    Get.snackbar('提示', '停止功能开发中', snackPosition: SnackPosition.TOP);
  }

  /// 验证开始条件
  bool _validateStartConditions() {
    // 基础必填项验证
    if (!_validateBasicRequirements()) {
      return false;
    }

    // 特殊业务规则验证
    if (!_validateBusinessRules()) {
      return false;
    }

    return true;
  }

  /// 验证基础必填项
  bool _validateBasicRequirements() {
    // 1. 关键词列表验证
    if (config.value.keywords.isEmpty) {
      addLog("❌ 请先添加关键词");
      Get.snackbar('错误', '请先添加关键词', snackPosition: SnackPosition.TOP);
      return false;
    }

    // 2. 时间范围验证
    if (!_validateDateRange()) {
      return false;
    }

    // 3. 地区选择验证
    if (selectedRegions.isEmpty) {
      addLog("❌ 请选择地区");
      Get.snackbar('错误', '请选择地区', snackPosition: SnackPosition.TOP);
      return false;
    }

    // 4. 采集平台验证
    if (config.value.platform.isEmpty) {
      addLog("❌ 请选择采集平台");
      Get.snackbar('错误', '请选择采集平台', snackPosition: SnackPosition.TOP);
      return false;
    }

    // 5. 地区类型验证
    if (config.value.regionType.isEmpty) {
      addLog("❌ 请选择地区类型");
      Get.snackbar('错误', '请选择地区类型', snackPosition: SnackPosition.TOP);
      return false;
    }

    return true;
  }

  /// 验证日期范围
  bool _validateDateRange() {
    try {
      // 检查开始日期和结束日期是否有效
      if (config.value.startDate.isAfter(config.value.endDate)) {
        addLog("❌ 开始日期不能大于结束日期");
        Get.snackbar('错误', '开始日期不能大于结束日期', snackPosition: SnackPosition.TOP);
        return false;
      }
      return true;
    } catch (e) {
      addLog("❌ 日期格式错误");
      Get.snackbar('错误', '日期格式错误', snackPosition: SnackPosition.TOP);
      return false;
    }
  }

  /// 验证特殊业务规则
  bool _validateBusinessRules() {
    // 1. 时间范围限制规则
    if (!_validateTimeRangeRestrictions()) {
      return false;
    }

    // 2. 地区选择冲突规则
    if (!_validateRegionConflictRules()) {
      return false;
    }

    return true;
  }

  /// 验证时间范围限制规则
  bool _validateTimeRangeRestrictions() {
    final hasNational = selectedRegions.contains('全国');
    final hasProvinceOrCity = selectedRegions.any((region) => region != '全国');

    // 规则1: 勾选全国时，开始日期只能从2019-01-01开始
    if (hasNational) {
      final nationalMinDate = DateTime(2019, 1, 1);
      if (config.value.startDate.isBefore(nationalMinDate)) {
        addLog("❌ 选择全国地区时，开始日期不能早于2019年1月1日");
        Get.snackbar('错误', '选择全国地区时，开始日期不能早于2019年1月1日', snackPosition: SnackPosition.TOP);
        return false;
      }
    }

    // 规则2: 勾选省份或城市时，开始日期只能从2022-06-04开始
    if (hasProvinceOrCity && !hasNational) {
      // final provinceCityMinDate = DateTime(2022, 6, 4);
      // if (config.value.startDate.isBefore(provinceCityMinDate)) {
      //   addLog("❌ 选择省份或城市时，开始日期不能早于2022年6月4日");
      //   Get.snackbar('错误', '选择省份或城市时，开始日期不能早于2022年6月4日', snackPosition: SnackPosition.TOP);
      //   return false;
      // }
    }

    return true;
  }

  /// 验证地区选择冲突规则
  bool _validateRegionConflictRules() {
    // 规则: 地区类型选择"合并查询"时，如果勾选全国则不能勾选其他地区
    if (config.value.regionType == 'merged') {
      final hasNational = selectedRegions.contains('全国');
      final hasOtherRegions = selectedRegions.any((region) => region != '全国');

      if (hasNational && hasOtherRegions) {
        addLog("❌ 合并查询模式下，选择全国后不能再选择其他地区");
        Get.snackbar('错误', '合并查询模式下，选择全国后不能再选择其他地区', snackPosition: SnackPosition.TOP);
        return false;
      }
    }

    return true;
  }

  /// 输出验证结果数据供观察
  void _outputValidationResult() {
    // 格式化日期为YYYYMMDD格式
    String formatDate(DateTime date) {
      return '${date.year}${date.month.toString().padLeft(2, '0')}${date.day.toString().padLeft(2, '0')}';
    }

    // 转换平台名称为API格式
    String platformName = config.value.platform;

    // 构建验证结果JSON
    Map<String, dynamic> validationResult = {
      "keyword": config.value.keywords,
      "regions": selectedRegions.toList(),
      "start_date": formatDate(config.value.startDate),
      "end_date": formatDate(config.value.endDate),
      "app_name": platformName,
      "regions_type": config.value.regionType,
    };

    // 输出到日志
    addLog('✅ 验证通过，配置数据如下：');
    addLog('关键词数量: ${config.value.keywords.length}');
    addLog('地区数量: ${selectedRegions.length}');
    addLog('时间范围: ${formatDate(config.value.startDate)} - ${formatDate(config.value.endDate)}');
    addLog('采集平台: ${platformName == 'aweme' ? '抖音' : '头条'}');
    addLog('地区类型: ${config.value.regionType == 'separate' ? '分别查询' : '合并查询'}');

    // 输出完整JSON供开发调试
    print('🔍 JLZS验证结果JSON: $validationResult');
  }

  /// 生成采集任务
  Future<bool> _generateCollectionTasks() async {
    try {
      addLog('正在生成采集任务...');

      // 格式化日期
      String formatDate(DateTime date) {
        return '${date.year}${date.month.toString().padLeft(2, '0')}${date.day.toString().padLeft(2, '0')}';
      }

      // 创建任务生成配置
      final taskConfig = JlzsTaskGenerationConfig(
        keywords: config.value.keywords,
        regions: selectedRegions.toList(),
        startDate: formatDate(config.value.startDate),
        endDate: formatDate(config.value.endDate),
        platform: config.value.platform,
        regionType: config.value.regionType,
      );

      // 验证配置
      if (!JlzsTaskGenerator.validateConfig(taskConfig)) {
        addLog('❌ 任务配置验证失败');
        Get.snackbar('错误', '任务配置验证失败', snackPosition: SnackPosition.TOP);
        return false;
      }

      // 生成任务
      final tasks = JlzsTaskGenerator.generateTasks(taskConfig);
      apiTasks.assignAll(tasks);

      // 生成任务摘要
      final summary = JlzsTaskGenerator.generateTaskSummary(taskConfig);
      addLog('✅ 任务生成完成');
      addLog('总关键词: ${summary['total_keywords']}');
      addLog('关键词组: ${summary['keyword_groups']}');
      addLog('总地区: ${summary['total_regions']}');
      addLog('总任务数: ${summary['total_tasks']}');

      // 生成执行计划
      final availableAccounts = accounts.where((account) =>
        account.status == AccountStatus.active && account.session != null
      ).toList();

      if (availableAccounts.isEmpty) {
        addLog('❌ 没有可用账号，请先添加并登录账号');
        Get.snackbar('错误', '没有可用账号，请先添加并登录账号', snackPosition: SnackPosition.TOP);
        return false;
      }

      final executionPlan = JlzsTaskGenerator.generateExecutionPlan(
        tasks,
        availableAccounts.length,
        config.value.extractInterval
      );

      if (!executionPlan['can_execute']) {
        addLog('❌ ${executionPlan['error']}');
        Get.snackbar('错误', executionPlan['error'], snackPosition: SnackPosition.TOP);
        return false;
      }

      addLog('执行计划: ${executionPlan['concurrency']}个账号并发');
      addLog('预计执行轮数: ${executionPlan['execution_rounds']}');
      addLog('预计总时长: ${executionPlan['estimated_total_time']}秒');

      return true;
    } catch (e) {
      addLog('❌ 生成任务失败: $e');
      Get.snackbar('错误', '生成任务失败: $e', snackPosition: SnackPosition.TOP);
      return false;
    }
  }

  /// 执行采集任务
  Future<void> _executeCollectionTasks() async {
    try {
      // 更新按钮状态
      startButtonText.value = '继续';
      isStartButtonEnabled.value = false;
      isStopButtonEnabled.value = true;

      addLog('开始执行采集任务...');

      // 获取可用账号
      final availableAccounts = accounts.where((account) =>
        account.status == AccountStatus.active && account.session != null
      ).toList();

      // 分配任务给账号
      final taskDistribution = JlzsTaskGenerator.distributeTasksToAccounts(
        apiTasks.toList(),
        availableAccounts.map((account) => account.id).toList()
      );

      // 并发执行任务
      List<Future<void>> executionFutures = [];

      for (int i = 0; i < availableAccounts.length; i++) {
        final account = availableAccounts[i];
        final accountTasks = taskDistribution[i];

        if (accountTasks.isNotEmpty) {
          executionFutures.add(_executeAccountTasks(account, accountTasks));
        }
      }

      // 等待所有任务完成
      await Future.wait(executionFutures);

      // 处理执行结果
      _processExecutionResults();

      addLog('✅ 所有任务执行完成');
      Get.snackbar('完成', '数据采集完成', snackPosition: SnackPosition.TOP);

    } catch (e) {
      addLog('❌ 执行任务失败: $e');
      Get.snackbar('错误', '执行任务失败: $e', snackPosition: SnackPosition.TOP);
    } finally {
      // 恢复按钮状态
      startButtonText.value = '开始';
      isStartButtonEnabled.value = true;
      isStopButtonEnabled.value = false;
    }
  }

  /// 执行单个账号的任务
  Future<void> _executeAccountTasks(JlzsAccountModel account, List<JlzsApiTaskData> tasks) async {
    for (int i = 0; i < tasks.length; i++) {
      final task = tasks[i];

      try {
        addLog('账号${account.maskedMobile} 执行任务 ${i + 1}/${tasks.length}');

        // 设置任务为执行中
        task.setRunning(account.id);

        // 执行API请求
        final result = await JlzsApiClient.executeTask(task, account);

        if (result['success']) {
          // 任务成功
          task.setCompleted(result['data']);
          completedTasks.add(task);
          addLog('✅ 任务完成: ${task.description}');
        } else {
          // 任务失败
          task.setFailed(result['error']);
          failedTasks.add(task);
          addLog('❌ 任务失败: ${task.description} - ${result['error']}');
        }

      } catch (e) {
        task.setFailed(e.toString());
        failedTasks.add(task);
        addLog('❌ 任务异常: ${task.description} - $e');
      }

      // 任务间隔
      if (i < tasks.length - 1) {
        await Future.delayed(Duration(seconds: config.value.extractInterval));
      }
    }
  }

  /// 处理执行结果
  void _processExecutionResults() {
    try {
      addLog('正在处理执行结果...');

      // 清空之前的数据
      separateData.clear();
      mergedData.clear();

      // 统计结果
      int successCount = completedTasks.length;
      int failedCount = failedTasks.length;
      int totalCount = apiTasks.length;

      addLog('执行结果统计:');
      addLog('总任务数: $totalCount');
      addLog('成功: $successCount');
      addLog('失败: $failedCount');
      addLog('成功率: ${(successCount / totalCount * 100).toStringAsFixed(1)}%');

      // 处理成功的任务数据
      for (final task in completedTasks) {
        if (task.responseData != null) {
          _processTaskResponseData(task);
        }
      }

      addLog('数据保存功能待实现');
      addLog('✅ 所有任务执行完成');

    } catch (e) {
      addLog('❌ 处理结果失败: $e');
    }
  }

  /// 处理单个任务的响应数据
  void _processTaskResponseData(JlzsApiTaskData task) {
    try {
      final responseData = task.responseData!;

      if (config.value.regionType == 'separate') {
        // 分开查询模式数据处理
        _processSeparateTaskData(task, responseData);
      } else {
        // 合并查询模式数据处理
        _processMergedTaskData(task, responseData);
      }

    } catch (e) {
      addLog('❌ 处理任务数据失败: ${task.id} - $e');
    }
  }

  /// 处理分开查询模式数据
  void _processSeparateTaskData(JlzsApiTaskData task, Map<String, dynamic> responseData) {
    try {
      addLog('开始处理分开查询数据: ${task.id}');
      addLog('响应数据结构: ${responseData.keys.toList()}');

      // 尝试多种可能的数据结构
      Map<String, dynamic>? data;

      // 方式1: 直接从data字段获取
      if (responseData.containsKey('data')) {
        data = responseData['data'] as Map<String, dynamic>?;
      }

      // 方式2: 如果data为空，尝试从根级别获取
      if (data?.isEmpty ?? true) {
        data = responseData;
      }

      if (data == null || data.isEmpty) {
        addLog('⚠️ 响应数据为空或格式不正确');
        return;
      }

      // 为每个地区创建数据模型
      for (final region in task.regions) {
        JlzsDataModel? regionData = separateData.firstWhereOrNull(
          (item) => item.region == region && item.platform == task.platform
        );

        if (regionData == null) {
          regionData = JlzsDataModel(
            region: region,
            platform: task.platform,
            data: {},
          );
          separateData.add(regionData);
        }

        // 生成日期范围
        final dateRange = _generateDateRange(task.startDate, task.endDate);
        addLog('生成日期范围: ${dateRange.length}天，从${dateRange.first}到${dateRange.last}');

        // 处理巨量指数API的实际数据格式
        _processJlzsApiData(data, regionData, task, dateRange);

        regionData.isCompleted = true;
      }

      addLog('✅ 分开查询数据处理完成');
    } catch (e) {
      addLog('❌ 处理分开查询数据失败: $e');
    }
  }

  /// 处理巨量指数API的实际数据格式
  void _processJlzsApiData(Map<String, dynamic> data, JlzsDataModel regionData, JlzsApiTaskData task, List<String> dateRange) {
    try {
      // 检查是否有hot_list数据
      if (data.containsKey('hot_list') && data['hot_list'] is List) {
        final hotList = data['hot_list'] as List;
        addLog('找到hot_list数据，包含${hotList.length}个关键词');

        for (final keywordItem in hotList) {
          if (keywordItem is Map<String, dynamic>) {
            final keyword = keywordItem['keyword']?.toString() ?? '';
            addLog('处理关键词: $keyword');

            // 处理综合指数数据 (hot_list)
            if (keywordItem.containsKey('hot_list') && keywordItem['hot_list'] is List) {
              final hotData = keywordItem['hot_list'] as List;
              addLog('找到综合指数数据${hotData.length}条');

              // 获取该关键词的搜索指数数据 (search_hot_list)
              List? keywordSearchData;
              if (keywordItem.containsKey('search_hot_list') && keywordItem['search_hot_list'] is List) {
                keywordSearchData = keywordItem['search_hot_list'] as List;
                addLog('找到关键词搜索指数数据${keywordSearchData.length}条');
              }

              // 使用该关键词的搜索指数数据
              _processKeywordData(regionData, keyword, hotData, keywordSearchData, dateRange);
            }
          }
        }
      } else {
        addLog('⚠️ 未找到hot_list数据，数据结构: ${data.keys.toList()}');
      }
    } catch (e) {
      addLog('❌ 处理巨量指数数据失败: $e');
    }
  }

  /// 处理单个关键词的指数数据
  void _processKeywordData(JlzsDataModel regionData, String keyword, List hotData, List? searchData, List<String> dateRange) {
    try {
      // 创建日期到搜索指数的映射
      Map<String, String> searchIndexMap = {};
      if (searchData != null) {
        for (final searchItem in searchData) {
          if (searchItem is Map<String, dynamic>) {
            final datetime = searchItem['datetime']?.toString() ?? '';
            final index = searchItem['index']?.toString() ?? '0';
            if (datetime.isNotEmpty) {
              // 转换日期格式 YYYYMMDD -> YYYY-MM-DD
              final formattedDate = _formatDateFromYYYYMMDD(datetime);
              searchIndexMap[formattedDate] = index;
            }
          }
        }
      }

      // 处理综合指数数据
      for (final hotItem in hotData) {
        if (hotItem is Map<String, dynamic>) {
          final datetime = hotItem['datetime']?.toString() ?? '';
          final hotIndex = hotItem['index']?.toString() ?? '0';

          if (datetime.isNotEmpty) {
            // 转换日期格式 YYYYMMDD -> YYYY-MM-DD
            final formattedDate = _formatDateFromYYYYMMDD(datetime);

            // 获取对应的搜索指数
            final searchIndex = searchIndexMap[formattedDate] ?? '0';

            // 创建关键词数据
            final jlzsKeywordData = JlzsKeywordData(
              keyword: keyword,
              hotIndex: hotIndex,
              searchIndex: searchIndex,
            );

            // 添加到数据模型
            regionData.addKeywordData(formattedDate, jlzsKeywordData);
            addLog('添加数据: $keyword, $formattedDate, 综合指数:$hotIndex, 搜索指数:$searchIndex');
          }
        }
      }

      // 为没有数据的日期填充0值
      for (final date in dateRange) {
        if (regionData.data?[date] == null ||
            !regionData.data![date]!.any((item) => item.keyword == keyword)) {
          final jlzsKeywordData = JlzsKeywordData(
            keyword: keyword,
            hotIndex: '0',
            searchIndex: '0',
          );
          regionData.addKeywordData(date, jlzsKeywordData);
          addLog('填充空数据: $keyword, $date');
        }
      }

    } catch (e) {
      addLog('❌ 处理关键词数据失败: $keyword - $e');
    }
  }

  /// 将YYYYMMDD格式转换为YYYY-MM-DD格式
  String _formatDateFromYYYYMMDD(String yyyymmdd) {
    if (yyyymmdd.length == 8) {
      return '${yyyymmdd.substring(0, 4)}-${yyyymmdd.substring(4, 6)}-${yyyymmdd.substring(6, 8)}';
    }
    return yyyymmdd;
  }

  /// 生成日期范围
  List<String> _generateDateRange(String startDate, String endDate) {
    try {
      // 转换YYYYMMDD格式到DateTime
      DateTime start, end;

      if (startDate.length == 8) {
        start = DateTime.parse('${startDate.substring(0, 4)}-${startDate.substring(4, 6)}-${startDate.substring(6, 8)}');
      } else {
        start = DateTime.parse(startDate);
      }

      if (endDate.length == 8) {
        end = DateTime.parse('${endDate.substring(0, 4)}-${endDate.substring(4, 6)}-${endDate.substring(6, 8)}');
      } else {
        end = DateTime.parse(endDate);
      }

      List<String> dateRange = [];
      DateTime current = start;

      while (!current.isAfter(end)) {
        dateRange.add(DateFormat('yyyy-MM-dd').format(current));
        current = current.add(Duration(days: 1));
      }

      return dateRange;
    } catch (e) {
      addLog('❌ 生成日期范围失败: $e');
      return [];
    }
  }

  /// 处理合并查询模式数据
  void _processMergedTaskData(JlzsApiTaskData task, Map<String, dynamic> responseData) {
    try {
      addLog('开始处理合并查询数据: ${task.id}');

      // 尝试多种可能的数据结构
      Map<String, dynamic>? data;

      if (responseData.containsKey('data')) {
        data = responseData['data'] as Map<String, dynamic>?;
      }

      if (data?.isEmpty ?? true) {
        data = responseData;
      }

      if (data == null || data.isEmpty) {
        addLog('⚠️ 合并查询响应数据为空');
        return;
      }

      // 处理巨量指数API的实际数据格式
      if (data.containsKey('hot_list') && data['hot_list'] is List) {
        final hotList = data['hot_list'] as List;

        for (final keywordItem in hotList) {
          if (keywordItem is Map<String, dynamic>) {
            final keyword = keywordItem['keyword']?.toString() ?? '';

            if (keywordItem.containsKey('hot_list') && keywordItem['hot_list'] is List) {
              final hotData = keywordItem['hot_list'] as List;

              // 获取该关键词的搜索指数数据
              List? keywordSearchData;
              if (keywordItem.containsKey('search_hot_list') && keywordItem['search_hot_list'] is List) {
                keywordSearchData = keywordItem['search_hot_list'] as List;
                addLog('找到关键词搜索指数数据${keywordSearchData.length}条');
              }

              // 处理每个日期的数据，使用该关键词的搜索指数
              _processMergedKeywordData(task, keyword, hotData, keywordSearchData);
            }
          }
        }
      }

      addLog('✅ 合并查询数据处理完成');
    } catch (e) {
      addLog('❌ 处理合并查询数据失败: $e');
    }
  }

  /// 处理合并查询的关键词数据
  void _processMergedKeywordData(JlzsApiTaskData task, String keyword, List hotData, List? searchData) {
    try {
      // 创建搜索指数映射
      Map<String, String> searchIndexMap = {};
      if (searchData != null) {
        for (final searchItem in searchData) {
          if (searchItem is Map<String, dynamic>) {
            final datetime = searchItem['datetime']?.toString() ?? '';
            final index = searchItem['index']?.toString() ?? '0';
            if (datetime.isNotEmpty) {
              searchIndexMap[datetime] = index;
            }
          }
        }
      }

      // 处理每个日期的数据
      for (final hotItem in hotData) {
        if (hotItem is Map<String, dynamic>) {
          final datetime = hotItem['datetime']?.toString() ?? '';
          final hotIndex = hotItem['index']?.toString() ?? '0';
          final searchIndex = searchIndexMap[datetime] ?? '0';

          if (datetime.isNotEmpty) {
            final mergedItem = JlzsMergedData(
              keyword: keyword,
              date: datetime, // 保持YYYYMMDD格式
              platform: task.platform,
              regions: task.regions,
              hotIndex: hotIndex,
              searchIndex: searchIndex,
            );
            mergedData.add(mergedItem);
          }
        }
      }
    } catch (e) {
      addLog('❌ 处理合并查询关键词数据失败: $keyword - $e');
    }
  }

  /// 保存采集结果到文件
  void _saveCollectionResults() async {
    try {
      addLog('开始保存数据到文件...');

      // 1. 处理分开查询数据
      if (config.value.regionType == 'separate') {
        await _saveSeparateQueryResults();
      }

      // 2. 处理合并查询数据
      if (config.value.regionType == 'merged') {
        await _saveMergedQueryResults();
      }

      // 显示完成通知
      Get.snackbar(
        '数据保存成功',
        '已保存所有采集数据，请在运行目录查看！',
        snackPosition: SnackPosition.TOP,
        duration: Duration(seconds: 3),
      );

      addLog('✅ 数据保存完成');
    } catch (e) {
      addLog('❌ 保存数据失败: $e');
    }
  }

  /// 保存分开查询模式的数据
  Future<void> _saveSeparateQueryResults() async {
    if (separateData.isEmpty) {
      addLog('⚠️ 分开查询数据为空，跳过保存');
      return;
    }

    addLog('开始保存分开查询数据...');

    for (final dataModel in separateData) {
      if (dataModel.data == null || dataModel.data!.isEmpty) {
        addLog('⚠️ 地区 ${dataModel.region} 数据为空，跳过');
        continue;
      }

      // 按关键词分组保存
      final keywordGroups = _groupDataByKeyword(dataModel);

      for (final keyword in keywordGroups.keys) {
        try {
          // 创建文件夹：地区/关键词
          final path = await StoreUtil.checkAndCreateFolders(
            dataModel.region ?? '未知地区',
            keyword
          );

          // 按处理周期生成数据
          for (final period in config.value.processingPeriods) {
            final csvData = _generateCsvDataForPeriod(
              keywordGroups[keyword]!,
              dataModel,
              keyword,
              period
            );

            // 生成文件名
            final fileName = _generateFileName(
              dataModel.region,
              dataModel.platform,
              period,
              keyword
            );

            // 导出CSV文件
            await exportCsv(csvData, path, fileName);
            addLog('✅ 保存成功: ${dataModel.region}-$keyword-$period');
          }
        } catch (e) {
          addLog('❌ 保存失败: ${dataModel.region}-$keyword: $e');
        }
      }
    }

    addLog('✅ 分开查询数据保存完成');
  }

  /// 保存合并查询模式的数据
  Future<void> _saveMergedQueryResults() async {
    if (mergedData.isEmpty) {
      addLog('⚠️ 合并查询数据为空，跳过保存');
      return;
    }

    addLog('开始保存合并查询数据...');

    try {
      // 按关键词分组数据
      Map<String, List<JlzsMergedData>> keywordGroups = {};
      for (final item in mergedData) {
        keywordGroups[item.keyword] ??= [];
        keywordGroups[item.keyword]!.add(item);
      }

      // 为每个关键词创建文件夹并保存数据
      for (final keyword in keywordGroups.keys) {
        try {
          // 创建文件夹：合并查询结果/关键词
          final path = await StoreUtil.checkAndCreateFolders('合并查询结果', keyword);

          // 按处理周期生成数据
          for (final period in config.value.processingPeriods) {
            final csvData = _generateMergedCsvDataForKeyword(keywordGroups[keyword]!, keyword, period);

            final fileName = _generateMergedFileNameForKeyword(keyword, period);
            await exportCsv(csvData, path, fileName);
            addLog('✅ 保存成功: 合并查询-$keyword-$period');
          }
        } catch (e) {
          addLog('❌ 保存失败: 合并查询-$keyword: $e');
        }
      }

      addLog('✅ 合并查询数据保存完成');
    } catch (e) {
      addLog('❌ 合并查询数据保存失败: $e');
    }
  }

  /// 按关键词分组数据
  Map<String, Map<String, List<JlzsKeywordData>>> _groupDataByKeyword(JlzsDataModel dataModel) {
    Map<String, Map<String, List<JlzsKeywordData>>> keywordGroups = {};

    if (dataModel.data == null) return keywordGroups;

    // 遍历所有日期的数据
    dataModel.data!.forEach((date, keywordList) {
      for (final keywordData in keywordList) {
        final keyword = keywordData.keyword;

        // 初始化关键词组
        keywordGroups[keyword] ??= {};
        keywordGroups[keyword]![date] ??= [];

        // 添加数据
        keywordGroups[keyword]![date]!.add(keywordData);
      }
    });

    return keywordGroups;
  }

  /// 为指定周期生成CSV数据
  List<List<dynamic>> _generateCsvDataForPeriod(
    Map<String, List<JlzsKeywordData>> keywordData,
    JlzsDataModel dataModel,
    String keyword,
    String period
  ) {
    List<List<dynamic>> csvData = [];

    // 添加表头
    csvData.add([
      "关键词",
      "地区",
      "平台",
      _getPeriodHeader(period),
      "综合指数",
      "搜索指数"
    ]);

    // 根据周期聚合数据
    final aggregatedData = _aggregateDataByPeriod(keywordData, period);

    // 生成CSV行数据
    final sortedKeys = aggregatedData.keys.toList()..sort();
    for (final periodKey in sortedKeys) {
      final aggregatedValues = aggregatedData[periodKey]!;

      csvData.add([
        keyword,
        dataModel.region ?? '未知地区',
        _getPlatformDisplayName(dataModel.platform),
        periodKey,
        aggregatedValues['hotIndex'],
        aggregatedValues['searchIndex']
      ]);
    }

    return csvData;
  }

  /// 为合并查询生成指定周期的CSV数据
  List<List<dynamic>> _generateMergedCsvDataForPeriod(String period) {
    List<List<dynamic>> csvData = [];

    // 添加表头
    csvData.add([
      "关键词",
      _getPeriodHeader(period),
      "平台",
      "地区列表",
      "综合指数",
      "搜索指数"
    ]);

    // 按关键词和时间周期分组数据
    Map<String, Map<String, List<JlzsMergedData>>> groupedData = {};

    for (final item in mergedData) {
      final keyword = item.keyword;
      final periodKey = _convertDateToPeriod(item.date, period);

      groupedData[keyword] ??= {};
      groupedData[keyword]![periodKey] ??= [];
      groupedData[keyword]![periodKey]!.add(item);
    }

    // 生成CSV行数据
    final sortedKeywords = groupedData.keys.toList()..sort();
    for (final keyword in sortedKeywords) {
      final keywordData = groupedData[keyword]!;
      final sortedPeriods = keywordData.keys.toList()..sort();

      for (final periodKey in sortedPeriods) {
        final periodItems = keywordData[periodKey]!;

        // 聚合同一周期的数据
        final aggregatedValues = _aggregateMergedData(periodItems);

        // 获取地区列表
        final regions = periodItems.first.regions.join(', ');

        csvData.add([
          keyword,
          periodKey,
          _getPlatformDisplayName(periodItems.first.platform),
          regions,
          aggregatedValues['hotIndex'],
          aggregatedValues['searchIndex']
        ]);
      }
    }

    return csvData;
  }

  /// 为单个关键词生成合并查询的CSV数据
  List<List<dynamic>> _generateMergedCsvDataForKeyword(List<JlzsMergedData> keywordData, String keyword, String period) {
    List<List<dynamic>> csvData = [];

    // 添加表头
    csvData.add([
      "关键词",
      _getPeriodHeader(period),
      "平台",
      "地区列表",
      "综合指数",
      "搜索指数"
    ]);

    // 按时间周期分组数据
    Map<String, List<JlzsMergedData>> groupedData = {};

    for (final item in keywordData) {
      final periodKey = _convertDateToPeriod(item.date, period);

      groupedData[periodKey] ??= [];
      groupedData[periodKey]!.add(item);
    }

    // 生成CSV行数据
    final sortedPeriods = groupedData.keys.toList()..sort();

    for (final periodKey in sortedPeriods) {
      final periodItems = groupedData[periodKey]!;

      // 聚合同一周期的数据
      final aggregatedValues = _aggregateMergedData(periodItems);

      // 获取地区列表
      final regions = periodItems.first.regions.join(', ');

      csvData.add([
        keyword,
        periodKey,
        _getPlatformDisplayName(periodItems.first.platform),
        regions,
        aggregatedValues['hotIndex'],
        aggregatedValues['searchIndex']
      ]);
    }

    return csvData;
  }

  /// 按周期聚合数据
  Map<String, Map<String, String>> _aggregateDataByPeriod(
    Map<String, List<JlzsKeywordData>> keywordData,
    String period
  ) {
    Map<String, Map<String, String>> aggregatedData = {};
    Map<String, int> dataCountMap = {}; // 记录每个周期的数据天数

    keywordData.forEach((date, dataList) {
      final periodKey = _convertDateToPeriod(date, period);

      // 初始化周期数据
      aggregatedData[periodKey] ??= {'hotIndex': '0', 'searchIndex': '0'};
      dataCountMap[periodKey] ??= 0;

      // 聚合数据
      for (final data in dataList) {
        final currentHot = double.tryParse(aggregatedData[periodKey]!['hotIndex']!) ?? 0;
        final currentSearch = double.tryParse(aggregatedData[periodKey]!['searchIndex']!) ?? 0;
        final newHot = double.tryParse(data.hotIndex) ?? 0;
        final newSearch = double.tryParse(data.searchIndex) ?? 0;

        // 累加数据
        aggregatedData[periodKey]!['hotIndex'] = (currentHot + newHot).toString();
        aggregatedData[periodKey]!['searchIndex'] = (currentSearch + newSearch).toString();
      }

      // 记录数据天数
      dataCountMap[periodKey] = dataCountMap[periodKey]! + 1;
    });

    // 根据聚合方式处理数据
    if (config.value.aggregationMethod == 'average') {
      aggregatedData.forEach((periodKey, values) {
        final dataCount = dataCountMap[periodKey]!;
        final hotSum = double.tryParse(values['hotIndex']!) ?? 0;
        final searchSum = double.tryParse(values['searchIndex']!) ?? 0;

        // 计算平均值
        double divisor;
        if (period == 'year') {
          // 年平均：根据具体年份计算天数
          divisor = _getActualDaysInYear(periodKey).toDouble();
        } else if (period == 'month') {
          // 月平均：根据具体年月计算天数
          divisor = _getActualDaysInMonth(periodKey).toDouble();
        } else if (period == 'week') {
          // 周平均：计算实际周天数（通常是7天，但可能有特殊情况）
          divisor = _getActualDaysInWeek(periodKey).toDouble();
        } else {
          // 日平均：除以实际数据天数
          divisor = dataCount.toDouble();
        }

        values['hotIndex'] = (hotSum / divisor).toStringAsFixed(2);
        values['searchIndex'] = (searchSum / divisor).toStringAsFixed(2);
      });
    }

    return aggregatedData;
  }

  /// 获取指定年份的实际天数
  int _getActualDaysInYear(String yearStr) {
    try {
      final year = int.parse(yearStr);
      // 判断是否为闰年
      if ((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0)) {
        return 366; // 闰年
      } else {
        return 365; // 平年
      }
    } catch (e) {
      return 365; // 默认返回365天
    }
  }

  /// 获取指定年月的实际天数
  int _getActualDaysInMonth(String yearMonthStr) {
    try {
      // yearMonthStr格式可能是 "2024-01" 或 "2024年1月" 等
      final parts = yearMonthStr.replaceAll('年', '-').replaceAll('月', '').split('-');
      if (parts.length >= 2) {
        final year = int.parse(parts[0]);
        final month = int.parse(parts[1]);

        // 使用DateTime计算该月的实际天数
        final nextMonth = month == 12 ? DateTime(year + 1, 1, 1) : DateTime(year, month + 1, 1);
        final currentMonth = DateTime(year, month, 1);
        return nextMonth.difference(currentMonth).inDays;
      }
    } catch (e) {
      // 解析失败时的处理
    }
    return 30; // 默认返回30天
  }

  /// 获取指定周的实际天数
  int _getActualDaysInWeek(String weekStr) {
    try {
      // weekStr格式: "2024-01-01 ~ 2024-01-07"
      if (weekStr.contains(' ~ ')) {
        final parts = weekStr.split(' ~ ');
        if (parts.length == 2) {
          final startDate = DateTime.parse(parts[0]);
          final endDate = DateTime.parse(parts[1]);
          return endDate.difference(startDate).inDays + 1; // +1 因为包含结束日期
        }
      }
    } catch (e) {
      // 解析失败时的处理
    }
    return 7; // 默认返回7天
  }

  /// 聚合合并查询数据
  Map<String, String> _aggregateMergedData(List<JlzsMergedData> items) {
    double totalHot = 0;
    double totalSearch = 0;

    for (final item in items) {
      totalHot += double.tryParse(item.hotIndex) ?? 0;
      totalSearch += double.tryParse(item.searchIndex) ?? 0;
    }

    if (config.value.aggregationMethod == 'average' && items.isNotEmpty) {
      totalHot /= items.length;
      totalSearch /= items.length;
    }

    return {
      'hotIndex': totalHot.toStringAsFixed(2),
      'searchIndex': totalSearch.toStringAsFixed(2)
    };
  }

  /// 将日期转换为指定周期的键
  String _convertDateToPeriod(String date, String period) {
    try {
      DateTime dateTime;

      // 处理不同的日期格式
      if (date.length == 8) {
        // YYYYMMDD格式
        dateTime = DateTime.parse('${date.substring(0, 4)}-${date.substring(4, 6)}-${date.substring(6, 8)}');
      } else {
        // YYYY-MM-DD格式
        dateTime = DateTime.parse(date);
      }

      switch (period) {
        case 'day':
          return DateFormat('yyyy-MM-dd').format(dateTime);
        case 'week':
          // 计算周的开始日期（周一）
          final weekStart = dateTime.subtract(Duration(days: dateTime.weekday - 1));
          final weekEnd = weekStart.add(Duration(days: 6));
          return '${DateFormat('yyyy-MM-dd').format(weekStart)} ~ ${DateFormat('yyyy-MM-dd').format(weekEnd)}';
        case 'month':
          return DateFormat('yyyy-MM').format(dateTime);
        case 'year':
          return DateFormat('yyyy').format(dateTime);
        default:
          return date;
      }
    } catch (e) {
      return date; // 如果解析失败，返回原始日期
    }
  }

  /// 获取周期表头名称
  String _getPeriodHeader(String period) {
    switch (period) {
      case 'day':
        return '日期';
      case 'week':
        return '周次';
      case 'month':
        return '月份';
      case 'year':
        return '年份';
      default:
        return '时间';
    }
  }

  /// 获取平台显示名称
  String _getPlatformDisplayName(String platform) {
    switch (platform) {
      case 'aweme':
        return '抖音';
      case 'toutiao':
        return '头条';
      default:
        return platform;
    }
  }

  /// 生成分开查询的文件名
  String _generateFileName(String? region, String platform, String period, String keyword) {
    final startDate = DateFormat('yyyyMMdd').format(config.value.startDate);
    final endDate = DateFormat('yyyyMMdd').format(config.value.endDate);
    final platformName = _getPlatformDisplayName(platform);
    final periodName = _getPeriodDisplayName(period);

    return '${region ?? '未知地区'}-$platformName-$periodName-$keyword-$startDate-$endDate';
  }

  /// 生成合并查询的文件名
  String _generateMergedFileName(String period) {
    final startDate = DateFormat('yyyyMMdd').format(config.value.startDate);
    final endDate = DateFormat('yyyyMMdd').format(config.value.endDate);
    final platformName = _getPlatformDisplayName(config.value.platform);
    final periodName = _getPeriodDisplayName(period);

    return '合并查询-$platformName-$periodName-$startDate-$endDate';
  }

  /// 生成合并查询按关键词分组的文件名
  String _generateMergedFileNameForKeyword(String keyword, String period) {
    final startDate = DateFormat('yyyyMMdd').format(config.value.startDate);
    final endDate = DateFormat('yyyyMMdd').format(config.value.endDate);
    final platformName = _getPlatformDisplayName(config.value.platform);
    final periodName = _getPeriodDisplayName(period);

    return '合并查询-$platformName-$periodName-$keyword-$startDate-$endDate';
  }

  /// 获取周期显示名称
  String _getPeriodDisplayName(String period) {
    switch (period) {
      case 'day':
        return '日';
      case 'week':
        return '周';
      case 'month':
        return '月';
      case 'year':
        return '年';
      default:
        return period;
    }
  }

  /// CSV导出方法（复用百度指数的实现）
  Future<void> exportCsv(List<List<dynamic>> csvData, String path, String fileName) async {
    try {
      String csv = const ListToCsvConverter().convert(csvData);

      // 添加 UTF-8 BOM
      List<int> bom = [0xEF, 0xBB, 0xBF];
      List<int> csvBytes = utf8.encode(csv);
      List<int> bomCsvBytes = bom + csvBytes;

      // 组合路径和文件名
      final File file = File('$path/$fileName.csv');

      // 如果文件已存在，先删除
      if (await file.exists()) {
        await file.delete();
        addLog('🗑️ 删除已存在文件: $fileName.csv');
      }

      // 将CSV数据写入文件
      await file.writeAsBytes(bomCsvBytes);
      addLog('💾 文件保存成功: $fileName.csv (${csvData.length}行数据)');
    } catch (e) {
      addLog('❌ CSV导出失败: $fileName - $e');
      rethrow;
    }
  }

  /// 检查所有账号的msToken有效期
  void checkAllMsTokens() {
    if (accounts.isEmpty) return;

    print('🔍 开始检查所有账号的msToken有效期...');

    var results = loginManager.batchCheckMsTokenValidity(accounts);
    List<JlzsAccountModel> expiredAccounts = [];
    List<JlzsAccountModel> soonExpiredAccounts = [];

    for (var account in accounts) {
      bool isValid = results[account.id] ?? false;

      if (!isValid) {
        // 检查是否已过期
        int? expiryTimestamp = account.session?.userInfo['msTokenExpiry'];
        if (expiryTimestamp != null) {
          DateTime expiryDate = DateTime.fromMillisecondsSinceEpoch(expiryTimestamp);
          if (DateTime.now().isAfter(expiryDate)) {
            expiredAccounts.add(account);
          } else {
            soonExpiredAccounts.add(account);
          }
        }
      }
    }

    // 处理过期账号
    if (expiredAccounts.isNotEmpty) {
      addLog('⚠️ 发现 ${expiredAccounts.length} 个账号的msToken已过期');
      for (var account in expiredAccounts) {
        addLog('- ${account.displayName}: msToken已过期');
      }
    }

    // 处理即将过期账号
    if (soonExpiredAccounts.isNotEmpty) {
      addLog('⚠️ 发现 ${soonExpiredAccounts.length} 个账号的msToken即将过期');
      for (var account in soonExpiredAccounts) {
        int? expiryTimestamp = account.session?.userInfo['msTokenExpiry'];
        if (expiryTimestamp != null) {
          DateTime expiryDate = DateTime.fromMillisecondsSinceEpoch(expiryTimestamp);
          int hoursLeft = expiryDate.difference(DateTime.now()).inHours;
          addLog('- ${account.displayName}: 剩余 $hoursLeft 小时');
        }
      }
    }

    if (expiredAccounts.isEmpty && soonExpiredAccounts.isEmpty) {
      addLog('✅ 所有账号的msToken都有效');
    }
  }

  /// 刷新指定账号的msToken
  Future<void> refreshAccountMsToken(JlzsAccountModel account) async {
    try {
      addLog('🔄 正在刷新账号 ${account.displayName} 的msToken...');

      bool success = await loginManager.refreshMsToken(account);

      if (success) {
        addLog('✅ 账号 ${account.displayName} 的msToken刷新成功');
        accounts.refresh(); // 更新UI
      } else {
        addLog('❌ 账号 ${account.displayName} 的msToken刷新失败');
      }
    } catch (e) {
      addLog('❌ 刷新msToken失败: $e');
    }
  }

  /// 批量刷新所有过期账号的msToken
  Future<void> refreshAllExpiredMsTokens() async {
    var results = loginManager.batchCheckMsTokenValidity(accounts);
    List<JlzsAccountModel> needRefreshAccounts = [];

    for (var account in accounts) {
      bool isValid = results[account.id] ?? false;
      if (!isValid) {
        needRefreshAccounts.add(account);
      }
    }

    if (needRefreshAccounts.isEmpty) {
      addLog('✅ 没有需要刷新msToken的账号');
      return;
    }

    addLog('🔄 开始批量刷新 ${needRefreshAccounts.length} 个账号的msToken...');

    for (var account in needRefreshAccounts) {
      await refreshAccountMsToken(account);
      // 添加延迟避免请求过快
      await Future.delayed(Duration(seconds: 1));
    }

    addLog('✅ 批量刷新msToken完成');
  }

  /// 保存数据
  void saveConfig() {
    // 检查是否有数据可以保存
    if (separateData.isEmpty && mergedData.isEmpty) {
      addLog('⚠️ 没有可保存的数据，请先执行采集任务');
      Get.snackbar('提示', '没有可保存的数据，请先执行采集任务', snackPosition: SnackPosition.TOP);
      return;
    }

    // 执行数据保存
    _saveCollectionResults();
  }

  /// 添加日志
  void addLog(String message) {
    final timestamp = DateTime.now().toString().substring(11, 19);
    print(message);
    taskLogs.add('[$timestamp] $message');

    // 如果超过500条，删除最老的日志
    if (taskLogs.length > 500) {
      taskLogs.removeRange(0, taskLogs.length - 500);
    }

    update(['task_logs']);

    // 自动滚动到最新日志
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (logScrollController.hasClients) {
        logScrollController.animateTo(
          logScrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  /// 清空日志
  void clearLogs() {
    taskLogs.clear();
    update(['task_logs']);
  }

  /// 关闭窗口
  void closeWindow() {
    if (isWindowClosing.value) return;

    isWindowClosing.value = true;

    // 通知主窗口
    _notifyMainWindow('windowClosed', {
      'windowType': 'jlzs',
      'timestamp': DateTime.now().toIso8601String(),
    });

    // 关闭窗口
    if (windowController != null) {
      windowController!.close();
    } else {
      SystemNavigator.pop();
    }
  }

  /// 通知主窗口
  void _notifyMainWindow(String action, Map<String, dynamic> data) {
    DesktopMultiWindow.invokeMethod(
      0, // 主窗口ID
      'jlzsAction',
      {
        'action': action,
        'data': data,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }


}
