import 'package:get/get.dart';
import 'package:desktop_multi_window/desktop_multi_window.dart';
import 'jlzs_analysis_controller.dart';

/// 巨量指数分析页面绑定
class JlzsAnalysisBinding extends Bindings {
  final WindowController? windowController;

  JlzsAnalysisBinding({this.windowController});

  @override
  void dependencies() {
    Get.lazyPut<JlzsAnalysisController>(() => JlzsAnalysisController(windowController: windowController));
  }
}
