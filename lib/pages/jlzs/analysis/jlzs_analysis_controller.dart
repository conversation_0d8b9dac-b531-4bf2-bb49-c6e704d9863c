import 'package:get/get.dart';
import 'package:desktop_multi_window/desktop_multi_window.dart';
import 'package:flutter/services.dart';

/// 巨量指数分析控制器
class JlzsAnalysisController extends GetxController {
  // 分析状态
  final isLoading = false.obs;
  final selectedDateRange = '最近7天'.obs;
  final chartType = '柱状图'.obs;
  final dataSource = '巨量指数'.obs;
  final selectedKeywords = <String>[].obs;
  final selectedRegions = <String>[].obs;
  
  // 模拟分析数据
  final analysisData = <Map<String, dynamic>>[].obs;
  final chartData = <Map<String, dynamic>>[].obs;
  final summaryData = <String, dynamic>{}.obs;
  
  // 窗口相关
  final isWindowClosing = false.obs;
  WindowController? windowController;

  JlzsAnalysisController({this.windowController});

  @override
  void onInit() {
    super.onInit();
    _loadAnalysisData();
  }

  /// 加载分析数据
  void _loadAnalysisData() {
    isLoading.value = true;
    
    // 模拟数据加载
    Future.delayed(const Duration(seconds: 1), () {
      _generateMockData();
      isLoading.value = false;
    });
  }

  /// 生成模拟数据
  void _generateMockData() {
    // 生成模拟的分析数据
    analysisData.value = List.generate(10, (index) => {
      'keyword': '关键词${index + 1}',
      'region': '地区${index + 1}',
      'views': (1000 + index * 500),
      'engagement': (50 + index * 10).toDouble(),
      'trend': index % 2 == 0 ? 'up' : 'down',
      'date': DateTime.now().subtract(Duration(days: index)).toIso8601String(),
    });

    // 生成图表数据
    chartData.value = List.generate(7, (index) => {
      'date': DateTime.now().subtract(Duration(days: 6 - index)).day.toString(),
      'value': 1000 + (index * 200) + (index % 3 * 100),
    });

    // 生成汇总数据
    summaryData.value = {
      'totalViews': 125000,
      'avgEngagement': 78.5,
      'topKeyword': '热门关键词1',
      'topRegion': '北京',
      'growthRate': 15.6,
    };

    update();
  }

  /// 切换日期范围
  void changeDateRange(String range) {
    selectedDateRange.value = range;
    _loadAnalysisData();
    update();
  }

  /// 切换图表类型
  void changeChartType(String type) {
    chartType.value = type;
    update();
  }

  /// 切换数据源
  void changeDataSource(String source) {
    dataSource.value = source;
    _loadAnalysisData();
    update();
  }

  /// 添加关键词筛选
  void addKeywordFilter(String keyword) {
    if (!selectedKeywords.contains(keyword)) {
      selectedKeywords.add(keyword);
      _loadAnalysisData();
    }
  }

  /// 移除关键词筛选
  void removeKeywordFilter(String keyword) {
    selectedKeywords.remove(keyword);
    _loadAnalysisData();
  }

  /// 添加地区筛选
  void addRegionFilter(String region) {
    if (!selectedRegions.contains(region)) {
      selectedRegions.add(region);
      _loadAnalysisData();
    }
  }

  /// 移除地区筛选
  void removeRegionFilter(String region) {
    selectedRegions.remove(region);
    _loadAnalysisData();
  }

  /// 导出分析报告
  void exportReport() {
    Get.snackbar(
      '导出成功',
      '分析报告已导出到本地文件',
      snackPosition: SnackPosition.TOP,
    );
    
    // 通知主窗口
    _notifyMainWindow('reportExported', {
      'fileName': 'jlzs_analysis_${DateTime.now().millisecondsSinceEpoch}.pdf',
      'recordCount': analysisData.length,
      'dateRange': selectedDateRange.value,
    });
  }

  /// 刷新数据
  void refreshData() {
    _loadAnalysisData();
    Get.snackbar(
      '刷新完成',
      '分析数据已更新',
      snackPosition: SnackPosition.TOP,
    );
  }

  /// 生成深度分析
  void generateDeepAnalysis() {
    Get.snackbar(
      '分析中',
      '正在生成深度分析报告...',
      snackPosition: SnackPosition.TOP,
    );
    
    // 模拟深度分析
    Future.delayed(const Duration(seconds: 2), () {
      Get.snackbar(
        '分析完成',
        '深度分析报告已生成',
        snackPosition: SnackPosition.TOP,
      );
    });
  }

  /// 设置分析参数
  void setAnalysisParams(Map<String, dynamic> params) {
    // 更新分析参数
    if (params.containsKey('keywords')) {
      selectedKeywords.value = List<String>.from(params['keywords']);
    }
    if (params.containsKey('regions')) {
      selectedRegions.value = List<String>.from(params['regions']);
    }
    if (params.containsKey('dateRange')) {
      selectedDateRange.value = params['dateRange'];
    }
    
    _loadAnalysisData();
  }

  /// 获取分析摘要
  Map<String, dynamic> getAnalysisSummary() {
    return {
      'totalRecords': analysisData.length,
      'dateRange': selectedDateRange.value,
      'keywordCount': selectedKeywords.length,
      'regionCount': selectedRegions.length,
      'chartType': chartType.value,
      'lastUpdated': DateTime.now().toIso8601String(),
    };
  }

  /// 通知主窗口
  void _notifyMainWindow(String action, Map<String, dynamic> data) {
    DesktopMultiWindow.invokeMethod(
      0, // 主窗口ID
      'jlzsAction',
      {
        'action': action,
        'data': data,
        'source': 'analysis',
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  /// 关闭窗口
  void closeWindow() {
    if (isWindowClosing.value) return;

    isWindowClosing.value = true;

    // 通知主窗口
    _notifyMainWindow('windowClosed', {
      'windowType': 'jlzsAnalysis',
      'summary': getAnalysisSummary(),
    });

    // 关闭窗口
    if (windowController != null) {
      windowController!.close();
    } else {
      SystemNavigator.pop();
    }
  }
}
