import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:desktop_multi_window/desktop_multi_window.dart';
import 'jlzs_analysis_controller.dart';

/// 巨量指数分析页面
class JlzsAnalysisPage extends StatelessWidget {
  final WindowController? windowController;

  const JlzsAnalysisPage({Key? key, this.windowController}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<JlzsAnalysisController>();

    return Scaffold(
      appBar: AppBar(
        title: const Text('巨量指数分析'),
        backgroundColor: Colors.orange.shade50,
        foregroundColor: Colors.orange.shade800,
        elevation: 1,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: controller.refreshData,
            tooltip: '刷新数据',
          ),
          IconButton(
            icon: const Icon(Icons.download),
            onPressed: controller.exportReport,
            tooltip: '导出报告',
          ),
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: controller.closeWindow,
            tooltip: '关闭窗口',
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // 控制面板
            _buildControlPanel(controller),
            const SizedBox(height: 16),
            
            // 主要内容区域
            Expanded(
              child: Row(
                children: [
                  // 左侧：数据摘要和筛选
                  Expanded(
                    flex: 1,
                    child: _buildLeftPanel(controller),
                  ),
                  const SizedBox(width: 16),
                  
                  // 右侧：图表和详细数据
                  Expanded(
                    flex: 2,
                    child: _buildRightPanel(controller),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建控制面板
  Widget _buildControlPanel(JlzsAnalysisController controller) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Row(
        children: [
          // 日期范围选择
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('时间范围', style: TextStyle(fontSize: 12, fontWeight: FontWeight.w500)),
                const SizedBox(height: 4),
                Obx(() => DropdownButton<String>(
                  value: controller.selectedDateRange.value,
                  isExpanded: true,
                  items: ['最近7天', '最近30天', '最近90天', '自定义'].map((range) {
                    return DropdownMenuItem(value: range, child: Text(range, style: const TextStyle(fontSize: 12)));
                  }).toList(),
                  onChanged: (value) => controller.changeDateRange(value!),
                )),
              ],
            ),
          ),
          const SizedBox(width: 16),
          
          // 图表类型选择
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('图表类型', style: TextStyle(fontSize: 12, fontWeight: FontWeight.w500)),
                const SizedBox(height: 4),
                Obx(() => DropdownButton<String>(
                  value: controller.chartType.value,
                  isExpanded: true,
                  items: ['柱状图', '折线图', '饼图', '散点图'].map((type) {
                    return DropdownMenuItem(value: type, child: Text(type, style: const TextStyle(fontSize: 12)));
                  }).toList(),
                  onChanged: (value) => controller.changeChartType(value!),
                )),
              ],
            ),
          ),
          const SizedBox(width: 16),
          
          // 操作按钮
          ElevatedButton.icon(
            onPressed: controller.generateDeepAnalysis,
            icon: const Icon(Icons.analytics, size: 16),
            label: const Text('深度分析', style: TextStyle(fontSize: 12)),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange.shade600,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建左侧面板
  Widget _buildLeftPanel(JlzsAnalysisController controller) {
    return Column(
      children: [
        // 数据摘要
        _buildSummaryCard(controller),
        const SizedBox(height: 16),
        
        // 筛选器
        Expanded(child: _buildFilters(controller)),
      ],
    );
  }

  /// 构建数据摘要卡片
  Widget _buildSummaryCard(JlzsAnalysisController controller) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.summarize, color: Colors.blue.shade600, size: 20),
              const SizedBox(width: 8),
              const Text('数据摘要', style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600)),
            ],
          ),
          const SizedBox(height: 12),
          Obx(() => Column(
            children: [
              _buildSummaryRow('总浏览量', '${controller.summaryData['totalViews'] ?? 0}'),
              _buildSummaryRow('平均参与度', '${controller.summaryData['avgEngagement'] ?? 0}%'),
              _buildSummaryRow('热门关键词', controller.summaryData['topKeyword'] ?? '-'),
              _buildSummaryRow('热门地区', controller.summaryData['topRegion'] ?? '-'),
              _buildSummaryRow('增长率', '+${controller.summaryData['growthRate'] ?? 0}%'),
            ],
          )),
        ],
      ),
    );
  }

  /// 构建摘要行
  Widget _buildSummaryRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: TextStyle(fontSize: 12, color: Colors.grey.shade600)),
          Text(value, style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500)),
        ],
      ),
    );
  }

  /// 构建筛选器
  Widget _buildFilters(JlzsAnalysisController controller) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.filter_list, color: Colors.grey.shade600, size: 20),
              const SizedBox(width: 8),
              const Text('数据筛选', style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600)),
            ],
          ),
          const SizedBox(height: 16),
          
          // 关键词筛选
          const Text('关键词筛选', style: TextStyle(fontSize: 12, fontWeight: FontWeight.w500)),
          const SizedBox(height: 8),
          Obx(() => Wrap(
            spacing: 4,
            runSpacing: 4,
            children: controller.selectedKeywords.map((keyword) {
              return Chip(
                label: Text(keyword, style: const TextStyle(fontSize: 10)),
                deleteIcon: const Icon(Icons.close, size: 14),
                onDeleted: () => controller.removeKeywordFilter(keyword),
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              );
            }).toList(),
          )),
          
          const SizedBox(height: 16),
          
          // 地区筛选
          const Text('地区筛选', style: TextStyle(fontSize: 12, fontWeight: FontWeight.w500)),
          const SizedBox(height: 8),
          Obx(() => Wrap(
            spacing: 4,
            runSpacing: 4,
            children: controller.selectedRegions.map((region) {
              return Chip(
                label: Text(region, style: const TextStyle(fontSize: 10)),
                deleteIcon: const Icon(Icons.close, size: 14),
                onDeleted: () => controller.removeRegionFilter(region),
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              );
            }).toList(),
          )),
        ],
      ),
    );
  }

  /// 构建右侧面板
  Widget _buildRightPanel(JlzsAnalysisController controller) {
    return Column(
      children: [
        // 图表区域
        Expanded(
          flex: 2,
          child: _buildChartArea(controller),
        ),
        const SizedBox(height: 16),
        
        // 数据表格
        Expanded(
          flex: 1,
          child: _buildDataTable(controller),
        ),
      ],
    );
  }

  /// 构建图表区域
  Widget _buildChartArea(JlzsAnalysisController controller) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.bar_chart, color: Colors.orange.shade600, size: 20),
              const SizedBox(width: 8),
              Obx(() => Text(
                '数据图表 - ${controller.chartType.value}',
                style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
              )),
            ],
          ),
          const SizedBox(height: 16),
          Expanded(
            child: Obx(() {
              if (controller.isLoading.value) {
                return const Center(child: CircularProgressIndicator());
              }
              
              // 这里应该是实际的图表组件，现在用占位符代替
              return Container(
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.insert_chart, size: 64, color: Colors.grey.shade400),
                      const SizedBox(height: 8),
                      Text(
                        '${controller.chartType.value}图表',
                        style: TextStyle(color: Colors.grey.shade600),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '数据点: ${controller.chartData.length}',
                        style: TextStyle(fontSize: 12, color: Colors.grey.shade500),
                      ),
                    ],
                  ),
                ),
              );
            }),
          ),
        ],
      ),
    );
  }

  /// 构建数据表格
  Widget _buildDataTable(JlzsAnalysisController controller) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.table_chart, color: Colors.orange.shade600, size: 20),
              const SizedBox(width: 8),
              const Text('详细数据', style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600)),
            ],
          ),
          const SizedBox(height: 16),
          Expanded(
            child: Obx(() {
              if (controller.isLoading.value) {
                return const Center(child: CircularProgressIndicator());
              }
              
              if (controller.analysisData.isEmpty) {
                return const Center(child: Text('暂无数据'));
              }
              
              return SingleChildScrollView(
                child: DataTable(
                  columnSpacing: 20,
                  columns: const [
                    DataColumn(label: Text('关键词', style: TextStyle(fontSize: 12, fontWeight: FontWeight.w600))),
                    DataColumn(label: Text('地区', style: TextStyle(fontSize: 12, fontWeight: FontWeight.w600))),
                    DataColumn(label: Text('浏览量', style: TextStyle(fontSize: 12, fontWeight: FontWeight.w600))),
                    DataColumn(label: Text('参与度', style: TextStyle(fontSize: 12, fontWeight: FontWeight.w600))),
                    DataColumn(label: Text('趋势', style: TextStyle(fontSize: 12, fontWeight: FontWeight.w600))),
                  ],
                  rows: controller.analysisData.take(10).map((data) {
                    return DataRow(
                      cells: [
                        DataCell(Text(data['keyword'], style: const TextStyle(fontSize: 11))),
                        DataCell(Text(data['region'], style: const TextStyle(fontSize: 11))),
                        DataCell(Text(data['views'].toString(), style: const TextStyle(fontSize: 11))),
                        DataCell(Text('${data['engagement']}%', style: const TextStyle(fontSize: 11))),
                        DataCell(
                          Icon(
                            data['trend'] == 'up' ? Icons.trending_up : Icons.trending_down,
                            size: 16,
                            color: data['trend'] == 'up' ? Colors.green : Colors.red,
                          ),
                        ),
                      ],
                    );
                  }).toList(),
                ),
              );
            }),
          ),
        ],
      ),
    );
  }
}
