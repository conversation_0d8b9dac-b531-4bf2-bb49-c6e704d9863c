import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:desktop_multi_window/desktop_multi_window.dart';
import 'jlzs_analysis_page.dart';
import 'jlzs_analysis_binding.dart';

/// 巨量指数分析窗口应用
class JlzsAnalysisWindowApp extends StatelessWidget {
  final Map<String, dynamic> args;
  final WindowController? windowController;

  const JlzsAnalysisWindowApp({
    Key? key,
    required this.args,
    this.windowController,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      title: '巨量指数分析',
      theme: ThemeData(
        fontFamily: "Alibaba",
        primarySwatch: Colors.orange,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      home: JlzsAnalysisPage(windowController: windowController),
      initialBinding: JlzsAnalysisBinding(windowController: windowController),
      debugShowCheckedModeBanner: false,
    );
  }
}
