import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../model/jlzs_account_new_model.dart';
import '../jlzs_logic.dart';

/// 账号管理弹窗 (新版本)
class AccountManageDialog extends StatefulWidget {
  const AccountManageDialog({Key? key}) : super(key: key);

  @override
  State<AccountManageDialog> createState() => _AccountManageDialogState();
}

class _AccountManageDialogState extends State<AccountManageDialog> {
  final JlzsLogic logic = Get.find<JlzsLogic>();
  final selectedAccounts = <String>{}.obs;
  final isSelectMode = false.obs;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: 700,
        height: 600,
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题栏
            Row(
              children: [
                Icon(Icons.manage_accounts, color: Colors.purple.shade600, size: 24),
                const SizedBox(width: 12),
                const Text(
                  '巨量指数账号管理',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Get.back(),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // 操作按钮栏
            Obx(() => Row(
              children: [
                ElevatedButton.icon(
                  onPressed: _showAddAccountDialog,
                  icon: const Icon(Icons.add, size: 18),
                  label: const Text('添加账号'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green.shade600,
                    foregroundColor: Colors.white,
                  ),
                ),
                const SizedBox(width: 12),
                ElevatedButton.icon(
                  onPressed: () => logic.refreshAllAccountStatus(),
                  icon: const Icon(Icons.refresh, size: 18),
                  label: const Text('刷新状态'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue.shade600,
                    foregroundColor: Colors.white,
                  ),
                ),
                const SizedBox(width: 12),
                ElevatedButton.icon(
                  onPressed: _checkAllMsTokens,
                  icon: const Icon(Icons.security, size: 18),
                  label: const Text('检查Token'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.purple.shade600,
                    foregroundColor: Colors.white,
                  ),
                ),
                const SizedBox(width: 12),

                if (!isSelectMode.value) ...[
                  ElevatedButton.icon(
                    onPressed: () => isSelectMode.value = true, 
                    icon: const Icon(Icons.checklist, size: 18),
                    label: const Text('批量操作'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange.shade600,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ] else ...[
                  ElevatedButton.icon(
                    onPressed: _batchDelete,
                    icon: const Icon(Icons.delete, size: 18),
                    label: Text('删除(${selectedAccounts.length})'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red.shade600,
                      foregroundColor: Colors.white,
                    ),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton.icon(
                    onPressed: _batchRefreshMsToken,
                    icon: const Icon(Icons.refresh, size: 18),
                    label: Text('刷新Token(${selectedAccounts.length})'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange.shade600,
                      foregroundColor: Colors.white,
                    ),
                  ),
                  const SizedBox(width: 8),
                  TextButton(
                    onPressed: () {
                      isSelectMode.value = false;
                      selectedAccounts.clear();
                    },
                    child: const Text('取消'),
                  ),
                ],
                const Spacer(),
                Obx(() => Text(
                  '共 ${logic.accounts.length} 个账号',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade600,
                  ),
                )),
              ],
            )),
            const SizedBox(height: 16),

            // 账号列表
            Expanded(
              child: Obx(() {
                if (logic.accounts.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.account_circle_outlined,
                             size: 64, color: Colors.grey.shade400),
                        const SizedBox(height: 16),
                        Text(
                          '暂无账号',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey.shade600,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '点击"添加账号"按钮添加第一个账号',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade500,
                          ),
                        ),
                      ],
                    ),
                  );
                }

                return ListView.builder(
                  itemCount: logic.accounts.length,
                  itemBuilder: (context, index) {
                    final account = logic.accounts[index];
                    return _buildAccountCard(account);
                  },
                );
              }),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建账号卡片
  Widget _buildAccountCard(JlzsAccountModel account) {
    return Obx(() => Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // 批量选择复选框
            if (isSelectMode.value) ...[
              Checkbox(
                value: selectedAccounts.contains(account.id),
                onChanged: (selected) {
                  if (selected == true) {
                    selectedAccounts.add(account.id);
                  } else {
                    selectedAccounts.remove(account.id);
                  }
                },
              ),
              const SizedBox(width: 12),
            ],

            // 账号信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.phone_android, size: 16, color: Colors.grey.shade600),
                      const SizedBox(width: 4),
                      Text(
                        account.maskedMobile,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(width: 8),
                      _buildStatusChip(account),
                      if (account.proxy != null) ...[
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: Colors.purple.shade100,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Text(
                            account.proxy!.type == ProxyType.http ? 'HTTP' : 'SOCKS5',
                            style: TextStyle(
                              fontSize: 9,
                              color: Colors.purple.shade700,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                  const SizedBox(height: 8),
                  if (account.session?.displayName != null) ...[
                    Text(
                      '用户: ${account.session!.displayName}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade700,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                  ],
                  if (account.connectedAccounts.isNotEmpty) ...[
                    Text(
                      '关联: ${account.connectedAccounts.map((e) => '${e.platformDisplayName}(${e.screenName})').join(', ')}',
                      style: TextStyle(
                        fontSize: 11,
                        color: Colors.grey.shade600,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                  ],
                  Text(
                    '最后使用: ${account.lastUsedFriendly}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                  if (account.proxy != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      '代理: ${account.proxy!.displayInfo}',
                      style: TextStyle(
                        fontSize: 11,
                        color: Colors.purple.shade600,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                  // 显示msToken状态
                  if (account.session?.userInfo['msToken'] != null) ...[
                    const SizedBox(height: 4),
                    _buildMsTokenStatus(account),
                  ],
                  if (account.remark != null && account.remark!.isNotEmpty) ...[
                    const SizedBox(height: 4),
                    Text(
                      '备注: ${account.remark}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],
                ],
              ),
            ),

            // 操作按钮
            if (!isSelectMode.value) ...[
              Column(
                children: [
                  IconButton(
                    icon: Icon(
                      account.isOnline ? Icons.visibility_off : Icons.visibility,
                      color: account.isOnline ? Colors.orange : Colors.green,
                    ),
                    onPressed: () => _toggleAccountStatus(account),
                    tooltip: account.isOnline ? '禁用账号（保持浏览器运行）' : '启用账号',
                  ),

                  IconButton(
                    icon: Icon(Icons.refresh, color: Colors.blue.shade600),
                    onPressed: () => _refreshMsToken(account),
                    tooltip: '刷新msToken',
                  ),

                  // 关闭浏览器按钮（只在账号在线时显示）
                  if (account.isOnline) ...[
                    IconButton(
                      icon: Icon(Icons.close, color: Colors.red.shade400),
                      onPressed: () => _closeBrowser(account),
                      tooltip: '关闭浏览器实例',
                    ),
                  ],
                  IconButton(
                    icon: Icon(Icons.edit, color: Colors.blue.shade600),
                    onPressed: () => _editAccount(account),
                    tooltip: '编辑账号',
                  ),
                  IconButton(
                    icon: Icon(Icons.delete, color: Colors.red.shade600),
                    onPressed: () => _deleteAccount(account),
                    tooltip: '删除账号',
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    ));
  }

  /// 构建状态标签
  Widget _buildStatusChip(JlzsAccountModel account) {
    Color color;
    String text = account.statusText;

    switch (account.status) {
      case AccountStatus.active:
        color = Colors.green;
        break;
      case AccountStatus.offline:
        color = Colors.grey;
        break;
      case AccountStatus.error:
        color = Colors.red;
        break;
      case AccountStatus.expired:
        color = Colors.orange;
        break;
      case AccountStatus.inactive:
        color = Colors.grey;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 10,
          color: color.withOpacity(0.8),
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  /// 构建msToken状态显示
  Widget _buildMsTokenStatus(JlzsAccountModel account) {
    String? msToken = account.session?.userInfo['msToken'];
    int? expiryTimestamp = account.session?.userInfo['msTokenExpiry'];

    if (msToken == null) {
      return Text(
        'msToken: 未获取',
        style: TextStyle(
          fontSize: 11,
          color: Colors.grey.shade600,
        ),
      );
    }

    Color statusColor = Colors.green;
    String statusText = '有效';

    if (expiryTimestamp != null) {
      DateTime expiryDate = DateTime.fromMillisecondsSinceEpoch(expiryTimestamp);
      DateTime now = DateTime.now();
      Duration timeUntilExpiry = expiryDate.difference(now);

      if (timeUntilExpiry.isNegative) {
        statusColor = Colors.red;
        statusText = '已过期';
      } else if (timeUntilExpiry.inHours < 24) {
        statusColor = Colors.orange;
        statusText = '${timeUntilExpiry.inHours}小时后过期';
      } else {
        statusText = '${timeUntilExpiry.inDays}天后过期';
      }
    }

    return Row(
      children: [
        Text(
          'msToken: ',
          style: TextStyle(
            fontSize: 11,
            color: Colors.grey.shade600,
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 1),
          decoration: BoxDecoration(
            color: statusColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            statusText,
            style: TextStyle(
              fontSize: 10,
              color: statusColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  /// 显示添加账号对话框
  void _showAddAccountDialog() {
    final remarkController = TextEditingController();
    final proxyAddressController = TextEditingController();
    final proxyPortController = TextEditingController();
    final proxyUsernameController = TextEditingController();
    final proxyPasswordController = TextEditingController();
    final proxyValidTimeController = TextEditingController(text: '60'); // 默认60分钟

    final useProxy = false.obs;
    final proxyType = ProxyType.http.obs;

    Get.dialog(
      AlertDialog(
        title: const Text('添加巨量指数账号'),
        content: SizedBox(
          width: 500,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 说明文字
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue.shade200),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.info_outline, color: Colors.blue.shade600, size: 20),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          '点击"开始登录"后，将打开浏览器窗口，请在浏览器中输入手机号完成登录',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.blue.shade700,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),

                // 备注
                TextField(
                  controller: remarkController,
                  decoration: const InputDecoration(
                    labelText: '备注 (可选)',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.note),
                  ),
                ),
                const SizedBox(height: 16),

                // 代理配置
                Obx(() => Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Checkbox(
                          value: useProxy.value,
                          onChanged: (value) => useProxy.value = value ?? false,
                        ),
                        const Text('使用代理'),
                      ],
                    ),
                    if (useProxy.value) ...[
                      const SizedBox(height: 12),
                      // 代理类型选择
                      DropdownButtonFormField<ProxyType>(
                        value: proxyType.value,
                        decoration: const InputDecoration(
                          labelText: '代理类型',
                          border: OutlineInputBorder(),
                        ),
                        items: [
                          DropdownMenuItem(value: ProxyType.http, child: Text('HTTP')),
                          DropdownMenuItem(value: ProxyType.socks5, child: Text('SOCKS5')),
                        ],
                        onChanged: (value) => proxyType.value = value!,
                      ),
                      const SizedBox(height: 12),
                      // 代理地址和端口
                      Row(
                        children: [
                          Expanded(
                            flex: 3,
                            child: TextField(
                              controller: proxyAddressController,
                              decoration: const InputDecoration(
                                labelText: '代理地址',
                                border: OutlineInputBorder(),
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            flex: 1,
                            child: TextField(
                              controller: proxyPortController,
                              keyboardType: TextInputType.number,
                              decoration: const InputDecoration(
                                labelText: '端口',
                                border: OutlineInputBorder(),
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      // 代理认证
                      Row(
                        children: [
                          Expanded(
                            child: TextField(
                              controller: proxyUsernameController,
                              decoration: const InputDecoration(
                                labelText: '用户名 (可选)',
                                border: OutlineInputBorder(),
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: TextField(
                              controller: proxyPasswordController,
                              obscureText: true,
                              decoration: const InputDecoration(
                                labelText: '密码 (可选)',
                                border: OutlineInputBorder(),
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      // 代理有效时间
                      TextField(
                        controller: proxyValidTimeController,
                        keyboardType: TextInputType.number,
                        decoration: const InputDecoration(
                          labelText: '有效时间 (分钟)',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.timer),
                          helperText: '代理的有效时间，默认60分钟',
                        ),
                      ),
                      const SizedBox(height: 12),
                      // 代理类型说明
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade50,
                          borderRadius: BorderRadius.circular(6),
                          border: Border.all(color: Colors.grey.shade300),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '代理类型说明:',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                                color: Colors.grey.shade700,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              '• HTTP: 适用于大多数网站，推荐使用\n• SOCKS5: 更高级的代理协议，支持更多应用',
                              style: TextStyle(
                                fontSize: 11,
                                color: Colors.grey.shade600,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 12),
                      // 代理验证按钮
                      Row(
                        children: [
                          Expanded(
                            child: OutlinedButton.icon(
                              onPressed: () => _validateProxy(
                                proxyAddressController,
                                proxyPortController,
                                proxyUsernameController,
                                proxyPasswordController,
                                proxyType,
                              ),
                              icon: const Icon(Icons.network_check, size: 16),
                              label: const Text('验证代理'),
                              style: OutlinedButton.styleFrom(
                                foregroundColor: Colors.blue.shade700,
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: OutlinedButton.icon(
                              onPressed: () => _autoGetProxy(
                                proxyAddressController,
                                proxyPortController,
                                proxyUsernameController,
                                proxyPasswordController,
                                proxyValidTimeController,
                                proxyType,
                              ),
                              icon: const Icon(Icons.download, size: 16),
                              label: const Text('自动获取'),
                              style: OutlinedButton.styleFrom(
                                foregroundColor: Colors.green.shade700,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ],
                )),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              _startLoginProcess(
                remarkController.text,
                useProxy.value,
                proxyType.value,
                proxyAddressController.text,
                proxyPortController.text,
                proxyUsernameController.text,
                proxyPasswordController.text,
                proxyValidTimeController.text,
              );
            },
            child: const Text('开始登录'),
          ),
        ],
      ),
    );
  }

  /// 开始登录流程
  void _startLoginProcess(
    String remark,
    bool useProxy,
    ProxyType proxyType,
    String proxyAddress,
    String proxyPort,
    String proxyUsername,
    String proxyPassword,
    String proxyValidTime,
  ) {

    JlzsProxyConfig? proxy;
    if (useProxy && proxyAddress.isNotEmpty && proxyPort.isNotEmpty) {
      try {
        int port = int.parse(proxyPort);
        int? validTime;
        if (proxyValidTime.isNotEmpty) {
          validTime = int.parse(proxyValidTime);
        }

        proxy = JlzsProxyConfig(
          address: proxyAddress,
          port: port,
          username: proxyUsername.isEmpty ? null : proxyUsername,
          password: proxyPassword.isEmpty ? null : proxyPassword,
          type: proxyType,
          validTime: validTime,
          startTime: DateTime.now(),
        );
      } catch (e) {
        Get.snackbar('错误', '代理端口和有效时间必须是数字', snackPosition: SnackPosition.TOP);
        return;
      }
    }

    Get.back();

    // 调用logic的开始登录流程方法
    logic.startLoginProcessWithoutMobile(
      proxy: proxy,
      remark: remark.isEmpty ? null : remark,
    );
  }

  /// 关闭浏览器实例
  void _closeBrowser(JlzsAccountModel account) async {
    try {
      // 确认对话框
      bool? confirmed = await Get.dialog<bool>(
        AlertDialog(
          title: Text('确认关闭'),
          content: Text('确定要关闭账号 ${account.displayName} 的浏览器实例吗？\n\n这将完全关闭浏览器，账号状态将变为离线。'),
          actions: [
            TextButton(
              onPressed: () => Get.back(result: false),
              child: Text('取消'),
            ),
            TextButton(
              onPressed: () => Get.back(result: true),
              child: Text('确定', style: TextStyle(color: Colors.red)),
            ),
          ],
        ),
      );

      if (confirmed != true) return;

      print('🔄 正在关闭账号浏览器: ${account.displayName}');

      // 关闭浏览器实例
      await logic.browserManager.closeBrowserForAccount(account.id);

      // 更新账号状态
      account.status = AccountStatus.offline;
      if (account.session != null) {
        account.session!.isActive = false;
      }

      // 通知UI更新
      logic.accounts.refresh();

      print('✅ 浏览器已关闭: ${account.displayName}');
      Get.snackbar('操作完成', '浏览器实例已关闭', snackPosition: SnackPosition.TOP);
    } catch (e) {
      print('❌ 关闭浏览器失败: $e');
      Get.snackbar('错误', '关闭浏览器失败: $e', snackPosition: SnackPosition.TOP);
    }
  }

  /// 切换账号状态
  void _toggleAccountStatus(JlzsAccountModel account) async {
    try {
      if (account.isOnline) {
        // 禁用账号 - 只改变状态，不关闭浏览器
        print('🔄 正在禁用账号: ${account.displayName}');

        // 更新账号状态为离线（但保持浏览器实例运行）
        account.status = AccountStatus.offline;
        if (account.session != null) {
          account.session!.isActive = false;
        }

        // 通知UI更新
        logic.accounts.refresh();

        print('✅ 账号已禁用: ${account.displayName}（浏览器实例保持运行）');
        Get.snackbar('操作完成', '账号已禁用，浏览器保持运行', snackPosition: SnackPosition.TOP);
      } else {
        // 启用账号 - 只改变状态，不重新登录
        print('🔄 正在启用账号: ${account.displayName}');

        // 直接恢复账号状态
        account.status = AccountStatus.active;
        if (account.session != null) {
          account.session!.isActive = true;
        }

        // 通知UI更新
        logic.accounts.refresh();

        print('✅ 账号已启用: ${account.displayName}');
        Get.snackbar('操作完成', '账号已启用', snackPosition: SnackPosition.TOP);
      }
    } catch (e) {
      print('❌ 切换账号状态失败: $e');
      Get.snackbar('错误', '操作失败: $e', snackPosition: SnackPosition.TOP);
    }
  }

  /// 刷新msToken
  void _refreshMsToken(JlzsAccountModel account) async {
    try {
      Get.snackbar('刷新中', '正在刷新 ${account.displayName} 的msToken...', snackPosition: SnackPosition.TOP);

      await logic.refreshAccountMsToken(account);

      // 刷新UI
      setState(() {});

    } catch (e) {
      Get.snackbar('错误', '刷新msToken失败: $e', snackPosition: SnackPosition.TOP);
    }
  }

  /// 编辑账号
  void _editAccount(JlzsAccountModel account) {
    // TODO: 实现编辑账号功能（修改备注、代理等）
    Get.snackbar('提示', '编辑功能开发中...', snackPosition: SnackPosition.TOP);
  }

  /// 删除账号
  void _deleteAccount(JlzsAccountModel account) {
    Get.dialog(
      AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要删除账号 "${account.maskedMobile}" 吗？\n\n此操作将：\n• 关闭浏览器实例\n• 清除所有会话数据\n• 无法撤销'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () async {
              await logic.removeAccount(account.id);
              Get.back();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  /// 检查所有账号的msToken
  void _checkAllMsTokens() {
    logic.checkAllMsTokens();
    Get.snackbar('检查完成', '已检查所有账号的msToken状态', snackPosition: SnackPosition.TOP);
  }

  /// 刷新所有过期的msToken
  void _refreshAllExpiredMsTokens() async {
    Get.snackbar('刷新中', '正在刷新所有过期的msToken...', snackPosition: SnackPosition.TOP);
    await logic.refreshAllExpiredMsTokens();
    setState(() {}); // 刷新UI
  }

  /// 批量刷新选中账号的msToken
  void _batchRefreshMsToken() async {
    if (selectedAccounts.isEmpty) return;

    Get.dialog(
      AlertDialog(
        title: const Text('确认刷新'),
        content: Text('确定要刷新选中的 ${selectedAccounts.length} 个账号的msToken吗？'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () async {
              Get.back();
              Get.snackbar('刷新中', '正在批量刷新msToken...', snackPosition: SnackPosition.TOP);

              for (var accountId in selectedAccounts) {
                var account = logic.accounts.firstWhere((acc) => acc.id == accountId);
                await logic.refreshAccountMsToken(account);
                // 添加延迟避免请求过快
                await Future.delayed(Duration(milliseconds: 500));
              }

              selectedAccounts.clear();
              isSelectMode.value = false;
              setState(() {}); // 刷新UI
              Get.snackbar('完成', '批量刷新msToken完成', snackPosition: SnackPosition.TOP);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
            child: const Text('刷新', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  /// 批量删除
  void _batchDelete() {
    if (selectedAccounts.isEmpty) {
      Get.snackbar('提示', '请先选择要删除的账号', snackPosition: SnackPosition.TOP);
      return;
    }

    Get.dialog(
      AlertDialog(
        title: const Text('批量删除确认'),
        content: Text('确定要删除选中的 ${selectedAccounts.length} 个账号吗？\n\n此操作将：\n• 关闭所有选中账号的浏览器实例\n• 清除所有会话数据\n• 无法撤销'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () async {
              Get.back(); // 先关闭确认对话框

              // 显示进度
              Get.dialog(
                AlertDialog(
                  content: Row(
                    children: [
                      CircularProgressIndicator(),
                      SizedBox(width: 16),
                      Text('正在删除账号...'),
                    ],
                  ),
                ),
                barrierDismissible: false,
              );

              // 批量删除
              for (String accountId in selectedAccounts.toList()) {
                await logic.removeAccount(accountId);
              }

              // 清空选择并退出批量模式
              selectedAccounts.clear();
              isSelectMode.value = false;

              Get.back(); // 关闭进度对话框
              Get.snackbar('成功', '批量删除完成', snackPosition: SnackPosition.TOP);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  /// 验证代理配置
  void _validateProxy(
    TextEditingController proxyAddressController,
    TextEditingController proxyPortController,
    TextEditingController proxyUsernameController,
    TextEditingController proxyPasswordController,
    Rx<ProxyType> proxyType,
  ) async {
    if (proxyAddressController.text.isEmpty || proxyPortController.text.isEmpty) {
      Get.snackbar('提示', '请先填写代理地址和端口', snackPosition: SnackPosition.TOP);
      return;
    }

    try {
      int port = int.parse(proxyPortController.text);
      var proxy = JlzsProxyConfig(
        address: proxyAddressController.text,
        port: port,
        username: proxyUsernameController.text.isEmpty ? null : proxyUsernameController.text,
        password: proxyPasswordController.text.isEmpty ? null : proxyPasswordController.text,
        type: proxyType.value,
      );

      Get.snackbar('验证中', '正在验证代理配置...', snackPosition: SnackPosition.TOP);

      bool isValid = await proxy.validate();

      if (isValid) {
        Get.snackbar('成功', '代理配置验证通过', snackPosition: SnackPosition.TOP);
      } else {
        Get.snackbar('失败', '代理配置验证失败，请检查配置', snackPosition: SnackPosition.TOP);
      }
    } catch (e) {
      Get.snackbar('错误', '代理端口必须是数字', snackPosition: SnackPosition.TOP);
    }
  }

  /// 自动获取代理
  void _autoGetProxy(
    TextEditingController addressController,
    TextEditingController portController,
    TextEditingController usernameController,
    TextEditingController passwordController,
    TextEditingController validTimeController,
    Rx<ProxyType> proxyType,
  ) async {
    try {
      Get.snackbar('获取中', '正在自动获取代理配置...', snackPosition: SnackPosition.TOP);

      // 显示代理API配置对话框
      String? proxyApiUrl = await _showProxyApiConfigDialog();

      if (proxyApiUrl == null || proxyApiUrl.isEmpty) {
        return; // 用户取消或未配置
      }

      // 调用代理API
      var response = await _fetchProxyFromApi(proxyApiUrl);

      if (response != null) {
        // 填充代理信息
        addressController.text = response['ip'] ?? response['address'] ?? '';
        portController.text = (response['port'] ?? '').toString();
        usernameController.text = response['username'] ?? '';
        passwordController.text = response['password'] ?? '';
        validTimeController.text = (response['validTime'] ?? response['expire_time'] ?? '60').toString();

        // 设置代理类型
        String typeStr = (response['type'] ?? 'http').toLowerCase();
        if (typeStr.contains('socks')) {
          proxyType.value = ProxyType.socks5;
        } else {
          proxyType.value = ProxyType.http;
        }

        Get.snackbar('成功', '代理配置已自动填充', snackPosition: SnackPosition.TOP);
      } else {
        Get.snackbar('失败', '获取代理失败，请检查API配置', snackPosition: SnackPosition.TOP);
      }

    } catch (e) {
      print('❌ 自动获取代理失败: $e');
      Get.snackbar('错误', '自动获取代理失败: $e', snackPosition: SnackPosition.TOP);
    }
  }

  /// 显示代理API配置对话框
  Future<String?> _showProxyApiConfigDialog() async {
    final apiUrlController = TextEditingController();
    String? result;

    await Get.dialog(
      AlertDialog(
        title: Text('配置代理API'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('请输入代理API地址：'),
            SizedBox(height: 12),
            TextField(
              controller: apiUrlController,
              decoration: InputDecoration(
                labelText: 'API地址',
                hintText: 'http://api.example.com/proxy',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.link),
              ),
            ),
            SizedBox(height: 12),
            Container(
              padding: EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.orange.shade50,
                borderRadius: BorderRadius.circular(6),
                border: Border.all(color: Colors.orange.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.warning_amber, color: Colors.orange.shade600, size: 16),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '如果代理需要认证，请手动填写用户名和密码；白名单代理无需认证',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.orange.shade700,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 16),
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '📋 API返回格式要求：',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      color: Colors.blue.shade700,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    '**************:44546',
                    style: TextStyle(
                      fontFamily: 'monospace',
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.blue.shade600,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    '格式：IP地址:端口号',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.blue.shade600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              result = null;
              Get.back();
            },
            child: Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              result = apiUrlController.text.trim();
              Get.back();
            },
            child: Text('确定'),
          ),
        ],
      ),
    );

    return result;
  }

  /// 从API获取代理
  Future<Map<String, dynamic>?> _fetchProxyFromApi(String apiUrl) async {
    try {
      print('🌐 正在从API获取代理: $apiUrl');

      // TODO: 实现HTTP请求获取代理
      // 示例实现：
      // var response = await http.get(Uri.parse(apiUrl));
      // if (response.statusCode == 200) {
      //   String responseText = response.body.trim();
      //   return _parseProxyResponse(responseText);
      // }

      // 模拟API响应（开发阶段）
      await Future.delayed(Duration(seconds: 2));

      // 模拟返回 IP:端口 格式
      String mockResponse = '**************:44546';
      return _parseProxyResponse(mockResponse);

    } catch (e) {
      print('❌ 从API获取代理失败: $e');
      return null;
    }
  }

  /// 解析代理响应（支持 IP:端口 格式）
  Map<String, dynamic>? _parseProxyResponse(String response) {
    try {
      response = response.trim();

      // 检查是否是 IP:端口 格式
      if (response.contains(':')) {
        List<String> parts = response.split(':');
        if (parts.length == 2) {
          String ip = parts[0].trim();
          String portStr = parts[1].trim();

          // 验证端口是否为数字
          int? port = int.tryParse(portStr);
          if (port != null && port > 0 && port <= 65535) {
            return {
              'ip': ip,
              'port': port,
              'username': '', // IP:端口格式通常不包含认证信息
              'password': '',
              'type': 'http', // 默认HTTP类型
              'validTime': 60, // 默认60分钟
            };
          }
        }
      }

      // 如果不是标准格式，尝试解析为JSON
      try {
        var jsonData = json.decode(response);
        return jsonData;
      } catch (e) {
        print('⚠️ 无法解析代理响应格式: $response');
        return null;
      }

    } catch (e) {
      print('❌ 解析代理响应失败: $e');
      return null;
    }
  }
}
