import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../jlzs_logic.dart';
import 'conversion_rules_dialog.dart';

/// 关键词输入组件
class KeywordInputWidget extends StatefulWidget {
  const KeywordInputWidget({Key? key}) : super(key: key);

  @override
  State<KeywordInputWidget> createState() => _KeywordInputWidgetState();
}

class _KeywordInputWidgetState extends State<KeywordInputWidget> {

  @override
  Widget build(BuildContext context) {
    final logic = Get.find<JlzsLogic>();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题和说明
        Row(
          children: [
            Icon(Icons.text_fields, color: Colors.blue.shade600, size: 20),
            const SizedBox(width: 8),
            const Text(
              '关键词列表',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
            const Spacer(),
            // 转换按钮
            ElevatedButton.icon(
              onPressed: () => _convertKeywords(logic),
              icon: const Icon(Icons.transform, size: 16),
              label: const Text('转换', style: TextStyle(fontSize: 12)),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green.shade600,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                minimumSize: Size.zero,
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
            ),
            const SizedBox(width: 8),
            // 转换规则按钮
            TextButton.icon(
              onPressed: _showConversionRulesDialog,
              icon: const Icon(Icons.settings, size: 16),
              label: const Text('规则', style: TextStyle(fontSize: 12)),
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                minimumSize: Size.zero,
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          '输入关键词后点击"转换"按钮，支持换行、空格、逗号分隔',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
        const SizedBox(height: 12),
        
        // 关键词输入框
        Container(
          height: 180,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade400),
            borderRadius: BorderRadius.circular(6.0),
          ),
          child: TextField(
            controller: logic.keywordController,
            maxLines: null,
            expands: true,
            textAlignVertical: TextAlignVertical.top,
            decoration: const InputDecoration(
              hintText: '请输入关键词...\n例如：\n关键词1,关键词2 关键词3\n关键词4；关键词5\n\n输入完成后点击"转换"按钮',
              border: InputBorder.none,
              contentPadding: EdgeInsets.all(12.0),
            ),
            style: const TextStyle(fontSize: 13),
            onChanged: (value) {
              // 实时统计关键词数量，但不应用转换规则
              _updateKeywordCount(logic, value);
            },
          ),
        ),

        const SizedBox(height: 8),

        // 关键词统计
        _buildKeywordStats(logic),
        
        const SizedBox(height: 12),
        
        // 关键词统计
        GetBuilder<JlzsLogic>(
          id: 'collection_details',
          builder: (logic) {
            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(6.0),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.info_outline, 
                       color: Colors.blue.shade600, size: 16),
                  const SizedBox(width: 8),
                  Text(
                    '已输入 ${logic.config.value.keywordCount} 个关键词',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.blue.shade700,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ],
    );
  }

  /// 构建关键词统计
  Widget _buildKeywordStats(JlzsLogic logic) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.green.shade50,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: Colors.green.shade200),
      ),
      child: GetBuilder<JlzsLogic>(
        id: 'collection_details',
        builder: (logic) {
          return Row(
            children: [
              Icon(Icons.check_circle_outline,
                   color: Colors.green.shade600, size: 16),
              const SizedBox(width: 8),
              Text(
                '已解析 ${logic.config.value.keywords.length} 个关键词',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.green.shade700,
                  fontWeight: FontWeight.w500,
                ),
              ),
              if (logic.config.value.keywords.isNotEmpty) ...[
                const SizedBox(width: 16),
                Text(
                  '最后转换: ${DateTime.now().toString().substring(11, 19)}',
                  style: TextStyle(
                    fontSize: 11,
                    color: Colors.green.shade600,
                  ),
                ),
              ],
            ],
          );
        },
      ),
    );
  }

  /// 实时更新关键词数量统计（不应用转换规则）
  void _updateKeywordCount(JlzsLogic logic, String value) {
    if (value.trim().isEmpty) {
      // 输入为空时，清空关键词列表
      logic.config.value = logic.config.value.copyWith(keywords: []);
      logic.update(['collection_details']);
    } else {
      // 只解析关键词数量，不应用转换规则
      List<String> keywords = logic.parseKeywords(value);
      logic.config.value = logic.config.value.copyWith(keywords: keywords);
      logic.update(['collection_details']);
    }
  }

  /// 转换关键词
  void _convertKeywords(JlzsLogic logic) {
    final inputText = logic.keywordController.text.trim();

    if (inputText.isEmpty) {
      Get.snackbar(
        '提示',
        '请先输入关键词',
        snackPosition: SnackPosition.TOP,
      );
      return;
    }

    // 解析关键词
    List<String> keywords = logic.parseKeywords(inputText);

    // 应用转换规则
    keywords = logic.keywordConversionRules.value.applyToList(keywords);

    // 将转换后的关键词按行显示在输入框中
    String convertedText = keywords.join('\n');
    logic.keywordController.text = convertedText;

    // 更新配置（转换后的关键词）
    logic.config.value = logic.config.value.copyWith(keywords: keywords);
    logic.update(['collection_details']);

    // 显示转换结果
    Get.snackbar(
      '转换完成',
      '成功转换 ${keywords.length} 个关键词',
      snackPosition: SnackPosition.TOP,
      duration: const Duration(seconds: 2),
    );
  }

  /// 显示转换规则对话框
  void _showConversionRulesDialog() {
    Get.dialog(
      const ConversionRulesDialog(),
    );
  }

}
