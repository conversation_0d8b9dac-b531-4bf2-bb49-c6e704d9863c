import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:dotted_line/dotted_line.dart';
import '../../../model/jlzs_config_model.dart';
import '../../../widgets/custom_checkbox.dart';
import '../jlzs_logic.dart';

/// 巨量指数地区选择组件 (基于CheckboxListWithExpand)
class JlzsRegionTreeWidget extends StatefulWidget {
  const JlzsRegionTreeWidget({Key? key}) : super(key: key);

  @override
  State<JlzsRegionTreeWidget> createState() => _JlzsRegionTreeWidgetState();
}

class _JlzsRegionTreeWidgetState extends State<JlzsRegionTreeWidget> {
  // 用来跟踪每个节点的展开状态
  Map<int, bool> isExpandedMap = {};
  Timer? _timer;
  // 添加 ScrollController
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    // 延迟展开第一个节点
    _timer = Timer(const Duration(milliseconds: 50), () {
      if (mounted) {
        setState(() {
          isExpandedMap[0] = true;
        });
      }
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    _scrollController.dispose();
    super.dispose();
  }

  /// 切换节点展开状态
  void _toggleExpand(int id) {
    setState(() {
      isExpandedMap[id] = !(isExpandedMap[id] ?? false);
    });
  }

  /// 展开有选中子节点的节点
  void _expandNodesBasedOnCheckedState(List<TreeNode> nodes) {
    for (var node in nodes) {
      _expandIfChildrenChecked(node);
      if (node.children.isNotEmpty) {
        _expandNodesBasedOnCheckedState(node.children);
      }
    }
  }

  /// 如果子节点被选中则展开该节点
  void _expandIfChildrenChecked(TreeNode node) {
    if (node.children.isNotEmpty) {
      bool anyChildChecked = node.children.any((child) => child.isChecked || _hasCheckedDescendant(child));
      if (anyChildChecked) {
        isExpandedMap[node.id] = true;
      }
    }
  }

  /// 检查是否有选中的后代节点
  bool _hasCheckedDescendant(TreeNode node) {
    if (node.isChecked) return true;
    if (node.children.isNotEmpty) {
      return node.children.any((child) => _hasCheckedDescendant(child));
    }
    return false;
  }

  /// 处理节点选中状态变化
  void _onNodeCheckedChange(bool isChecked, TreeNode node) {
    final logic = Get.find<JlzsLogic>();

    setState(() {
      if (node.children.isNotEmpty) {
        // 省份节点：只选择省份本身，不自动选择城市
        node.isChecked = isChecked;
        if (!isChecked) {
          // 如果取消选中省份，也取消所有城市
          node.setOnlyChildrenChecked(false);
        }
      } else {
        // 城市节点：只选择城市本身
        node.isChecked = isChecked;
      }
    });

    // 通知逻辑控制器更新
    logic.onRegionSelectionChanged(node.name);
  }

  /// 选择省份的所有城市
  void _selectAllCitiesInProvince(TreeNode provinceNode) {
    final logic = Get.find<JlzsLogic>();

    setState(() {
      // 只选中所有城市，不自动选中省份
      provinceNode.setOnlyChildrenChecked(true);
    });

    // 通知逻辑控制器更新
    logic.onRegionSelectionChanged(provinceNode.name);
  }

  /// 取消选择省份的所有城市
  void _unselectAllCitiesInProvince(TreeNode provinceNode) {
    final logic = Get.find<JlzsLogic>();

    setState(() {
      // 只取消城市，保留省份选择
      provinceNode.setOnlyChildrenChecked(false);
    });

    // 通知逻辑控制器更新
    logic.onRegionSelectionChanged(provinceNode.name);
  }

  /// 构建单个节点
  Widget buildItem(TreeNode node) {
    bool isExpanded = isExpandedMap[node.id] ?? false;

    // 对于省份节点，计算复选框状态
    bool? checkboxValue = node.isChecked;
    if (node.children.isNotEmpty && !node.isChecked) {
      // 如果省份本身未选中，检查子节点状态
      if (node.areAllChildrenChecked()) {
        checkboxValue = true; // 所有城市都选中
      } else if (node.areSomeChildrenChecked()) {
        checkboxValue = null; // 部分城市选中（三态）
      } else {
        checkboxValue = false; // 没有城市选中
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          onTap: () {
            if (node.children.isNotEmpty) {
              _toggleExpand(node.id);
            }
          },
          child: Row(
            children: [
              _buildIconWithLine(isExpanded, node),
              _buildDottedLine(),
              Expanded(
                child: Row(
                  children: [
                    // 使用原生 Checkbox 支持三态
                    if (node.children.isNotEmpty)
                      Checkbox(
                        value: checkboxValue,
                        tristate: true,
                        onChanged: (value) {
                          _onNodeCheckedChange(value ?? false, node);
                        },
                        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        visualDensity: VisualDensity.compact,
                      )
                    else
                      CustomCheckbox(
                        isChecked: checkboxValue ?? false,
                        onChanged: (value) {
                          _onNodeCheckedChange(value, node);
                        },
                        label: '',
                      ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        node.name,
                        style: const TextStyle(fontSize: 14, fontFamily: "Alibaba"),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ),
                    // 为省份节点添加选择所有城市的按钮
                    if (node.children.isNotEmpty) ...[
                      const SizedBox(width: 8),
                      _buildProvinceActionButton(node),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
        // 展开并且有子节点时，递归展示子节点
        if (isExpanded && node.children.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(left: 20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                for (var child in node.children)
                  buildItem(child),
              ],
            ),
          ),
      ],
    );
  }

  /// 构建展开/收起图标
  Widget _buildIconWithLine(bool isExpanded, TreeNode node) {
    return Container(
      width: 14,
      margin: EdgeInsets.symmetric(vertical: node.children.isEmpty ? 0 : 5),
      height: node.children.isEmpty ? 25 : 14,
      alignment: Alignment.center,
      decoration: node.children.isNotEmpty
          ? BoxDecoration(
              color: Colors.grey.shade200,
              border: Border.all(
                color: Colors.grey,
                width: 1.0,
              ),
            )
          : null,
      child: node.children.isEmpty
          ? const DottedLine(
              direction: Axis.vertical,
              lineThickness: 0.5,
              dashGapLength: 2,
              dashLength: 3,
            )
          : Icon(
              isExpanded ? Icons.remove : Icons.add,
              color: Colors.grey.shade500,
              size: 12,
            ),
    );
  }

  /// 构建虚线
  Widget _buildDottedLine() {
    return Container(
      width: 10,
      height: 14,
      margin: const EdgeInsets.only(left: 2),
      alignment: Alignment.center,
      child: const DottedLine(
        lineThickness: 0.5,
        dashGapLength: 2,
        dashLength: 3,
      ),
    );
  }

  /// 构建省份操作按钮
  Widget _buildProvinceActionButton(TreeNode provinceNode) {
    final allCitiesSelected = provinceNode.areAllChildrenChecked();
    final someCitiesSelected = provinceNode.areSomeChildrenChecked();

    return Container(
      height: 24,
      child: TextButton(
        onPressed: () {
          if (allCitiesSelected) {
            // 如果所有城市都选中，则取消选择所有城市
            _unselectAllCitiesInProvince(provinceNode);
          } else {
            // 否则选择所有城市
            _selectAllCitiesInProvince(provinceNode);
          }
        },
        style: TextButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
          minimumSize: Size.zero,
          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          backgroundColor: allCitiesSelected
              ? Colors.orange.shade100
              : someCitiesSelected
                  ? Colors.blue.shade100
                  : Colors.grey.shade100,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              allCitiesSelected
                  ? Icons.remove_circle_outline
                  : Icons.add_circle_outline,
              size: 12,
              color: allCitiesSelected
                  ? Colors.orange.shade700
                  : someCitiesSelected
                      ? Colors.blue.shade700
                      : Colors.grey.shade600,
            ),
            const SizedBox(width: 2),
            Text(
              allCitiesSelected ? '取消城市' : '选择城市',
              style: TextStyle(
                fontSize: 10,
                color: allCitiesSelected
                    ? Colors.orange.shade700
                    : someCitiesSelected
                        ? Colors.blue.shade700
                        : Colors.grey.shade600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final logic = Get.find<JlzsLogic>();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题和操作按钮
        Row(
          children: [
            Icon(Icons.location_on, color: Colors.blue.shade600, size: 20),
            const SizedBox(width: 8),
            const Text(
              '地区选择',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
            const Spacer(),
            // 全选/取消全选按钮
            GetBuilder<JlzsLogic>(
              id: 'region_tree',
              builder: (logic) {
                final isAllSelected = logic.isAllRegionsSelected();
                final isSomeSelected = logic.isSomeRegionsSelected();

                return TextButton.icon(
                  onPressed: () {
                    if (isAllSelected) {
                      logic.unselectAllRegions();
                    } else {
                      logic.selectAllRegions();
                    }
                  },
                  icon: Icon(
                    isAllSelected ? Icons.check_box :
                    isSomeSelected ? Icons.indeterminate_check_box : Icons.check_box_outline_blank,
                    size: 16,
                  ),
                  label: Text(
                    isAllSelected ? '取消全选' : '全选',
                    style: const TextStyle(fontSize: 12),
                  ),
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    minimumSize: Size.zero,
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                );
              },
            ),
            const SizedBox(width: 8),
            // 手动输入城市按钮
            TextButton.icon(
              onPressed: logic.showCityInputDialog,
              icon: const Icon(Icons.edit, size: 16),
              label: const Text('手动输入', style: TextStyle(fontSize: 12)),
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                minimumSize: Size.zero,
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        
        // 地区统计
        GetBuilder<JlzsLogic>(
          id: 'collection_details',
          builder: (logic) {
            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.green.shade50,
                borderRadius: BorderRadius.circular(6.0),
                border: Border.all(color: Colors.green.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.check_circle_outline, 
                       color: Colors.green.shade600, size: 16),
                  const SizedBox(width: 8),
                  Text(
                    '已选择 ${logic.getSelectedRegionCount()} 个地区',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.green.shade700,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            );
          },
        ),
        
        const SizedBox(height: 12),
        
        // 地区树状列表
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(6.0),
            ),
            child: GetBuilder<JlzsLogic>(
              id: 'region_tree',
              builder: (logic) {
                if (logic.areaData.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const CircularProgressIndicator(),
                        const SizedBox(height: 8),
                        Text(
                          '正在加载地区数据...',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  );
                }
                
                // 根据选中状态展开节点
                _expandNodesBasedOnCheckedState(logic.areaData);
                
                return Scrollbar(
                  controller: _scrollController,
                  thumbVisibility: true,
                  child: SingleChildScrollView(
                    controller: _scrollController,
                    padding: const EdgeInsets.symmetric(vertical: 10.0, horizontal: 12),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        for (var node in logic.areaData)
                          buildItem(node),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ),
      ],
    );
  }
}
