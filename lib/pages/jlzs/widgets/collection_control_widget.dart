import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../jlzs_logic.dart';

/// 采集控制组件
class CollectionControlWidget extends StatelessWidget {
  const CollectionControlWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final logic = Get.find<JlzsLogic>();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 采集详情显示
        _buildCollectionDetails(logic),
        const SizedBox(height: 20),

        // 数据处理配置
        _buildDataProcessingConfig(logic),
        const SizedBox(height: 20),

        // 运行日志
        _buildTaskLogs(logic),
        const SizedBox(height: 20),
        
        // 控制按钮
        _buildControlButtons(logic),
      ],
    );
  }

  /// 构建采集详情显示
  Widget _buildCollectionDetails(JlzsLogic logic) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.info, color: Colors.green.shade600, size: 20),
            const SizedBox(width: 8),
            const Text(
              '采集详情',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        GetBuilder<JlzsLogic>(
          id: 'collection_details',
          builder: (logic) {
            return Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(6),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: Column(
                children: [
                  _buildDetailRow('关键词数量', '${logic.config.value.keywordCount} 个'),
                  const SizedBox(height: 8),
                  _buildDetailRow('地区数量', '${logic.config.value.regionCount} 个'),
                  const SizedBox(height: 8),
                  _buildDetailRow('时间范围', logic.config.value.dateRangeDisplay),
                  const SizedBox(height: 8),
                  _buildDetailRow('采集平台', logic.config.value.platformDisplayName),
                  const SizedBox(height: 8),
                  _buildDetailRow('地区类型', logic.config.value.regionTypeDisplayName),
                  const SizedBox(height: 8),
                  _buildDetailRow('处理周期', _getProcessingPeriodsDisplay(logic.config.value.processingPeriods)),
                ],
              ),
            );
          },
        ),
      ],
    );
  }

  /// 构建详情行
  Widget _buildDetailRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  /// 构建数据处理配置
  Widget _buildDataProcessingConfig(JlzsLogic logic) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.settings_applications, color: Colors.green.shade600, size: 20),
            const SizedBox(width: 8),
            const Text(
              '数据处理配置',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        
        // 处理周期选择 (多选)
        const Text('采集处理周期', style: TextStyle(fontSize: 13, fontWeight: FontWeight.w500)),
        const SizedBox(height: 8),
        GetBuilder<JlzsLogic>(
          id: 'collection_details',
          builder: (logic) {
            return Wrap(
              spacing: 8,
              children: ['day', 'week', 'month', 'year'].map((period) {
                final isSelected = logic.isProcessingPeriodSelected(period);
                return FilterChip(
                  label: Text(
                    _getPeriodDisplayName(period),
                    style: TextStyle(
                      fontSize: 11,
                      color: isSelected ? Colors.blue.shade700 : Colors.grey.shade700,
                    ),
                  ),
                  selected: isSelected,
                  onSelected: (selected) {
                    logic.toggleProcessingPeriod(period);
                  },
                  selectedColor: Colors.blue.shade100,
                  checkmarkColor: Colors.blue.shade700,
                  backgroundColor: Colors.grey.shade200,
                );
              }).toList(),
            );
          },
        ),
        
        const SizedBox(height: 16),
        
        // 聚合方式选择
        const Text('数据聚合方式', style: TextStyle(fontSize: 13, fontWeight: FontWeight.w500)),
        const SizedBox(height: 8),
        Obx(() => Row(
          children: [
            Expanded(
              child: RadioListTile<String>(
                title: const Text('数据加总', style: TextStyle(fontSize: 12)),
                value: 'sum',
                groupValue: logic.config.value.aggregationMethod,
                onChanged: (value) => logic.setAggregationMethod(value!),
                dense: true,
                contentPadding: EdgeInsets.zero,
              ),
            ),
            Expanded(
              child: RadioListTile<String>(
                title: const Text('数据平均', style: TextStyle(fontSize: 12)),
                value: 'average',
                groupValue: logic.config.value.aggregationMethod,
                onChanged: (value) => logic.setAggregationMethod(value!),
                dense: true,
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        )),
      ],
    );
  }



  /// 构建任务日志
  Widget _buildTaskLogs(JlzsLogic logic) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.list_alt, color: Colors.green.shade600, size: 20),
            const SizedBox(width: 8),
            const Text(
              '运行日志',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
            const Spacer(),
            TextButton(
              onPressed: logic.clearLogs,
              child: const Text('清空', style: TextStyle(fontSize: 12)),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Container(
          height: 150,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(6),
            color: Colors.grey.shade50,
          ),
          child: GetBuilder<JlzsLogic>(
            id: 'task_logs',
            builder: (logic) {
              if (logic.taskLogs.isEmpty) {
                return const Center(
                  child: Text(
                    '暂无日志',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                );
              }
              
              return Scrollbar(
                controller: logic.logScrollController,
                thickness: 6,
                radius: const Radius.circular(3),
                child: ListView.builder(
                  controller: logic.logScrollController,
                  padding: const EdgeInsets.all(8),
                  itemCount: logic.taskLogs.length,
                  itemBuilder: (context, index) {
                    return Padding(
                      padding: const EdgeInsets.symmetric(vertical: 1),
                      child: Text(
                        logic.taskLogs[index],
                        style: const TextStyle(
                          fontSize: 11,
                          fontFamily: 'monospace',
                        ),
                      ),
                    );
                  },
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  /// 构建控制按钮
  Widget _buildControlButtons(JlzsLogic logic) {
    return Row(
      children: [
        Expanded(
          child: Obx(() => ElevatedButton.icon(
            onPressed: logic.isStartButtonEnabled.value ? logic.startCollection : null,
            icon: Icon(
              logic.startButtonText.value == '开始' ? Icons.play_arrow : Icons.refresh,
              size: 18,
            ),
            label: Text(logic.startButtonText.value),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green.shade600,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          )),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Obx(() => ElevatedButton.icon(
            onPressed: logic.isStopButtonEnabled.value ? logic.stopCollection : null,
            icon: const Icon(Icons.stop, size: 18),
            label: const Text('停止'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red.shade600,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          )),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: ElevatedButton.icon(
            onPressed: logic.saveConfig,
            icon: const Icon(Icons.save, size: 18),
            label: const Text('保存'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue.shade600,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
      ],
    );
  }

  /// 获取周期显示名称
  String _getPeriodDisplayName(String period) {
    switch (period) {
      case 'day':
        return '日';
      case 'week':
        return '周';
      case 'month':
        return '月';
      case 'year':
        return '年';
      default:
        return period;
    }
  }

  /// 获取处理周期列表的显示文本
  String _getProcessingPeriodsDisplay(List<String> periods) {
    if (periods.isEmpty) return '未设置';
    return periods.map((period) => _getPeriodDisplayName(period)).join('、');
  }
}
