import 'package:flutter/material.dart';
import '../../../../model/jlzs_account_new_model.dart';

/// msToken状态显示组件
/// 
/// 从 AccountManageDialog._buildMsTokenStatus 方法提取
/// 保持原有的样式和逻辑完全不变
class MsTokenStatusWidget extends StatelessWidget {
  final JlzsAccountModel account;

  const MsTokenStatusWidget({
    Key? key,
    required this.account,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    String? msToken = account.session?.userInfo['msToken'];
    int? expiryTimestamp = account.session?.userInfo['msTokenExpiry'];

    if (msToken == null) {
      return Text(
        'msToken: 未获取',
        style: TextStyle(
          fontSize: 11,
          color: Colors.grey.shade600,
        ),
      );
    }

    Color statusColor = Colors.green;
    String statusText = '有效';

    if (expiryTimestamp != null) {
      DateTime expiryDate = DateTime.fromMillisecondsSinceEpoch(expiryTimestamp);
      DateTime now = DateTime.now();
      Duration timeUntilExpiry = expiryDate.difference(now);

      if (timeUntilExpiry.isNegative) {
        statusColor = Colors.red;
        statusText = '已过期';
      } else if (timeUntilExpiry.inHours < 24) {
        statusColor = Colors.orange;
        statusText = '${timeUntilExpiry.inHours}小时后过期';
      } else {
        statusText = '${timeUntilExpiry.inDays}天后过期';
      }
    }

    return Row(
      children: [
        Text(
          'msToken: ',
          style: TextStyle(
            fontSize: 11,
            color: Colors.grey.shade600,
          ),
        ),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 1),
          decoration: BoxDecoration(
            color: statusColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            statusText,
            style: TextStyle(
              fontSize: 10,
              color: statusColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }
}
