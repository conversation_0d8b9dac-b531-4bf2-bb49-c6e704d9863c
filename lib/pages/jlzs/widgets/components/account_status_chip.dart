import 'package:flutter/material.dart';
import '../../../../model/jlzs_account_new_model.dart';

/// 账号状态标签组件
/// 
/// 从 AccountManageDialog._buildStatusChip 方法提取
/// 保持原有的样式和逻辑完全不变
class AccountStatusChip extends StatelessWidget {
  final JlzsAccountModel account;

  const AccountStatusChip({
    Key? key,
    required this.account,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Color color;
    String text = account.statusText;

    switch (account.status) {
      case AccountStatus.active:
        color = Colors.green;
        break;
      case AccountStatus.offline:
        color = Colors.grey;
        break;
      case AccountStatus.error:
        color = Colors.red;
        break;
      case AccountStatus.expired:
        color = Colors.orange;
        break;
      case AccountStatus.inactive:
        color = Colors.grey;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 10,
          color: color.withOpacity(0.8),
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}
