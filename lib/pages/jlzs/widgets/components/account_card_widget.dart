import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../model/jlzs_account_new_model.dart';
import 'account_status_chip.dart';
import 'ms_token_status_widget.dart';

/// 账号操作类型枚举
enum AccountAction {
  toggle,        // 切换账号状态
  refresh,       // 刷新Token
  closeBrowser,  // 关闭浏览器
  edit,          // 编辑账号
  delete,        // 删除账号
}

/// 账号卡片组件
/// 
/// 从 AccountManageDialog._buildAccountCard 方法提取
/// 保持原有的样式和逻辑完全不变
class AccountCardWidget extends StatelessWidget {
  final JlzsAccountModel account;
  final bool isSelectMode;
  final bool isSelected;
  final VoidCallback? onSelect;
  final Function(AccountAction) onAction;

  const AccountCardWidget({
    Key? key,
    required this.account,
    required this.isSelectMode,
    required this.isSelected,
    this.onSelect,
    required this.onAction,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Obx(() => Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // 批量选择复选框
            if (isSelectMode) ...[
              Checkbox(
                value: isSelected,
                onChanged: (selected) => onSelect?.call(),
              ),
              const SizedBox(width: 12),
            ],

            // 账号信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.phone_android, size: 16, color: Colors.grey.shade600),
                      const SizedBox(width: 4),
                      Text(
                        account.maskedMobile,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(width: 8),
                      AccountStatusChip(account: account),
                      if (account.proxy != null) ...[
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: Colors.purple.shade100,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Text(
                            account.proxy!.type == ProxyType.http ? 'HTTP' : 'SOCKS5',
                            style: TextStyle(
                              fontSize: 9,
                              color: Colors.purple.shade700,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                  const SizedBox(height: 8),
                  if (account.session?.displayName != null) ...[
                    Text(
                      '用户: ${account.session!.displayName}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade700,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                  ],
                  if (account.connectedAccounts.isNotEmpty) ...[
                    Text(
                      '关联: ${account.connectedAccounts.map((e) => '${e.platformDisplayName}(${e.screenName})').join(', ')}',
                      style: TextStyle(
                        fontSize: 11,
                        color: Colors.grey.shade600,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                  ],
                  Text(
                    '最后使用: ${account.lastUsedFriendly}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                  if (account.proxy != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      '代理: ${account.proxy!.displayInfo}',
                      style: TextStyle(
                        fontSize: 11,
                        color: Colors.purple.shade600,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                  // 显示msToken状态
                  if (account.session?.userInfo['msToken'] != null) ...[
                    const SizedBox(height: 4),
                    MsTokenStatusWidget(account: account),
                  ],
                  if (account.remark != null && account.remark!.isNotEmpty) ...[
                    const SizedBox(height: 4),
                    Text(
                      '备注: ${account.remark}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],
                ],
              ),
            ),

            // 操作按钮
            if (!isSelectMode) ...[
              Column(
                children: [
                  IconButton(
                    icon: Icon(
                      account.isOnline ? Icons.visibility_off : Icons.visibility,
                      color: account.isOnline ? Colors.orange : Colors.green,
                    ),
                    onPressed: () => onAction(AccountAction.toggle),
                    tooltip: account.isOnline ? '禁用账号（保持浏览器运行）' : '启用账号',
                  ),

                  IconButton(
                    icon: Icon(Icons.refresh, color: Colors.blue.shade600),
                    onPressed: () => onAction(AccountAction.refresh),
                    tooltip: '刷新msToken',
                  ),

                  // 关闭浏览器按钮（只在账号在线时显示）
                  if (account.isOnline) ...[
                    IconButton(
                      icon: Icon(Icons.close, color: Colors.red.shade400),
                      onPressed: () => onAction(AccountAction.closeBrowser),
                      tooltip: '关闭浏览器实例',
                    ),
                  ],
                  IconButton(
                    icon: Icon(Icons.edit, color: Colors.blue.shade600),
                    onPressed: () => onAction(AccountAction.edit),
                    tooltip: '编辑账号',
                  ),
                  IconButton(
                    icon: Icon(Icons.delete, color: Colors.red.shade600),
                    onPressed: () => onAction(AccountAction.delete),
                    tooltip: '删除账号',
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    ));
  }
}
