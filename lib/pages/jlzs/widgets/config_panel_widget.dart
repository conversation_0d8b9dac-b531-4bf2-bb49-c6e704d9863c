import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '../jlzs_logic.dart';

/// 配置面板组件
class ConfigPanelWidget extends StatelessWidget {
  const ConfigPanelWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final logic = Get.find<JlzsLogic>();

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 账号管理
          _buildAccountSection(logic),
          const SizedBox(height: 16),

          // 采集配置
          _buildCollectionConfig(logic),
          const SizedBox(height: 16),

          // 功能扩展按钮
          _buildExtensionButtons(logic),
        ],
      ),
    );
  }

  /// 构建账号管理部分
  Widget _buildAccountSection(JlzsLogic logic) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.account_circle, color: Colors.purple.shade600, size: 20),
            const SizedBox(width: 8),
            const Text(
              '账号管理',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: logic.openAccountManagement,
                icon: const Icon(Icons.manage_accounts, size: 18),
                label: const Text('账号管理'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.purple.shade100,
                  foregroundColor: Colors.purple.shade700,
                  elevation: 0,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
            const SizedBox(width: 8),
            IconButton(
              onPressed: () => logic.refreshAllAccountStatus(),
              icon: const Icon(Icons.refresh, size: 18),
              tooltip: '刷新状态',
              style: IconButton.styleFrom(
                backgroundColor: Colors.purple.shade50,
                foregroundColor: Colors.purple.shade700,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),

        // 账号状态统计
        _buildAccountStats(logic),
      ],
    );
  }

  /// 构建账号状态统计
  Widget _buildAccountStats(JlzsLogic logic) {
    return GetBuilder<JlzsLogic>(
      id: 'account_stats',
      builder: (logic) {
        // 从实际的账号管理器获取数据
        var stats = logic.getAccountStats();
        int totalAccounts = stats['total'] ?? 0;
        int loggedInAccounts = stats['loggedIn'] ?? 0;
        int activeAccounts = stats['active'] ?? 0;

        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            borderRadius: BorderRadius.circular(6.0),
            border: Border.all(color: Colors.grey.shade200),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildCountItem(
                count: totalAccounts,
                label: "账号",
                color: Colors.grey.shade700!,
                icon: Icons.group,
              ),
              _buildCountItem(
                count: loggedInAccounts,
                label: "已登录",
                color: Colors.blue.shade700!,
                icon: Icons.login,
              ),
              _buildCountItem(
                count: activeAccounts,
                label: "已启用",
                color: Colors.green.shade700!,
                icon: Icons.check_circle,
              ),
            ],
          ),
        );
      },
    );
  }

  /// 构建统计项
  Widget _buildCountItem({
    required int count,
    required String label,
    required Color color,
    required IconData icon,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, color: color, size: 16),
        const SizedBox(height: 4),
        Text(
          count.toString(),
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 10,
            color: color,
          ),
        ),
      ],
    );
  }

  /// 构建采集配置部分
  Widget _buildCollectionConfig(JlzsLogic logic) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.settings, color: Colors.purple.shade600, size: 20),
            const SizedBox(width: 8),
            const Text(
              '采集配置',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        
        // 时间配置
        _buildDateConfig(logic),
        const SizedBox(height: 16),
        
        // 平台选择
        _buildPlatformConfig(logic),
        const SizedBox(height: 16),
        
        // 地区类型选择
        _buildRegionTypeConfig(logic),
        const SizedBox(height: 16),
        
        // 提取间隔
        _buildIntervalConfig(logic),
        const SizedBox(height: 16),

        // 保存路径配置
        _buildSavePathConfig(logic),
      ],
    );
  }

  /// 构建日期配置
  Widget _buildDateConfig(JlzsLogic logic) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('采集时间范围', style: TextStyle(fontSize: 13, fontWeight: FontWeight.w500)),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: InkWell(
                onTap: () => _selectDate(logic, true),
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade400),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Obx(() => Text(
                    '${logic.config.value.startDate.year}-${logic.config.value.startDate.month.toString().padLeft(2, '0')}-${logic.config.value.startDate.day.toString().padLeft(2, '0')}',
                    style: const TextStyle(fontSize: 12),
                  )),
                ),
              ),
            ),
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 8),
              child: Text('至', style: TextStyle(fontSize: 12)),
            ),
            Expanded(
              child: InkWell(
                onTap: () => _selectDate(logic, false),
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade400),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Obx(() => Text(
                    '${logic.config.value.endDate.year}-${logic.config.value.endDate.month.toString().padLeft(2, '0')}-${logic.config.value.endDate.day.toString().padLeft(2, '0')}',
                    style: const TextStyle(fontSize: 12),
                  )),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建平台配置
  Widget _buildPlatformConfig(JlzsLogic logic) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('采集平台', style: TextStyle(fontSize: 13, fontWeight: FontWeight.w500)),
        const SizedBox(height: 8),
        Obx(() => Row(
          children: [
            Expanded(
              child: RadioListTile<String>(
                title: const Text('抖音', style: TextStyle(fontSize: 12)),
                value: 'aweme',
                groupValue: logic.config.value.platform,
                onChanged: (value) => logic.setPlatform(value!),
                dense: true,
                contentPadding: EdgeInsets.zero,
              ),
            ),
            Expanded(
              child: RadioListTile<String>(
                title: const Text('头条', style: TextStyle(fontSize: 12)),
                value: 'toutiao',
                groupValue: logic.config.value.platform,
                onChanged: (value) => logic.setPlatform(value!),
                dense: true,
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        )),
      ],
    );
  }

  /// 构建地区类型配置
  Widget _buildRegionTypeConfig(JlzsLogic logic) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('地区类型', style: TextStyle(fontSize: 13, fontWeight: FontWeight.w500)),
        const SizedBox(height: 8),
        Obx(() => Column(
          children: [
            RadioListTile<String>(
              title: const Text('地区分开查询', style: TextStyle(fontSize: 12)),
              value: 'separate',
              groupValue: logic.config.value.regionType,
              onChanged: (value) => logic.setRegionType(value!),
              dense: true,
              contentPadding: EdgeInsets.zero,
            ),
            RadioListTile<String>(
              title: const Text('地区合并查询', style: TextStyle(fontSize: 12)),
              value: 'merged',
              groupValue: logic.config.value.regionType,
              onChanged: (value) => logic.setRegionType(value!),
              dense: true,
              contentPadding: EdgeInsets.zero,
            ),
          ],
        )),
      ],
    );
  }

  /// 构建间隔配置
  Widget _buildIntervalConfig(JlzsLogic logic) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('提取间隔(秒)', style: TextStyle(fontSize: 13, fontWeight: FontWeight.w500)),
        const SizedBox(height: 8),
        Obx(() => TextField(
          decoration: const InputDecoration(
            hintText: '30',
            border: OutlineInputBorder(),
            contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 10),
          ),
          style: const TextStyle(fontSize: 12),
          keyboardType: TextInputType.number,
          inputFormatters: [FilteringTextInputFormatter.digitsOnly],
          controller: TextEditingController(text: logic.config.value.extractInterval.toString()),
          onChanged: (value) {
            final interval = int.tryParse(value) ?? 30;
            logic.setExtractInterval(interval);
          },
        )),
      ],
    );
  }

  /// 构建扩展按钮部分
  Widget _buildExtensionButtons(JlzsLogic logic) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.extension, color: Colors.purple.shade600, size: 20),
            const SizedBox(width: 8),
            const Text(
              '功能扩展',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: logic.openAnalysisWindow,
                icon: const Icon(Icons.analytics, size: 16),
                label: const Text('分析', style: TextStyle(fontSize: 12)),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange.shade100,
                  foregroundColor: Colors.orange.shade700,
                  elevation: 0,
                  padding: const EdgeInsets.symmetric(vertical: 10),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: logic.openProfileWindow,
                icon: const Icon(Icons.people, size: 16),
                label: const Text('人群画像', style: TextStyle(fontSize: 12)),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.teal.shade100,
                  foregroundColor: Colors.teal.shade700,
                  elevation: 0,
                  padding: const EdgeInsets.symmetric(vertical: 10),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建保存路径配置
  Widget _buildSavePathConfig(JlzsLogic logic) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text('保存路径', style: TextStyle(fontSize: 13, fontWeight: FontWeight.w500)),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: logic.savePathController,
                decoration: const InputDecoration(
                  hintText: '选择或输入保存路径...',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                ),
                style: const TextStyle(fontSize: 12),
                onChanged: logic.onSavePathChanged,
              ),
            ),
            const SizedBox(width: 8),
            ElevatedButton.icon(
              onPressed: logic.selectSavePath,
              icon: const Icon(Icons.folder_open, size: 16),
              label: const Text('选择', style: TextStyle(fontSize: 12)),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 选择日期
  Future<void> _selectDate(JlzsLogic logic, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: Get.context!,
      initialDate: isStartDate ? logic.config.value.startDate : logic.config.value.endDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );

    if (picked != null) {
      if (isStartDate) {
        logic.setStartDate(picked);
      } else {
        logic.setEndDate(picked);
      }
    }
  }


}
