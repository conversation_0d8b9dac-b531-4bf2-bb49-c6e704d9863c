import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../jlzs_logic.dart';

/// 转换规则对话框
class ConversionRulesDialog extends StatefulWidget {
  const ConversionRulesDialog({Key? key}) : super(key: key);

  @override
  State<ConversionRulesDialog> createState() => _ConversionRulesDialogState();
}

class _ConversionRulesDialogState extends State<ConversionRulesDialog> {
  late JlzsLogic logic;
  late TextEditingController prefixController;
  late TextEditingController suffixController;
  
  bool toLowerCase = false;
  bool addPrefix = false;
  bool addSuffix = false;

  @override
  void initState() {
    super.initState();
    logic = Get.find<JlzsLogic>();
    
    // 初始化状态
    toLowerCase = logic.keywordConversionRules.value.toLowerCase;
    addPrefix = logic.keywordConversionRules.value.addPrefix;
    addSuffix = logic.keywordConversionRules.value.addSuffix;
    
    // 初始化控制器
    prefixController = TextEditingController(text: logic.keywordConversionRules.value.prefix);
    suffixController = TextEditingController(text: logic.keywordConversionRules.value.suffix);
  }

  @override
  void dispose() {
    prefixController.dispose();
    suffixController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('关键词转换规则'),
      content: SizedBox(
        width: 500,
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('当前支持的转换规则:', style: TextStyle(fontWeight: FontWeight.w600)),
              const SizedBox(height: 12),
              
              _buildRuleItem('1. 分隔符处理', '支持换行、空格、逗号、分号分隔'),
              _buildRuleItem('2. 去重处理', '自动去除重复的关键词'),
              _buildRuleItem('3. 空白处理', '自动去除前后空白字符'),
              _buildRuleItem('4. 空值过滤', '自动过滤空的关键词'),
              
              const SizedBox(height: 16),
              const Text('高级转换规则:', style: TextStyle(fontWeight: FontWeight.w600)),
              const SizedBox(height: 8),
              
              CheckboxListTile(
                title: const Text('启用大小写统一', style: TextStyle(fontSize: 14)),
                subtitle: const Text('将所有关键词转换为小写', style: TextStyle(fontSize: 12)),
                value: toLowerCase,
                onChanged: (value) {
                  setState(() {
                    toLowerCase = value ?? false;
                  });
                },
                dense: true,
              ),
              
              CheckboxListTile(
                title: const Text('启用前缀添加', style: TextStyle(fontSize: 14)),
                subtitle: const Text('为每个关键词添加指定前缀', style: TextStyle(fontSize: 12)),
                value: addPrefix,
                onChanged: (value) {
                  setState(() {
                    addPrefix = value ?? false;
                  });
                },
                dense: true,
              ),
              
              if (addPrefix) ...[
                const SizedBox(height: 8),
                TextField(
                  controller: prefixController,
                  decoration: const InputDecoration(
                    labelText: '前缀内容',
                    hintText: '例如: #',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                  style: const TextStyle(fontSize: 14),
                ),
              ],
              
              CheckboxListTile(
                title: const Text('启用后缀添加', style: TextStyle(fontSize: 14)),
                subtitle: const Text('为每个关键词添加指定后缀', style: TextStyle(fontSize: 12)),
                value: addSuffix,
                onChanged: (value) {
                  setState(() {
                    addSuffix = value ?? false;
                  });
                },
                dense: true,
              ),
              
              if (addSuffix) ...[
                const SizedBox(height: 8),
                TextField(
                  controller: suffixController,
                  decoration: const InputDecoration(
                    labelText: '后缀内容',
                    hintText: '例如: _keyword',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                  style: const TextStyle(fontSize: 14),
                ),
              ],
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Get.back(),
          child: const Text('取消'),
        ),
        ElevatedButton(
          onPressed: () {
            // 应用转换规则
            _applyRules();
            Get.back();
            Get.snackbar('成功', '转换规则已更新', snackPosition: SnackPosition.TOP);
          },
          child: const Text('应用'),
        ),
      ],
    );
  }

  /// 应用转换规则
  void _applyRules() {
    logic.updateConversionRule('toLowerCase', toLowerCase);
    logic.updateConversionRule('addPrefix', addPrefix);
    logic.updateConversionRule('prefix', prefixController.text);
    logic.updateConversionRule('addSuffix', addSuffix);
    logic.updateConversionRule('suffix', suffixController.text);
    
    // 重新应用转换规则
    logic.reapplyConversionRules();
  }

  /// 构建规则项
  Widget _buildRuleItem(String title, String description) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(Icons.check_circle, color: Colors.green.shade600, size: 16),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(title, style: const TextStyle(fontSize: 13, fontWeight: FontWeight.w500)),
                Text(description, style: TextStyle(fontSize: 12, color: Colors.grey.shade600)),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
