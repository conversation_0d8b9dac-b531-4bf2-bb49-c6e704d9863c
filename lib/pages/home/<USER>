import 'package:bd/exports.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'home_logic.dart';

class HomePage extends StatelessWidget {
  HomePage({Key? key}) : super(key: key);

  final logic = Get.find<HomeLogic>();


  @override
  Widget build(BuildContext context) {
    print('object');
    return Material(
      child: Scaffold(
        body: GetBuilder<HomeLogic>(
            id: 'sideMenu',
            builder: (logic) {
          return Padding(
              padding: const EdgeInsets.all(8.0),
              child: Wrap(
              spacing: 8.0, // 水平方向上的间距
              runSpacing: 8.0, // 垂直方向上的间距
              children: List.generate(logic.navItems.length, (index) {
              return SizedBox(
              width: (MediaQuery.of(context).size.width / 1) - 16, // 设置每个按钮的宽度，使其成为两列
              child: ElevatedButton(
              onPressed: () { logic.onNavItemTap(index); },
              child: Text('${logic.navItems[index].name}'),
              ),
              );
              })));
        },),
      ),
    );
  }
}



