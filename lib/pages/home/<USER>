import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:desktop_multi_window/desktop_multi_window.dart';
import '../../utils/multi_window_manager.dart';
import '../../routes/app_routes.dart';
import '../../utils/window_utils.dart';

// import '../download/download_view.dart';
// import '../settings/settings_view.dart';

class HomeLogic extends GetxController {

  final navItems =  [
    NavItemModel(name: '百度指数', icon: Icons.analytics, isSelected: true, index: 0),
    NavItemModel(name: '设置', icon: Icons.settings, isSelected: false, index: 1),
    NavItemModel(name: '数据分析', icon: Icons.bar_chart, isSelected: false, index: 2),
    NavItemModel(name: '巨量指数', icon: Icons.trending_up, isSelected: false, index: 3),
  ];
  @override
  void onInit() {
    super.onInit();
    _setupMessageHandler();
  }

  /// 处理导航项点击
  void onNavItemTap(int index) {
    switch (index) {
      case 0:
        // 百度指数 - 保持原有逻辑，完全不变
        _startBdIndex();
        break;
      case 1:
        // 设置功能 - 使用多窗口
        _openSettings();
        break;
      case 2:
        // 数据分析功能 - 使用多窗口
        _openDataAnalysis();
        break;
      case 3:
        // 巨量指数功能 - 使用多窗口
        _openJlzs();
        break;
    }
  }

  /// 启动百度指数 (保持原有逻辑)
  void _startBdIndex() {
    Get.toNamed(AppRoutes.bdIndex);
    // 保持原有的窗口尺寸调整逻辑
    if (GetPlatform.isDesktop) {
      WindowManagerUtils.instance.setSize(1000, 800);
    }
  }

  /// 打开设置窗口 (新功能)
  void _openSettings() {
    MultiWindowManager().openSettingsWindow();
  }

  /// 打开数据分析窗口 (新功能)
  void _openDataAnalysis() {
    MultiWindowManager().openDataAnalysisWindow();
  }

  /// 打开巨量指数窗口 (新功能)
  void _openJlzs() {
    MultiWindowManager().openJlzsWindow();
  }

  /// 设置消息处理器，监听来自设置窗口的消息
  void _setupMessageHandler() {
    DesktopMultiWindow.setMethodHandler((call, fromWindowId) async {
      switch (call.method) {
        case 'settingsChanged':
          _handleSettingsChanged(call.arguments);
          break;
        case 'dataAnalysisAction':
          _handleDataAnalysisAction(call.arguments);
          break;
        case 'jlzsAction':
          _handleJlzsAction(call.arguments);
          break;
        case 'windowClosed':
          _handleWindowClosed(call.arguments);
          break;
        case 'window_closed':
          _handleWindowClosedFromManager(call.arguments);
          break;
      }
      return 'ok';
    });
  }

  /// 处理设置变更
  void _handleSettingsChanged(dynamic data) {
    print('收到设置变更通知: $data');
    // 这里可以根据设置变更更新主窗口的显示
    // 比如主题变更、语言变更等
  }

  /// 处理数据分析操作
  void _handleDataAnalysisAction(dynamic data) {
    print('收到数据分析操作通知: $data');
    // 这里可以处理数据分析窗口的操作反馈
    // 比如数据导出完成、分析结果更新等
  }

  /// 处理巨量指数操作
  void _handleJlzsAction(dynamic data) {
    print('收到巨量指数操作通知: $data');
    // 这里可以处理巨量指数窗口的操作反馈
    // 比如数据采集完成、分析结果更新等
  }

  /// 处理窗口关闭
  void _handleWindowClosed(dynamic data) {
    print('窗口已关闭: $data');
    // 可以在这里更新主窗口的状态
  }

  /// 处理来自多窗口管理器的窗口关闭通知
  void _handleWindowClosedFromManager(dynamic data) {
    print('收到多窗口管理器的窗口关闭通知: $data');
    final windowTypeStr = data['windowType'] as String?;
    if (windowTypeStr != null) {
      // 通知多窗口管理器清理窗口记录
      MultiWindowManager().handleWindowClosed(windowTypeStr);
    }
  }





}


class NavItemModel {
   NavItemModel({
    required this.index,
    required this.name,
    required this.icon,
    required this.isSelected,
  });

  final String name;
  final int index;
  final IconData icon;
  bool    isSelected;
}
