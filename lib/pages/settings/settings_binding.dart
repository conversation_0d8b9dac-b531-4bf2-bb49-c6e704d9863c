import 'package:get/get.dart';
import 'package:desktop_multi_window/desktop_multi_window.dart';
import 'settings_controller.dart';

/// 设置绑定 - GetX依赖注入
class SettingsBinding extends Bindings {
  final WindowController? windowController;

  SettingsBinding({this.windowController});

  @override
  void dependencies() {
    Get.lazyPut(() {
      final controller = SettingsController();
      controller.windowController = windowController;
      return controller;
    });
  }
}
