import 'package:get/get.dart';
import 'package:desktop_multi_window/desktop_multi_window.dart';
import 'package:flutter/services.dart';
import '../../utils/chrome_manager.dart';

/// 设置控制器 - 标准的GetX控制器
class SettingsController extends GetxController {
  // 设置状态
  final isDarkMode = false.obs;
  final language = 'zh_CN'.obs;
  final autoStart = false.obs;
  final enableNotifications = true.obs;
  final windowAlwaysOnTop = false.obs;

  // 浏览器管理状态
  final chromeManager = ChromeManager();
  final localChromeVersions = <ChromeVersion>[].obs;
  final availableVersions = <String>[].obs;
  final chromeInfo = <String, dynamic>{}.obs;
  final isLoadingChromeVersions = false.obs;
  final isDownloading = false.obs;
  final downloadProgress = 0.0.obs;
  final downloadingVersion = ''.obs;

  // 窗口相关
  final isWindowClosing = false.obs;
  WindowController? windowController;

  @override
  void onInit() {
    super.onInit();
    _loadSettings();
    _loadChromeVersions();
  }

  /// 加载设置
  void _loadSettings() {
    // 这里可以从本地存储加载设置
    // 例如使用 SharedPreferences 或 Hive
    print('加载设置...');
  }

  /// 切换主题
  void toggleTheme(bool value) {
    isDarkMode.value = value;
    _saveSettings();
    _notifyMainWindow();
    update(); // 更新UI
  }

  /// 切换语言
  void changeLanguage(String lang) {
    language.value = lang;
    _saveSettings();
    _notifyMainWindow();
    update();
  }

  /// 切换自动启动
  void toggleAutoStart(bool value) {
    autoStart.value = value;
    _saveSettings();
    update();
  }

  /// 切换通知
  void toggleNotifications(bool value) {
    enableNotifications.value = value;
    _saveSettings();
    update();
  }

  /// 切换窗口置顶
  void toggleAlwaysOnTop(bool value) {
    windowAlwaysOnTop.value = value;
    _saveSettings();
    update();
  }

  /// 保存设置
  void _saveSettings() {
    // 这里实现设置的持久化存储
    print('保存设置: 主题=${isDarkMode.value}, 语言=${language.value}');
  }

  /// 通知主窗口设置发生变化
  void _notifyMainWindow() {
    final settingsData = {
      'isDarkMode': isDarkMode.value,
      'language': language.value,
      'autoStart': autoStart.value,
      'enableNotifications': enableNotifications.value,
      'windowAlwaysOnTop': windowAlwaysOnTop.value,
    };

    // 发送消息到主窗口
    DesktopMultiWindow.invokeMethod(
      0, // 主窗口ID通常是0
      'settingsChanged',
      settingsData,
    );
  }

  /// 关闭窗口
  void closeWindow() {
    if (isWindowClosing.value) return;

    isWindowClosing.value = true;

    // 直接关闭窗口，无需复杂的通知机制
    if (windowController != null) {
      windowController!.close();
    } else {
      SystemNavigator.pop();
    }
  }

  /// 重置所有设置
  void resetSettings() {
    isDarkMode.value = false;
    language.value = 'zh_CN';
    autoStart.value = false;
    enableNotifications.value = true;
    windowAlwaysOnTop.value = false;
    
    _saveSettings();
    _notifyMainWindow();
    update();
    
    Get.snackbar(
      '设置重置',
      '所有设置已重置为默认值',
      snackPosition: SnackPosition.TOP,
    );
  }

  /// 应用设置
  void applySettings() {
    _saveSettings();
    _notifyMainWindow();
    
    Get.snackbar(
      '设置已保存',
      '设置已成功应用',
      snackPosition: SnackPosition.TOP,
    );
  }

  /// 加载Chrome版本信息
  Future<void> _loadChromeVersions() async {
    try {
      isLoadingChromeVersions.value = true;

      // 加载本地版本
      var localVersions = await chromeManager.getLocalVersions();
      localChromeVersions.value = localVersions;

      // 加载Chrome信息
      var info = await chromeManager.getChromeInfo();
      chromeInfo.value = info;

      print('已加载 ${localVersions.length} 个本地Chrome版本');
    } catch (e) {
      print('加载Chrome版本失败: $e');
    } finally {
      isLoadingChromeVersions.value = false;
    }
  }

  /// 刷新Chrome版本信息
  Future<void> refreshChromeVersions() async {
    await _loadChromeVersions();
    Get.snackbar('成功', '已刷新Chrome版本信息', snackPosition: SnackPosition.TOP);
  }

  /// 清理不完整的Chrome版本
  Future<void> cleanupIncompleteVersions() async {
    try {
      int cleanedCount = await chromeManager.cleanupIncompleteVersions();
      if (cleanedCount > 0) {
        Get.snackbar('成功', '已清理 $cleanedCount 个不完整版本', snackPosition: SnackPosition.TOP);
        await _loadChromeVersions(); // 刷新版本列表
      } else {
        Get.snackbar('提示', '没有发现不完整的版本', snackPosition: SnackPosition.TOP);
      }
    } catch (e) {
      print('清理不完整版本失败: $e');
      Get.snackbar('错误', '清理失败: $e', snackPosition: SnackPosition.TOP);
    }
  }

  /// 删除Chrome版本
  Future<void> deleteChromeVersion(String version) async {
    try {
      bool success = await chromeManager.deleteVersion(version);
      if (success) {
        Get.snackbar('成功', '已删除Chrome版本 $version', snackPosition: SnackPosition.TOP);
        await _loadChromeVersions(); // 刷新本地版本列表
      } else {
        Get.snackbar('错误', '删除Chrome版本失败', snackPosition: SnackPosition.TOP);
      }
    } catch (e) {
      print('删除Chrome版本失败: $e');
      Get.snackbar('错误', '删除失败: $e', snackPosition: SnackPosition.TOP);
    }
  }

  /// 获取可用的Chrome版本
  Future<void> loadAvailableVersions() async {
    try {
      isLoadingChromeVersions.value = true;
      var versions = await chromeManager.getAvailableVersions();
      availableVersions.value = versions;
      print('已加载 ${versions.length} 个可用Chrome版本');
    } catch (e) {
      print('加载可用版本失败: $e');
      Get.snackbar('错误', '获取可用版本失败: $e', snackPosition: SnackPosition.TOP);
    } finally {
      isLoadingChromeVersions.value = false;
    }
  }

  /// 下载Chrome（使用Puppeteer）
  Future<void> downloadChrome({String? version}) async {
    try {
      isDownloading.value = true;
      downloadingVersion.value = version ?? 'Puppeteer默认版本';
      downloadProgress.value = 0.0;

      bool success = await chromeManager.downloadChromeWithPuppeteer(
        version: version,
        onProgress: (received, total, progress) {
          downloadProgress.value = progress;
        },
      );

      if (success) {
        Get.snackbar('成功', 'Chrome ${downloadingVersion.value} 下载完成',
                    snackPosition: SnackPosition.TOP);
        await _loadChromeVersions(); // 刷新本地版本列表
      } else {
        Get.snackbar('错误', 'Chrome ${downloadingVersion.value} 下载失败',
                    snackPosition: SnackPosition.TOP);
      }
    } catch (e) {
      print('下载Chrome失败: $e');
      Get.snackbar('错误', '下载失败: $e', snackPosition: SnackPosition.TOP);
    } finally {
      isDownloading.value = false;
      downloadingVersion.value = '';
      downloadProgress.value = 0.0;
    }
  }

  /// 获取推荐的Chrome版本
  Future<ChromeVersion?> getRecommendedChromeVersion() async {
    return await chromeManager.getRecommendedVersion();
  }
}
