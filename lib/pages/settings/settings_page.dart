import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:desktop_multi_window/desktop_multi_window.dart';
import 'settings_controller.dart';
import '../../utils/chrome_manager.dart';

/// 设置页面 - 使用GetX控制器
class SettingsPage extends StatelessWidget {
  final WindowController? windowController;

  const SettingsPage({Key? key, this.windowController}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<SettingsController>( // 使用GetBuilder
      builder: (controller) => Scaffold(
        appBar: AppBar(
          title: const Text('系统设置'),
          actions: [
            IconButton(
              icon: const Icon(Icons.close),
              onPressed: controller.closeWindow,
              tooltip: '关闭窗口',
            ),
          ],
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 外观设置
              _buildSectionTitle('外观设置'),
              _buildSettingTile(
                title: '深色主题',
                subtitle: '启用深色模式界面',
                trailing: Switch(
                  value: controller.isDarkMode.value,
                  onChanged: controller.toggleTheme,
                ),
              ),
              _buildSettingTile(
                title: '语言设置',
                subtitle: '当前语言: ${_getLanguageName(controller.language.value)}',
                trailing: DropdownButton<String>(
                  value: controller.language.value,
                  items: const [
                    DropdownMenuItem(value: 'zh_CN', child: Text('简体中文')),
                    DropdownMenuItem(value: 'en_US', child: Text('English')),
                  ],
                  onChanged: (value) {
                    if (value != null) {
                      controller.changeLanguage(value);
                    }
                  },
                ),
              ),
              
              const Divider(height: 32),
              
              // 系统设置
              _buildSectionTitle('系统设置'),
              _buildSettingTile(
                title: '开机自启动',
                subtitle: '系统启动时自动运行程序',
                trailing: Switch(
                  value: controller.autoStart.value,
                  onChanged: controller.toggleAutoStart,
                ),
              ),
              _buildSettingTile(
                title: '通知提醒',
                subtitle: '启用系统通知',
                trailing: Switch(
                  value: controller.enableNotifications.value,
                  onChanged: controller.toggleNotifications,
                ),
              ),
              _buildSettingTile(
                title: '窗口置顶',
                subtitle: '保持窗口在最前面',
                trailing: Switch(
                  value: controller.windowAlwaysOnTop.value,
                  onChanged: controller.toggleAlwaysOnTop,
                ),
              ),

              const SizedBox(height: 24),

              // 浏览器管理
              _buildSectionTitle('浏览器管理'),
              _buildBrowserManagement(controller),

              const SizedBox(height: 32),

              // 底部按钮
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  ElevatedButton(
                    onPressed: controller.resetSettings,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.grey[300],
                      foregroundColor: Colors.black87,
                    ),
                    child: const Text('重置设置'),
                  ),
                  ElevatedButton(
                    onPressed: controller.applySettings,
                    child: const Text('应用设置'),
                  ),
                ],
              ),
              
              const SizedBox(height: 16),

              // 底部间距
              const SizedBox(height: 32),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Colors.blue,
        ),
      ),
    );
  }

  Widget _buildSettingTile({
    required String title,
    required String subtitle,
    required Widget trailing,
  }) {
    return ListTile(
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: trailing,
      contentPadding: const EdgeInsets.symmetric(horizontal: 0),
    );
  }

  String _getLanguageName(String languageCode) {
    switch (languageCode) {
      case 'zh_CN':
        return '简体中文';
      case 'en_US':
        return 'English';
      default:
        return '简体中文';
    }
  }

  /// 构建浏览器管理部分
  Widget _buildBrowserManagement(SettingsController controller) {
    return Obx(() => Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.web, color: Colors.blue),
                const SizedBox(width: 8),
                const Text(
                  'Puppeteer Chrome管理',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.refresh),
                  onPressed: controller.refreshChromeVersions,
                  tooltip: '刷新版本信息',
                ),
              ],
            ),
            const SizedBox(height: 12),

            // Chrome信息概览
            if (controller.chromeInfo.isNotEmpty) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info_outline, color: Colors.blue.shade600),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        '共 ${controller.chromeInfo['totalVersions']} 个版本，'
                        '其中 ${controller.chromeInfo['availableVersions']} 个可用，'
                        '占用空间 ${controller.chromeInfo['totalSizeMB']}MB',
                        style: TextStyle(color: Colors.blue.shade700),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
            ],

            // 管理操作按钮
            Wrap(
              spacing: 12,
              runSpacing: 8,
              children: [
                ElevatedButton.icon(
                  onPressed: controller.isDownloading.value
                    ? null
                    : () => controller.downloadChrome(),
                  icon: const Icon(Icons.download, size: 18),
                  label: const Text('下载默认版本'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue.shade100,
                    foregroundColor: Colors.blue.shade700,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: controller.isDownloading.value
                    ? null
                    : () => _showVersionSelectionDialog(controller),
                  icon: const Icon(Icons.download_for_offline, size: 18),
                  label: const Text('下载指定版本'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green.shade100,
                    foregroundColor: Colors.green.shade700,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: controller.cleanupIncompleteVersions,
                  icon: const Icon(Icons.cleaning_services, size: 18),
                  label: const Text('清理不完整版本'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange.shade100,
                    foregroundColor: Colors.orange.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              '推荐版本: ${controller.chromeInfo['recommendedVersion'] ?? '无'}',
              style: TextStyle(
                color: Colors.green.shade700,
                fontWeight: FontWeight.w500,
              ),
            ),

            // 下载进度
            if (controller.isDownloading.value) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('正在下载 Chrome ${controller.downloadingVersion.value}...'),
                    const SizedBox(height: 8),
                    LinearProgressIndicator(
                      value: controller.downloadProgress.value,
                    ),
                    const SizedBox(height: 4),
                    Text('${(controller.downloadProgress.value * 100).toStringAsFixed(1)}%'),
                  ],
                ),
              ),
            ],

            const SizedBox(height: 16),

            // 本地版本列表
            if (controller.localChromeVersions.isNotEmpty) ...[
              const Text(
                'Puppeteer已下载版本:',
                style: TextStyle(fontWeight: FontWeight.w500),
              ),
              const SizedBox(height: 8),
              if (controller.isLoadingChromeVersions.value)
                const Center(child: CircularProgressIndicator())
              else
                ...controller.localChromeVersions.map((version) =>
                  _buildVersionTile(version, controller)
                ),
            ] else if (!controller.isLoadingChromeVersions.value) ...[
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Row(
                  children: [
                    Icon(Icons.info_outline, color: Colors.grey),
                    SizedBox(width: 8),
                    Text('未发现Puppeteer下载的Chrome版本'),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    ));
  }

  /// 构建版本条目
  Widget _buildVersionTile(ChromeVersion version, SettingsController controller) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: ListTile(
        leading: Icon(
          version.isExtracted ? Icons.check_circle : Icons.error,
          color: version.isExtracted ? Colors.green : Colors.orange,
        ),
        title: Text('Chrome ${version.version}'),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(version.statusDescription),
            if (version.lastUsed != null)
              Text(
                '最后使用: ${_formatDateTime(version.lastUsed!)}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                ),
              ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (version.isExtracted)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.green.shade100,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '推荐',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.green.shade700,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            const SizedBox(width: 8),
            IconButton(
              icon: const Icon(Icons.delete, color: Colors.red),
              onPressed: () => _showDeleteConfirmDialog(controller, version.version),
              tooltip: '删除此版本',
            ),
          ],
        ),
      ),
    );
  }

  /// 格式化日期时间
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
           '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// 显示版本选择对话框
  void _showVersionSelectionDialog(SettingsController controller) {
    Get.dialog(
      AlertDialog(
        title: const Text('选择Chrome版本'),
        content: SizedBox(
          width: 400,
          height: 300,
          child: Column(
            children: [
              Row(
                children: [
                  const Text('可用版本:'),
                  const Spacer(),
                  TextButton(
                    onPressed: controller.loadAvailableVersions,
                    child: const Text('刷新列表'),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Expanded(
                child: Obx(() {
                  if (controller.isLoadingChromeVersions.value) {
                    return const Center(child: CircularProgressIndicator());
                  } else if (controller.availableVersions.isEmpty) {
                    return const Center(
                      child: Text('点击"刷新列表"获取可用版本'),
                    );
                  } else {
                    return ListView.builder(
                      itemCount: controller.availableVersions.length,
                      itemBuilder: (context, index) {
                        String version = controller.availableVersions[index];
                        return ListTile(
                          title: Text('Chrome $version'),
                          trailing: ElevatedButton(
                            onPressed: controller.isDownloading.value
                              ? null
                              : () {
                                  Get.back();
                                  controller.downloadChrome(version: version);
                                },
                            child: const Text('下载'),
                          ),
                        );
                      },
                    );
                  }
                }),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
        ],
      ),
    );
  }

  /// 显示删除确认对话框
  void _showDeleteConfirmDialog(SettingsController controller, String version) {
    Get.dialog(
      AlertDialog(
        title: const Text('确认删除'),
        content: Text('确定要删除Chrome版本 $version 吗？\n\n此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              controller.deleteChromeVersion(version);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }
}
