import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:desktop_multi_window/desktop_multi_window.dart';
import 'settings_page.dart';
import 'settings_binding.dart';

/// 设置窗口应用 - 完全使用GetX
class SettingsWindowApp extends StatelessWidget {
  final Map<String, dynamic> args;
  final WindowController? windowController;

  const SettingsWindowApp({
    Key? key,
    required this.args,
    this.windowController,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(  // 使用GetMaterialApp
      title: '设置管理',
      theme: ThemeData(
        fontFamily: "Alibaba",
        primarySwatch: Colors.blue,
      ),
      home: SettingsPage(windowController: windowController),
      initialBinding: SettingsBinding(windowController: windowController), // GetX绑定
      debugShowCheckedModeBanner: false,
    );
  }
}
