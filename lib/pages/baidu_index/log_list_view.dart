
import 'package:bd/pages/baidu_index/baidu_index_logic.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';


class LogListView extends StatelessWidget {
  const LogListView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Container(
        decoration: BoxDecoration(
          color: Colors.grey.shade50,
          borderRadius: const BorderRadius.only(
            bottomLeft: Radius.circular(8),
            bottomRight: Radius.circular(8),
          ),
        ),
        child: GetBuilder<BaiduIndexLogic>(
          id: 'logs',
          builder: (logic) {
            return Scrollbar(
              controller: logic.logScrollController,
              thickness: 6,
              radius: const Radius.circular(3),
              child: ListView.builder(
                controller: logic.logScrollController,
                padding: const EdgeInsets.all(16),
                itemCount: logic.logs.length,
                itemBuilder: (context, index) {
                  return LogItem(log: logic.logs[index]);
                },
              ),
            );
          },
        ),
      ),
    );
  }
}

class LogItem extends StatelessWidget {
  final String log;

  const LogItem({
    Key? key,
    required this.log,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final parts = log.split(']');
    final timestamp = parts[0].substring(1);
    final content = parts.length > 1 ? parts[1].trim() : '';
    final isError = log.contains('错误');
    final isSuccess = log.contains('成功');

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(
          color: isError
              ? Colors.red.shade100
              : isSuccess
              ? Colors.green.shade100
              : Colors.grey.shade200,
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 时间戳
          Text(
            timestamp,
            style: TextStyle(
              fontSize: 11,
              color: Colors.grey[600],
              fontFamily: 'Consolas',
            ),
          ),
          const SizedBox(width: 12),
          // 日志内容
          Expanded(
            child: Text(
              content,
              style: TextStyle(
                fontSize: 12,
                height: 1.4,
                color: isError
                    ? Colors.red[700]
                    : isSuccess
                    ? Colors.green[700]
                    : Colors.grey[800],
                fontFamily: 'Consolas',
              ),
            ),
          ),
        ],
      ),
    );
  }
}

