// ignore_for_file: prefer_const_literals_to_create_immutables
import 'package:dio5_log/dio_log.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'baidu_index_logic.dart';
import 'widgets/keyword_input_section.dart';
import 'widgets/config_management_section.dart';
import 'widgets/details_logs_section.dart';

class BaiduIndexPage extends StatelessWidget {
  BaiduIndexPage({Key? key}) : super(key: key);

  final logic = Get.find<BaiduIndexLogic>();
  final ScrollController _scrollController = ScrollController();

  @override
  Widget build(BuildContext context) {
    DioLogInterceptor.enablePrintLog = false;
    logic.httpClientUtil.dio.interceptors.add(DioLogInterceptor());
    showDebugBtn(context, btnColor: Colors.blue, btnSize: 50);
    return Scaffold(
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Row(
          children: [
            // 第一�?- 关键词输入区�?
            KeywordInputSection(
              logic: logic,
              scrollController: _scrollController,
            ),
            SizedBox(width: 10,),
            // 第二�?- 配置管理区域
            Expanded(
              flex: 1,
              child: ConfigManagementSection(logic: logic),
            ),
            SizedBox(width: 10,),
            // 第三�?- 详情和日志区�?
            Expanded(
              flex: 2,
              child: DetailsLogsSection(logic: logic),
            ),
          ],
        ),
      ),
    );
  }
}

