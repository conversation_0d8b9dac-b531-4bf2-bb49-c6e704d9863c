import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tekflat_design/tekflat_design.dart';
import 'package:bd/model/baidu_user_model.dart';
import 'package:bd/utils/toast_util.dart';
import '../../../model/baidu_model.dart';
import '../baidu_index_logic.dart';

/// 代理对话框组件
/// 职责：所有代理相关对话框（单个代理设置、批量代理导入、代理配置）
class ProxyDialogsWidget {
  final BaiduIndexLogic logic;

  ProxyDialogsWidget({required this.logic});

  /// 显示单个代理设置对话框
  void showProxyDialog(BuildContext context, BaiDuUsers user) {
    final formKey = GlobalKey<FormState>();
    final proxyController = TextEditingController(text: user.proxyAddress);
    final portController = TextEditingController(text: user.proxyPort);
    final proxyUsernameController = TextEditingController(text: user.proxyUsername);
    final proxyPasswordController = TextEditingController(text: user.proxyPassword);

    // 定义有效时间选项（分钟）
    final List<int> validTimeOptions = [5, 10, 15, 30, 45, 60, 90, 120, 180, 240, 300, 360];
    // 默认选择60分钟或最接近的现有设置
    int selectedMinutes = user.proxyValidTime ?? 60;

    TekDialogs.defaultDialog(
      context,
      width: 400,
      content: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TekTypography(
                text: "代理设置",
                type: TekTypographyType.headline,
              ),
              SizedBox(height: 16),

              // 代理基本信息
              Row(
                children: [
                  Expanded(
                    flex: 3,
                    child: TekInput(
                      labelText: "代理地址",
                      controller: proxyController,
                      hintText: "例如: 127.0.0.1",
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return '请输入代理地址';
                        }
                        return null;
                      },
                    ),
                  ),
                  SizedBox(width: 8),
                  Expanded(
                    flex: 1,
                    child: TekInput(
                      labelText: "端口",
                      controller: portController,
                      hintText: "例如: 7890",
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return '请输入端口';
                        }
                        if (int.tryParse(value) == null) {
                          return '请输入有效端口';
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),
              SizedBox(height: 12),

              // 代理认证信息
              Row(
                children: [
                  Expanded(
                    child: TekInput(
                      labelText: "代理用户名",
                      controller: proxyUsernameController,
                      hintText: "可选",
                    ),
                  ),
                  SizedBox(width: 8),
                  Expanded(
                    child: TekInput(
                      labelText: "代理密码",
                      controller: proxyPasswordController,
                      hintText: "可选",
                      obscureText: true,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 12),

              // 代理有效时间下拉框
              GetBuilder<BaiduIndexLogic>(
                  id: "proxy_dialog",
                  init: logic,
                  builder: (logic) {
                    return Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "代理有效时间",
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey[700],
                                ),
                              ),
                              SizedBox(height: 4),
                              Container(
                                decoration: BoxDecoration(
                                  border: Border.all(color: Colors.grey.shade300),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: DropdownButtonHideUnderline(
                                  child: DropdownButton<int>(
                                    value: selectedMinutes,
                                    isExpanded: true,
                                    padding: EdgeInsets.symmetric(horizontal: 12),
                                    items: validTimeOptions.map((int minutes) {
                                      String displayText = minutes >= 60
                                          ? "${minutes ~/ 60}小时${minutes % 60 > 0 ? ' ${minutes % 60}分钟' : ''}"
                                          : "$minutes分钟";
                                      return DropdownMenuItem<int>(
                                        value: minutes,
                                        child: Text(displayText),
                                      );
                                    }).toList(),
                                    onChanged: (int? newValue) {
                                      if (newValue != null) {
                                        selectedMinutes = newValue;
                                        logic.update(['proxy_dialog']);
                                      }
                                    },
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    );
                  }),

              SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TekButton(
                    type: TekButtonType.danger,
                    text: "取消",
                    onPressed: () => Navigator.pop(context),
                  ),
                  SizedBox(width: 8),
                  TekButton(
                    type: TekButtonType.info,
                    text: "确认",
                    onPressed: () => _handleProxyConfirm(
                      context, 
                      formKey, 
                      user, 
                      proxyController, 
                      portController, 
                      proxyUsernameController, 
                      proxyPasswordController, 
                      selectedMinutes
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 处理代理设置确认
  void _handleProxyConfirm(
    BuildContext context,
    GlobalKey<FormState> formKey,
    BaiDuUsers user,
    TextEditingController proxyController,
    TextEditingController portController,
    TextEditingController proxyUsernameController,
    TextEditingController proxyPasswordController,
    int selectedMinutes,
  ) async {
    if (formKey.currentState!.validate()) {
      // 先检测代理是否可用
      showToast("正在检测代理...");
      bool isProxyValid = await logic.checkProxy(
        proxyController.text,
        portController.text,
        proxyUsernameController.text,
        proxyPasswordController.text,
      );
      dismissAllToast();

      if (!isProxyValid) {
        showToast("代理连接失败，请检查代理设置");
        return;
      }

      // 代理可用，更新信息
      user.proxyAddress = proxyController.text;
      user.proxyPort = portController.text;
      user.proxyUsername = proxyUsernameController.text;
      user.proxyPassword = proxyPasswordController.text;
      user.proxyValidTime = selectedMinutes;
      user.proxyStartTime = DateTime.now();
      user.isProxy = true;

      logic.update(['list', 'proxy_dialog', 'now', 'three']);
      Navigator.pop(context);
      showToast("代理设置成功");
      logic.addLog("✅ 账号:${user.username}---代理设置成功");
    }
  }

  /// 显示批量代理设置对话框
  void showBatchProxyDialog(BuildContext context) {
    final textController = TextEditingController();
    final List<int> validTimeOptions = [5, 10, 15, 30, 45, 60, 90, 120, 180, 240, 300, 360];
    int selectedMinutes = 60;

    TekDialogs.defaultDialog(
      context,
      width: 600,
      content: GetBuilder<BaiduIndexLogic>(
        id: "proxy_dialog",
        builder: (logic) {
          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TekTypography(
                  text: "批量导入代理",
                  type: TekTypographyType.headline,
                ),
                SizedBox(height: 16),

                // 文本输入区域
                TekInput(
                  labelText: "代理列表",
                  controller: textController,
                  maxLines: 10,
                  hintText: "每行一个代理，格式：IP:端口 或 IP:端口:用户名:密码",
                ),

                SizedBox(height: 12),

                // 添加有效期选择
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "代理有效时间",
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[700],
                      ),
                    ),
                    SizedBox(height: 4),
                    Container(
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: DropdownButtonHideUnderline(
                        child: DropdownButton<int>(
                          value: selectedMinutes,
                          isExpanded: true,
                          padding: EdgeInsets.symmetric(horizontal: 12),
                          items: validTimeOptions.map((int minutes) {
                            String displayText = minutes >= 60
                                ? "${minutes ~/ 60}小时${minutes % 60 > 0 ? ' ${minutes % 60}分钟' : ''}"
                                : "$minutes分钟";
                            return DropdownMenuItem<int>(
                              value: minutes,
                              child: Text(displayText),
                            );
                          }).toList(),
                          onChanged: (int? newValue) {
                            if (newValue != null) {
                              selectedMinutes = newValue;
                              logic.update(['proxy_dialog']);
                            }
                          },
                        ),
                      ),
                    ),
                  ],
                ),

                SizedBox(height: 24),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TekButton(
                      type: TekButtonType.danger,
                      text: "取消",
                      onPressed: () => Navigator.pop(context),
                    ),
                    SizedBox(width: 8),
                    TekButton(
                      type: TekButtonType.info,
                      text: "确认导入",
                      onPressed: () => _handleBatchProxyImport(context, textController, selectedMinutes),
                    ),
                  ],
                ),
              ],
            ),
          );
        }
      ),
    );
  }

  /// 处理批量代理导入
  void _handleBatchProxyImport(BuildContext context, TextEditingController textController, int selectedMinutes) async {
    final lines = textController.text.split('\n')
        .where((line) => line.trim().isNotEmpty);
    final proxies = lines
        .map((line) => ProxyConfig.fromString(line))
        .where((proxy) => proxy != null)
        .cast<ProxyConfig>()
        .toList();
    if (proxies.isEmpty) {
      showToast("没有有效的代理配置");
      return;
    }
    print("selectedMinutes ---${selectedMinutes}");
    await logic.importProxies(proxies, selectedMinutes);
    Navigator.pop(context);
  }

  /// 显示代理配置对话框
  void showPzProxyDialog(BuildContext context) {
    final textController = TextEditingController();
    final List<int> validTimeOptions = [1,5, 10, 15, 30, 45, 60, 90, 120, 180, 240, 300, 360];
    final List<String> validProxyOptions = ["否",'是'];

    String validProxyitem = logic.isZDProxy ? "是" : "否";
    textController.text = logic.pt_proxy_url;
    int selectedMinutes = 30;

    TekDialogs.defaultDialog(
      context,
      width: 600,
      content: GetBuilder<BaiduIndexLogic>(
          id: "pz_proxy_dialog",
          builder: (logic) {
            return Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TekTypography(
                    text: "代理配置",
                    type: TekTypographyType.headline,
                  ),
                  SizedBox(height: 16),

                  // 文本输入区域
                  TekInput(
                    labelText: "代理链接",
                    controller: textController,
                    maxLines: 1,
                    hintText: "",
                  ),

                  SizedBox(height: 12),

                  // 代理自动切换选择
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "代理自动切换",
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[700],
                        ),
                      ),
                      SizedBox(height: 4),
                      Container(
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: DropdownButtonHideUnderline(
                          child: DropdownButton<String>(
                            value: validProxyitem,
                            isExpanded: true,
                            padding: EdgeInsets.symmetric(horizontal: 12),
                            items: validProxyOptions.map((String option) {
                              return DropdownMenuItem<String>(
                                value: option,
                                child: Text(option),
                              );
                            }).toList(),
                            onChanged: (newValue) {
                              if (newValue != null) {
                                validProxyitem = newValue;
                                logic.isZDProxy = (validProxyitem == "是");
                                logic.update(['pz_proxy_dialog']);
                              }
                            },
                          ),
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: 12),

                  // 代理有效时间选择
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "代理有效时间",
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[700],
                        ),
                      ),
                      SizedBox(height: 4),
                      Container(
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: DropdownButtonHideUnderline(
                          child: DropdownButton<int>(
                            value: selectedMinutes,
                            isExpanded: true,
                            padding: EdgeInsets.symmetric(horizontal: 12),
                            items: validTimeOptions.map((int minutes) {
                              String displayText = minutes >= 60
                                  ? "${minutes ~/ 60}小时${minutes % 60 > 0 ? ' ${minutes % 60}分钟' : ''}"
                                  : "$minutes分钟";
                              return DropdownMenuItem<int>(
                                value: minutes,
                                child: Text(displayText),
                              );
                            }).toList(),
                            onChanged: (int? newValue) {
                              if (newValue != null) {
                                selectedMinutes = newValue;
                                logic.update(['pz_proxy_dialog']);
                              }
                            },
                          ),
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: 24),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      TekButton(
                        type: TekButtonType.danger,
                        text: "取消",
                        onPressed: () => Navigator.pop(context),
                      ),
                      SizedBox(width: 8),
                      TekButton(
                        type: TekButtonType.info,
                        text: "确定配置",
                        onPressed: () => _handlePzProxyConfig(context, textController, selectedMinutes),
                      )
                    ],
                  ),
                ],
              ),
            );
          }
      ),
    );
  }

  /// 处理代理配置确认
  void _handlePzProxyConfig(BuildContext context, TextEditingController textController, int selectedMinutes) async {
    logic.pt_proxy_url = textController.text;

    if (logic.pt_proxy_url.isEmpty) {
      showToast("没有有效的代理链接");
      return;
    }

    try {
      // 获取所有选中用户
      final selectedUsers = logic.users.where((user) => user.isSelected);

      // 并行处理所有请求
      final List<ProxyConfig> allProxies = await Future.wait(
        selectedUsers.map((user) async {
          final response = await logic.httpClientUtil.get(
            url: logic.pt_proxy_url,
          );

          return response.toString()
              .split('\n')
              .map((line) => line.trim())
              .where((line) => line.isNotEmpty)
              .map(ProxyConfig.fromString)
              .whereType<ProxyConfig>()
              .toList();
        }),
      ).then((results) => results.expand((list) => list).toList());

      if (allProxies.isEmpty) {
        showToast("未获取到有效代理");
        return;
      }
      print("selectedMinutes ---${selectedMinutes}");
      await logic.importProxies(allProxies, selectedMinutes);
      Navigator.pop(context);
    } catch (e) {
      showToast("代理获取失败: ${e.toString()}");
    }
  }
}
