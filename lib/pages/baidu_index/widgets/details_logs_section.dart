import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tekflat_design/tekflat_design.dart';
import 'package:bd/widgets/custom_checkbox.dart';
import '../baidu_index_logic.dart';
import '../log_list_view.dart';
import 'baidu_index_widgets.dart';

/// 详情和日志区域组件
/// 包含提取详情显示、数据处理选项、运行日志区域、操作按钮组等功能
class DetailsLogsSection extends StatelessWidget {
  final BaiduIndexLogic logic;

  const DetailsLogsSection({
    Key? key,
    required this.logic,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<BaiduIndexLogic>(
      id: "three",
      init: logic,
      builder: (logic) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 提取详情显示
            Padding(
              padding: EdgeInsets.symmetric(vertical: 15),
              child: Row(
                children: [
                  Text(
                    "提取详情：关键词：${logic.keyWords.length}个，选地区：${logic.countCheckedNodes(logic.area_data)}\n时间范围：${logic.startDate}~${logic.endDate}",
                    style: TextStyle(color: Colors.black, fontSize: 12),
                  )
                ],
              ),
            ),
            
            // 数据处理选项 - 第一行（日、周、月、年）
            Row(
              children: [
                CustomCheckbox(
                  isChecked: logic.r,
                  onChanged: (value) {
                    logic.r = value;
                    logic.update(['three']);
                  },
                  label: "日",
                ),
                SizedBox(width: 10),
                CustomCheckbox(
                  isChecked: logic.z,
                  onChanged: (value) {
                    logic.z = value;
                    logic.update(['three']);
                  },
                  label: "周",
                ),
                SizedBox(width: 10),
                CustomCheckbox(
                  isChecked: logic.y,
                  onChanged: (value) {
                    logic.y = value;
                    logic.update(['three']);
                  },
                  label: "月",
                ),
                SizedBox(width: 10),
                CustomCheckbox(
                  isChecked: logic.n,
                  onChanged: (value) {
                    logic.n = value;
                    logic.update(['three']);
                  },
                  label: "年",
                ),
              ],
            ),
            
            SizedBox(height: 5),
            
            // 数据处理选项 - 第二行（数据加总、数据平均）
            Row(
              children: [
                CustomCheckbox(
                  isChecked: logic.sjjz,
                  onChanged: (value) {
                    logic.sjjz = value;
                    logic.update(['three']);
                  },
                  label: "数据加总",
                ),
                SizedBox(width: 10),
                CustomCheckbox(
                  isChecked: logic.sjpj,
                  onChanged: (value) {
                    logic.sjpj = value;
                    logic.update(['three']);
                  },
                  label: "数据平均",
                ),
              ],
            ),
            
            // 运行日志区域
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  margin: EdgeInsets.only(top: 12),
                  width: double.infinity,
                  height: 300,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey.shade200),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.1),
                        spreadRadius: 1,
                        blurRadius: 3,
                        offset: Offset(0, 1),
                      ),
                    ],
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 日志标题栏
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade50,
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(8),
                            topRight: Radius.circular(8),
                          ),
                          border: Border(
                            bottom: BorderSide(color: Colors.grey.shade200),
                          ),
                        ),
                        child: Row(
                          children: [
                            Icon(Icons.terminal, size: 18, color: Colors.grey[700]),
                            SizedBox(width: 8),
                            Text(
                              '运行日志',
                              style: TextStyle(
                                fontWeight: FontWeight.w600,
                                color: Colors.grey[800],
                              ),
                            ),
                            Spacer(),
                            // 日志计数
                            Container(
                              padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                              decoration: BoxDecoration(
                                color: Colors.blue.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: GetBuilder<BaiduIndexLogic>(
                                id: 'logs_count',
                                builder: (logic) => Text(
                                  '${logic.logs.length}条',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.blue[700],
                                  ),
                                ),
                              ),
                            ),
                            SizedBox(width: 12),
                            // 清空按钮
                            TekButton(
                              size: TekButtonSize.small,
                              type: TekButtonType.info,
                              icon: Icon(Icons.delete_outline, size: 16),
                              text: "清空",
                              onPressed: () => logic.clearLogs(),
                            ),
                          ],
                        ),
                      ),
                      
                      // 日志内容区域
                      LogListView(),
                    ],
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 12),
            
            // 操作按钮组
            GetBuilder<BaiduIndexLogic>(
              id: 'buttons',
              builder: (logic) {
                return Row(
                  children: [
                    // 检测按钮
                    TekButton(
                      size: TekButtonSize.large,
                      type: TekButtonType.outline,
                      width: 80,
                      height: 40,
                      text: "检测",
                      onPressed: () => logic.onCheckExit(),
                    ),
                    SizedBox(width: 20),
                    
                    // 开始/继续按钮
                    TekButton(
                      size: TekButtonSize.large,
                      type: TaskStateUtils.getButtonType(logic.taskState),
                      width: 80,
                      height: 40,
                      text: TaskStateUtils.getButtonText(logic.taskState),
                      onPressed: logic.taskState == TaskState.running
                          ? null // 运行中禁用按钮
                          : () {
                        if (logic.taskState == TaskState.initial || logic.taskState == TaskState.stopped) {
                          logic.onBdStart();
                        } else {
                          logic.onBdContinue();
                        } 
                      },
                    ),
                    SizedBox(width: 20),
                    
                    // 停止按钮
                    TekButton(
                      width: 80,
                      height: 40,
                      size: TekButtonSize.large,
                      type: TekButtonType.danger,
                      text: "停止",
                      onPressed: () => logic.onBdStop(),
                    ),
                    SizedBox(width: 20),
                    
                    // 保存数据按钮
                    TekButton(
                      width: 80,
                      height: 40,
                      type: TekButtonType.success,
                      text: "保存数据",
                      onPressed: () {
                        if (logic.fileStorageIndex == 0) {
                          logic.handlingData0();
                        } else if (logic.fileStorageIndex == 1) {
                          logic.addLog("× 错误-每个关键词分开存放功能未开放");
                        } else if (logic.fileStorageIndex == 2) {
                          logic.handlingData2();
                        }
                      },
                    ),
                  ],
                );
              },
            ),
          ],
        );
      },
    );
  }
}
