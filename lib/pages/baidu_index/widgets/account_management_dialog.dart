import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../baidu_index_logic.dart';
import 'account_list_widget.dart';
import 'batch_operations_widget.dart';
import 'proxy_dialogs_widget.dart';

/// 账号管理对话框组件 - 重构后的主容器
class AccountManagement extends StatelessWidget {
  AccountManagement({super.key});

  final logic = Get.find<BaiduIndexLogic>();
  final ScrollController _scrollController = ScrollController();
  late final ProxyDialogsWidget _proxyDialogs = ProxyDialogsWidget(logic: logic);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<BaiduIndexLogic>(
        id: "list",
        init: logic,
        builder: (logic) {
          return ConstrainedBox(
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.8,
              maxWidth: 750,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                // 头部区域 - 使用批量操作组件
                BatchOperationsWidget(
                  logic: logic,
                  onProxyConfigPressed: (context) => _proxyDialogs.showPzProxyDialog(context),
                  onBatchProxyPressed: (context) => _proxyDialogs.showBatchProxyDialog(context),
                ),

                // 账号列表 - 使用账号列表组件
                AccountListWidget(
                  logic: logic,
                  scrollController: _scrollController,
                  onProxySettingPressed: (context, user) => _proxyDialogs.showProxyDialog(context, user),
                ),
              ],
            ),
          );
        }
    );
  }
}
