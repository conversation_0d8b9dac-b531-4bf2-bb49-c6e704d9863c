import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:tekflat_design/tekflat_design.dart';
import 'package:bd/widgets/checkbox_with_line.dart';
import '../baidu_index_logic.dart';

/// 关键词输入区域组件
/// 包含关键词输入框、地区选择树形控件、地区操作按钮组等功能
class KeywordInputSection extends StatelessWidget {
  final BaiduIndexLogic logic;
  final ScrollController scrollController;

  const KeywordInputSection({
    Key? key,
    required this.logic,
    required this.scrollController,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<BaiduIndexLogic>(
      id: "now",
      init: logic,
      builder: (logic) {
        return Expanded(
          flex: 1,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 关键词输入标题
              Padding(
                padding: EdgeInsets.symmetric(vertical: 15),
                child: Text(
                  "关键词列表:（一行一个）",
                  style: TextStyle(color: Colors.red, fontSize: 14),
                ),
              ),
              
              // 关键词输入框
              TekInput(
                width: 300,
                hintText: '关键词',
                controller: logic.gjcTextEditingController,
                size: TekInputSize.areaMedium,
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.all(Radius.circular(5.0)),
                  borderSide: BorderSide(
                    color: Colors.grey.shade400,
                    width: 1.0,
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.all(Radius.circular(5.0)),
                  borderSide: BorderSide(
                    color: Colors.blue,
                    width: 2.0,
                  ),
                ),
                onChanged: (v) {
                  if (v!.isEmpty) {
                    logic.keyWords = [];
                  } else {
                    logic.keyWords = v.split("\n");
                    logic.keyWords = logic.keyWords
                        .map((e) => e.trim())
                        .where((e) => e.isNotEmpty)
                        .toList();
                  }
                  logic.update(["three"]);
                },
              ),
              
              // 地区选择标题和清空按钮
              Padding(
                padding: EdgeInsets.symmetric(vertical: 10),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: TekTypography(
                        text: "地区选择",
                        type: TekTypographyType.bodyMedium,
                        color: Colors.blue,
                      ),
                    ),
                    TekButton(
                      size: TekButtonSize.small,
                      type: TekButtonType.info,
                      text: "清空↑",
                      onPressed: () {
                        logic.gjcTextEditingController.text = "";
                        logic.keyWords = [];
                      },
                    ),
                  ],
                ),
              ),
              
              // 地区选择树形控件
              Container(
                height: 400,
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(5.0)),
                  border: Border.all(
                    color: Colors.grey.shade400,
                    width: 1.0,
                  ),
                ),
                child: Scrollbar(
                  controller: scrollController,
                  thumbVisibility: true,
                  child: SingleChildScrollView(
                    controller: scrollController,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                        vertical: 10.0,
                        horizontal: 12,
                      ),
                      child: CheckboxListWithExpand(
                        data: logic.area_data,
                        onCitySelected: (v) {
                          logic.update(["three"]);
                        },
                      ),
                    ),
                  ),
                ),
              ),
              
              // 地区操作按钮组 - 第一行
              Padding(
                padding: EdgeInsets.symmetric(vertical: 5),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: TekButton(
                        size: TekButtonSize.small,
                        type: TekButtonType.info,
                        text: "勾选全部省份",
                        onPressed: () async {
                          for (var area_data_item in logic.area_data[0].children) {
                            if (area_data_item.name != "上海" &&
                                area_data_item.name != "北京" &&
                                area_data_item.name != "天津" &&
                                area_data_item.name != "重庆" &&
                                area_data_item.name != "台湾" &&
                                area_data_item.name != "香港" &&
                                area_data_item.name != "澳门") {
                              area_data_item.isChecked = true;
                            }
                          }
                          logic.update(["now"]);
                          logic.update(["three"]);
                        },
                      ),
                    ),
                    SizedBox(width: 5),
                    Expanded(
                      child: TekButton(
                        size: TekButtonSize.small,
                        type: TekButtonType.info,
                        text: "勾选地级市",
                        onPressed: () {
                          logic.checkPrefectureLevelCity(logic.area_data);
                          logic.update(["now", "three"]);
                        },
                      ),
                    ),
                  ],
                ),
              ),
              
              // 地区操作按钮组 - 第二行
              Padding(
                padding: EdgeInsets.symmetric(vertical: 5),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: TekButton(
                        size: TekButtonSize.small,
                        type: TekButtonType.info,
                        text: "勾选县级市",
                        onPressed: () async {
                          logic.checkCountyLevelCities(logic.area_data);
                          logic.update(["now", "three"]);
                        },
                      ),
                    ),
                    SizedBox(width: 5),
                    Expanded(
                      child: TekButton(
                        size: TekButtonSize.small,
                        type: TekButtonType.info,
                        text: "取消全部勾选",
                        onPressed: () {
                          logic.setAllNodesUncheckedInList(logic.area_data);
                          logic.update(["now", "three"]);
                        },
                      ),
                    ),
                  ],
                ),
              ),
              
              // 手动输入城市列表按钮
              Container(
                padding: EdgeInsets.symmetric(vertical: 5),
                width: double.infinity,
                child: TekButton(
                  size: TekButtonSize.small,
                  type: TekButtonType.info,
                  text: "手动输入城市列表",
                  onPressed: () => _showManualCityInputDialog(context),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// 显示手动输入城市对话框
  Future<void> _showManualCityInputDialog(BuildContext context) async {
    String? result = await showDialog<String>(
      context: context,
      builder: (BuildContext context) {
        String inputText = '';
        return AlertDialog(
          title: Text('输入城市名称'),
          content: Container(
            width: 400,
            height: 300,
            child: TextField(
              maxLines: 15,
              decoration: InputDecoration(
                hintText: '请输入城市名称，多个城市用换行分隔',
              ),
              onChanged: (value) {
                List<String> cleanCities = value
                    .split('\n')
                    .where((s) => s.isNotEmpty)
                    .map((s) => s.trim())
                    .toList();
                String formattedText = cleanCities.join('\n');
                inputText = formattedText;
              },
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: Text('取消'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: Text('确定'),
              onPressed: () {
                Navigator.of(context).pop(inputText);
              },
            ),
          ],
        );
      },
    );

    if (result != null && result.isNotEmpty) {
      List<String> cities = result
          .split('\n')
          .where((city) => city.trim().isNotEmpty)
          .toList();
      logic.checkHandMovement(logic.area_data, cities);
      logic.update(["now", "three"]);
    }
  }
}
