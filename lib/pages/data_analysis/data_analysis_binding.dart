import 'package:get/get.dart';
import 'package:desktop_multi_window/desktop_multi_window.dart';
import 'data_analysis_controller.dart';

/// 数据分析绑定 - GetX依赖注入
class DataAnalysisBinding extends Bindings {
  final WindowController? windowController;

  DataAnalysisBinding({this.windowController});

  @override
  void dependencies() {
    Get.lazyPut(() {
      final controller = DataAnalysisController();
      controller.windowController = windowController;
      return controller;
    });
  }
}
