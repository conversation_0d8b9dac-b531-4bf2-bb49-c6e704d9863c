import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:desktop_multi_window/desktop_multi_window.dart';
import 'data_analysis_page.dart';
import 'data_analysis_binding.dart';

/// 数据分析窗口应用 - 完全使用GetX
class DataAnalysisWindowApp extends StatelessWidget {
  final Map<String, dynamic> args;
  final WindowController? windowController;

  const DataAnalysisWindowApp({
    Key? key,
    required this.args,
    this.windowController,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(  // 使用GetMaterialApp
      title: '数据分析',
      theme: ThemeData(
        fontFamily: "Alibaba",
        primarySwatch: Colors.blue,
      ),
      home: DataAnalysisPage(windowController: windowController),
      initialBinding: DataAnalysisBinding(windowController: windowController), // GetX绑定
      debugShowCheckedModeBanner: false,
    );
  }
}
