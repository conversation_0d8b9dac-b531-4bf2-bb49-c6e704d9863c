import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:desktop_multi_window/desktop_multi_window.dart';
import 'data_analysis_controller.dart';

/// 数据分析页面 - 使用GetX控制器
class DataAnalysisPage extends StatelessWidget {
  final WindowController? windowController;

  const DataAnalysisPage({Key? key, this.windowController}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<DataAnalysisController>( // 使用GetBuilder
      builder: (controller) => Scaffold(
        appBar: AppBar(
          title: const Text('数据分析'),
          actions: [
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: controller.refreshData,
              tooltip: '刷新数据',
            ),
            IconButton(
              icon: const Icon(Icons.download),
              onPressed: controller.exportData,
              tooltip: '导出数据',
            ),
            IconButton(
              icon: const Icon(Icons.close),
              onPressed: controller.closeWindow,
              tooltip: '关闭窗口',
            ),
          ],
        ),
        body: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 控制面板
              _buildControlPanel(controller),
              
              const SizedBox(height: 20),
              
              // 数据展示区域
              Expanded(
                child: controller.isLoading.value
                    ? const Center(child: CircularProgressIndicator())
                    : _buildDataDisplay(controller),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildControlPanel(DataAnalysisController controller) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          children: [
            // 日期范围选择
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('时间范围', style: TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),
                  DropdownButton<String>(
                    value: controller.selectedDateRange.value,
                    isExpanded: true,
                    items: const [
                      DropdownMenuItem(value: '最近7天', child: Text('最近7天')),
                      DropdownMenuItem(value: '最近30天', child: Text('最近30天')),
                      DropdownMenuItem(value: '最近90天', child: Text('最近90天')),
                      DropdownMenuItem(value: '自定义', child: Text('自定义')),
                    ],
                    onChanged: (value) {
                      if (value != null) {
                        controller.changeDateRange(value);
                      }
                    },
                  ),
                ],
              ),
            ),
            
            const SizedBox(width: 20),
            
            // 图表类型选择
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('图表类型', style: TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),
                  DropdownButton<String>(
                    value: controller.chartType.value,
                    isExpanded: true,
                    items: const [
                      DropdownMenuItem(value: '柱状图', child: Text('柱状图')),
                      DropdownMenuItem(value: '折线图', child: Text('折线图')),
                      DropdownMenuItem(value: '饼图', child: Text('饼图')),
                      DropdownMenuItem(value: '散点图', child: Text('散点图')),
                    ],
                    onChanged: (value) {
                      if (value != null) {
                        controller.changeChartType(value);
                      }
                    },
                  ),
                ],
              ),
            ),
            
            const SizedBox(width: 20),
            
            // 数据源选择
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('数据源', style: TextStyle(fontWeight: FontWeight.bold)),
                  const SizedBox(height: 8),
                  DropdownButton<String>(
                    value: controller.dataSource.value,
                    isExpanded: true,
                    items: const [
                      DropdownMenuItem(value: '百度指数', child: Text('百度指数')),
                      DropdownMenuItem(value: '微信指数', child: Text('微信指数')),
                      DropdownMenuItem(value: '微博指数', child: Text('微博指数')),
                    ],
                    onChanged: (value) {
                      if (value != null) {
                        controller.changeDataSource(value);
                      }
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDataDisplay(DataAnalysisController controller) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '数据分析结果 (${controller.chartType.value})',
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            // 数据统计
            Row(
              children: [
                _buildStatCard('总记录数', '${controller.analysisData.length}', Colors.blue),
                const SizedBox(width: 16),
                _buildStatCard('平均值', '${_calculateAverage(controller.analysisData)}', Colors.green),
                const SizedBox(width: 16),
                _buildStatCard('最大值', '${_calculateMax(controller.analysisData)}', Colors.orange),
              ],
            ),
            
            const SizedBox(height: 20),
            
            // 数据表格
            Expanded(
              child: SingleChildScrollView(
                child: DataTable(
                  columns: const [
                    DataColumn(label: Text('日期')),
                    DataColumn(label: Text('关键词')),
                    DataColumn(label: Text('数值')),
                  ],
                  rows: controller.analysisData.map((data) {
                    return DataRow(
                      cells: [
                        DataCell(Text(data['date'])),
                        DataCell(Text(data['keyword'])),
                        DataCell(Text('${data['value']}')),
                      ],
                    );
                  }).toList(),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, Color color) {
    return Expanded(
      child: Card(
        color: color.withOpacity(0.1),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            children: [
              Text(title, style: TextStyle(color: color, fontWeight: FontWeight.bold)),
              const SizedBox(height: 4),
              Text(value, style: TextStyle(fontSize: 18, color: color)),
            ],
          ),
        ),
      ),
    );
  }

  int _calculateAverage(List<Map<String, dynamic>> data) {
    if (data.isEmpty) return 0;
    int sum = data.fold(0, (sum, item) => sum + (item['value'] as int));
    return sum ~/ data.length;
  }

  int _calculateMax(List<Map<String, dynamic>> data) {
    if (data.isEmpty) return 0;
    return data.fold(0, (max, item) => 
        (item['value'] as int) > max ? item['value'] : max);
  }
}
