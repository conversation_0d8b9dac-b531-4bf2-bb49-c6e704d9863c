import 'package:get/get.dart';
import 'package:desktop_multi_window/desktop_multi_window.dart';
import 'package:flutter/services.dart';

/// 数据分析控制器 - 标准的GetX控制器
class DataAnalysisController extends GetxController {
  // 数据分析状态
  final isLoading = false.obs;
  final selectedDateRange = '最近7天'.obs;
  final chartType = '柱状图'.obs;
  final dataSource = '百度指数'.obs;
  
  // 模拟数据
  final analysisData = <Map<String, dynamic>>[].obs;
  
  // 窗口相关
  final isWindowClosing = false.obs;
  WindowController? windowController;

  @override
  void onInit() {
    super.onInit();
    _loadAnalysisData();
  }

  /// 加载分析数据
  void _loadAnalysisData() {
    isLoading.value = true;
    
    // 模拟数据加载
    Future.delayed(Duration(seconds: 2), () {
      analysisData.assignAll([
        {'date': '2024-01-01', 'value': 1200, 'keyword': '关键词1'},
        {'date': '2024-01-02', 'value': 1350, 'keyword': '关键词1'},
        {'date': '2024-01-03', 'value': 1100, 'keyword': '关键词1'},
        {'date': '2024-01-04', 'value': 1450, 'keyword': '关键词1'},
        {'date': '2024-01-05', 'value': 1600, 'keyword': '关键词1'},
      ]);
      isLoading.value = false;
    });
  }

  /// 切换日期范围
  void changeDateRange(String range) {
    selectedDateRange.value = range;
    _loadAnalysisData(); // 重新加载数据
    update();
  }

  /// 切换图表类型
  void changeChartType(String type) {
    chartType.value = type;
    update();
  }

  /// 切换数据源
  void changeDataSource(String source) {
    dataSource.value = source;
    _loadAnalysisData(); // 重新加载数据
    update();
  }

  /// 导出数据
  void exportData() {
    // 实现数据导出逻辑
    Get.snackbar(
      '导出成功',
      '数据已导出到本地文件',
      snackPosition: SnackPosition.TOP,
    );
    
    // 通知主窗口
    _notifyMainWindow('dataExported', {
      'fileName': 'analysis_${DateTime.now().millisecondsSinceEpoch}.csv',
      'recordCount': analysisData.length,
    });
  }

  /// 刷新数据
  void refreshData() {
    _loadAnalysisData();
    Get.snackbar(
      '刷新完成',
      '数据已更新',
      snackPosition: SnackPosition.TOP,
    );
  }

  /// 通知主窗口
  void _notifyMainWindow(String action, Map<String, dynamic> data) {
    DesktopMultiWindow.invokeMethod(
      0, // 主窗口ID
      'dataAnalysisAction',
      {
        'action': action,
        'data': data,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );
  }

  /// 关闭窗口（按照官方示例）
  void closeWindow() {
    if (isWindowClosing.value) return;

    isWindowClosing.value = true;

    // 直接关闭窗口，无需复杂的通知机制
    if (windowController != null) {
      windowController!.close();
    } else {
      SystemNavigator.pop();
    }
  }
}
