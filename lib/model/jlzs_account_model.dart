/// 巨量指数账号数据模型
class JlzsAccountModel {
  String id;                       // 账号ID
  String username;                 // 用户名
  String password;                 // 密码 (加密存储)
  String platform;                 // 平台类型 (aweme/toutiao)
  bool isActive;                   // 是否激活
  DateTime lastUsed;               // 最后使用时间
  DateTime createdAt;              // 创建时间
  String? remark;                  // 备注

  JlzsAccountModel({
    required this.id,
    required this.username,
    required this.password,
    required this.platform,
    this.isActive = true,
    DateTime? lastUsed,
    DateTime? createdAt,
    this.remark,
  }) : lastUsed = lastUsed ?? DateTime.now(),
       createdAt = createdAt ?? DateTime.now();

  /// 从JSON创建实例
  factory JlzsAccountModel.fromJson(Map<String, dynamic> json) {
    return JlzsAccountModel(
      id: json['id'] ?? '',
      username: json['username'] ?? '',
      password: json['password'] ?? '',
      platform: json['platform'] ?? 'aweme',
      isActive: json['isActive'] ?? true,
      lastUsed: DateTime.parse(json['lastUsed'] ?? DateTime.now().toIso8601String()),
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      remark: json['remark'],
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'password': password,
      'platform': platform,
      'isActive': isActive,
      'lastUsed': lastUsed.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'remark': remark,
    };
  }

  /// 复制实例
  JlzsAccountModel copyWith({
    String? id,
    String? username,
    String? password,
    String? platform,
    bool? isActive,
    DateTime? lastUsed,
    DateTime? createdAt,
    String? remark,
  }) {
    return JlzsAccountModel(
      id: id ?? this.id,
      username: username ?? this.username,
      password: password ?? this.password,
      platform: platform ?? this.platform,
      isActive: isActive ?? this.isActive,
      lastUsed: lastUsed ?? this.lastUsed,
      createdAt: createdAt ?? this.createdAt,
      remark: remark ?? this.remark,
    );
  }

  /// 获取平台显示名称
  String get platformDisplayName {
    switch (platform) {
      case 'aweme':
        return '抖音';
      case 'toutiao':
        return '头条';
      default:
        return platform;
    }
  }

  /// 获取状态显示名称
  String get statusDisplayName {
    return isActive ? '正常' : '禁用';
  }

  /// 获取最后使用时间的友好显示
  String get lastUsedFriendly {
    final now = DateTime.now();
    final difference = now.difference(lastUsed);
    
    if (difference.inDays > 0) {
      return '${difference.inDays}天前';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }

  /// 更新最后使用时间
  void updateLastUsed() {
    lastUsed = DateTime.now();
  }

  /// 验证账号信息是否有效
  bool isValid() {
    return id.isNotEmpty &&
           username.isNotEmpty &&
           password.isNotEmpty &&
           platform.isNotEmpty;
  }

  @override
  String toString() {
    return 'JlzsAccountModel{id: $id, username: $username, platform: $platform, '
           'isActive: $isActive, lastUsed: $lastUsed, createdAt: $createdAt, '
           'remark: $remark}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is JlzsAccountModel &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          username == other.username &&
          password == other.password &&
          platform == other.platform &&
          isActive == other.isActive &&
          lastUsed == other.lastUsed &&
          createdAt == other.createdAt &&
          remark == other.remark;

  @override
  int get hashCode =>
      id.hashCode ^
      username.hashCode ^
      password.hashCode ^
      platform.hashCode ^
      isActive.hashCode ^
      lastUsed.hashCode ^
      createdAt.hashCode ^
      remark.hashCode;
}

/// 账号管理器
class JlzsAccountManager {
  static final List<JlzsAccountModel> _accounts = [];

  /// 获取所有账号
  static List<JlzsAccountModel> get accounts => List.unmodifiable(_accounts);

  /// 添加账号
  static void addAccount(JlzsAccountModel account) {
    if (account.isValid()) {
      _accounts.add(account);
    }
  }

  /// 删除账号
  static bool removeAccount(String accountId) {
    final index = _accounts.indexWhere((account) => account.id == accountId);
    if (index != -1) {
      _accounts.removeAt(index);
      return true;
    }
    return false;
  }

  /// 更新账号
  static bool updateAccount(JlzsAccountModel updatedAccount) {
    final index = _accounts.indexWhere((account) => account.id == updatedAccount.id);
    if (index != -1 && updatedAccount.isValid()) {
      _accounts[index] = updatedAccount;
      return true;
    }
    return false;
  }

  /// 根据ID获取账号
  static JlzsAccountModel? getAccountById(String accountId) {
    try {
      return _accounts.firstWhere((account) => account.id == accountId);
    } catch (e) {
      return null;
    }
  }

  /// 根据平台获取账号列表
  static List<JlzsAccountModel> getAccountsByPlatform(String platform) {
    return _accounts.where((account) => account.platform == platform).toList();
  }

  /// 获取激活的账号列表
  static List<JlzsAccountModel> getActiveAccounts() {
    return _accounts.where((account) => account.isActive).toList();
  }

  /// 清空所有账号
  static void clearAllAccounts() {
    _accounts.clear();
  }

  /// 生成新的账号ID
  static String generateAccountId() {
    return 'jlzs_${DateTime.now().millisecondsSinceEpoch}';
  }
}
