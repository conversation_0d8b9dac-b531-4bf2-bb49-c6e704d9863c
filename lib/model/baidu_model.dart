// 代理配置模型
class ProxyConfig {
  final String ip;
  final String port;
  final String? username;
  final String? password;

  ProxyConfig({
    required this.ip,
    required this.port,
    this.username,
    this.password,
  });

  // 从字符串解析代理配置
  static ProxyConfig? fromString(String text) {
    final parts = text.trim().split(':');
    if (parts.length >= 2) {
      return ProxyConfig(
        ip: parts[0],
        port: parts[1],
        username: parts.length > 2 ? parts[2] : null,
        password: parts.length > 3 ? parts[3] : null,
      );
    }
    return null;
  }
}

class TreeNode {
  final String name;
  final int id;
  bool isChecked;
  List<TreeNode> children;

  TreeNode(this.name, this.id, this.children, {this.isChecked = false});
}
