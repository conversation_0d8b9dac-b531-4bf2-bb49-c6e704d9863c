/// 巨量指数API请求任务数据模型
class JlzsApiTaskData {
  String id;                    // 任务ID
  List<String> keywords;        // 关键词列表（最多5个）
  List<String> regions;         // 地区列表
  String startDate;             // 开始日期 (YYYYMMDD)
  String endDate;               // 结束日期 (YYYYMMDD)
  String platform;              // 平台 (aweme/toutiao)
  String regionType;            // 地区类型 (separate/merged)
  JlzsApiTaskStatus status;     // 任务状态
  String? accountId;            // 执行账号ID
  DateTime createdAt;           // 创建时间
  DateTime? startedAt;          // 开始执行时间
  DateTime? completedAt;        // 完成时间
  String? errorMessage;         // 错误信息
  Map<String, dynamic>? responseData; // 响应数据

  JlzsApiTaskData({
    required this.id,
    required this.keywords,
    required this.regions,
    required this.startDate,
    required this.endDate,
    required this.platform,
    required this.regionType,
    this.status = JlzsApiTaskStatus.pending,
    this.accountId,
    DateTime? createdAt,
    this.startedAt,
    this.completedAt,
    this.errorMessage,
    this.responseData,
  }) : createdAt = createdAt ?? DateTime.now();

  /// 从JSON创建实例
  factory JlzsApiTaskData.fromJson(Map<String, dynamic> json) {
    return JlzsApiTaskData(
      id: json['id'] ?? '',
      keywords: List<String>.from(json['keywords'] ?? []),
      regions: List<String>.from(json['regions'] ?? []),
      startDate: json['startDate'] ?? '',
      endDate: json['endDate'] ?? '',
      platform: json['platform'] ?? 'aweme',
      regionType: json['regionType'] ?? 'separate',
      status: JlzsApiTaskStatus.values.byName(json['status'] ?? 'pending'),
      accountId: json['accountId'],
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      startedAt: json['startedAt'] != null ? DateTime.parse(json['startedAt']) : null,
      completedAt: json['completedAt'] != null ? DateTime.parse(json['completedAt']) : null,
      errorMessage: json['errorMessage'],
      responseData: json['responseData'],
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'keywords': keywords,
      'regions': regions,
      'startDate': startDate,
      'endDate': endDate,
      'platform': platform,
      'regionType': regionType,
      'status': status.name,
      'accountId': accountId,
      'createdAt': createdAt.toIso8601String(),
      'startedAt': startedAt?.toIso8601String(),
      'completedAt': completedAt?.toIso8601String(),
      'errorMessage': errorMessage,
      'responseData': responseData,
    };
  }

  /// 复制实例
  JlzsApiTaskData copyWith({
    String? id,
    List<String>? keywords,
    List<String>? regions,
    String? startDate,
    String? endDate,
    String? platform,
    String? regionType,
    JlzsApiTaskStatus? status,
    String? accountId,
    DateTime? createdAt,
    DateTime? startedAt,
    DateTime? completedAt,
    String? errorMessage,
    Map<String, dynamic>? responseData,
  }) {
    return JlzsApiTaskData(
      id: id ?? this.id,
      keywords: keywords ?? this.keywords,
      regions: regions ?? this.regions,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      platform: platform ?? this.platform,
      regionType: regionType ?? this.regionType,
      status: status ?? this.status,
      accountId: accountId ?? this.accountId,
      createdAt: createdAt ?? this.createdAt,
      startedAt: startedAt ?? this.startedAt,
      completedAt: completedAt ?? this.completedAt,
      errorMessage: errorMessage ?? this.errorMessage,
      responseData: responseData ?? this.responseData,
    );
  }

  /// 构建API请求体
  Map<String, dynamic> buildRequestBody() {
    // 处理地区参数：如果包含"全国"，则region为空数组
    List<String> regionParam = [];
    if (!regions.contains('全国')) {
      regionParam = regions;
    }

    return {
      "keyword_list": keywords,
      "start_date": startDate,
      "end_date": endDate,
      "app_name": platform,
      "region": regionParam,
    };
  }

  /// 设置为执行中状态
  void setRunning(String accountId) {
    this.accountId = accountId;
    status = JlzsApiTaskStatus.running;
    startedAt = DateTime.now();
  }

  /// 设置为完成状态
  void setCompleted(Map<String, dynamic> responseData) {
    this.responseData = responseData;
    status = JlzsApiTaskStatus.completed;
    completedAt = DateTime.now();
  }

  /// 设置为失败状态
  void setFailed(String errorMessage) {
    this.errorMessage = errorMessage;
    status = JlzsApiTaskStatus.failed;
    completedAt = DateTime.now();
  }

  /// 获取任务描述
  String get description {
    return '关键词${keywords.length}个, 地区${regions.length}个, ${platform == 'aweme' ? '抖音' : '头条'}';
  }

  /// 获取执行时长
  Duration? get executionDuration {
    if (startedAt == null) return null;
    final endTime = completedAt ?? DateTime.now();
    return endTime.difference(startedAt!);
  }
}

/// API任务状态枚举
enum JlzsApiTaskStatus {
  pending,    // 等待执行
  running,    // 执行中
  completed,  // 完成
  failed,     // 失败
  cancelled,  // 取消
}

/// 任务生成配置
class JlzsTaskGenerationConfig {
  List<String> keywords;        // 关键词列表
  List<String> regions;         // 地区列表
  String startDate;             // 开始日期 (YYYYMMDD)
  String endDate;               // 结束日期 (YYYYMMDD)
  String platform;              // 平台 (aweme/toutiao)
  String regionType;            // 地区类型 (separate/merged)
  int keywordsPerTask;          // 每个任务的关键词数量（默认5）

  JlzsTaskGenerationConfig({
    required this.keywords,
    required this.regions,
    required this.startDate,
    required this.endDate,
    required this.platform,
    required this.regionType,
    this.keywordsPerTask = 5,
  });

  /// 从配置模型创建
  factory JlzsTaskGenerationConfig.fromConfig(
    List<String> keywords,
    List<String> regions,
    String startDate,
    String endDate,
    String platform,
    String regionType,
  ) {
    return JlzsTaskGenerationConfig(
      keywords: keywords,
      regions: regions,
      startDate: startDate,
      endDate: endDate,
      platform: platform,
      regionType: regionType,
    );
  }

  /// 获取关键词分组
  List<List<String>> getKeywordGroups() {
    List<List<String>> groups = [];
    for (int i = 0; i < keywords.length; i += keywordsPerTask) {
      int end = (i + keywordsPerTask < keywords.length) 
          ? i + keywordsPerTask 
          : keywords.length;
      groups.add(keywords.sublist(i, end));
    }
    return groups;
  }

  /// 获取优化后的地区列表（仅合并查询模式）
  List<String> getOptimizedRegions() {
    if (regionType != 'merged') {
      return regions;
    }

    // TODO: 实现地区优化逻辑
    // 例如：如果选择了某省的所有城市，则只保留省份名称
    return regions;
  }
}
