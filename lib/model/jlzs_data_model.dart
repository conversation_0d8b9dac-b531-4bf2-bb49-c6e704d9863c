/// 巨量指数关键词数据模型
class JlzsKeywordData {
  String keyword;          // 关键词
  String hotIndex;         // 综合指数
  String searchIndex;      // 搜索指数

  JlzsKeywordData({
    required this.keyword, 
    required this.hotIndex, 
    required this.searchIndex
  });

  /// 从JSON创建实例
  factory JlzsKeywordData.fromJson(Map<String, dynamic> json) {
    return JlzsKeywordData(
      keyword: json['keyword'] ?? '',
      hotIndex: json['hotIndex'] ?? '0',
      searchIndex: json['searchIndex'] ?? '0',
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'keyword': keyword,
      'hotIndex': hotIndex,
      'searchIndex': searchIndex,
    };
  }

  /// 复制实例
  JlzsKeywordData copyWith({
    String? keyword,
    String? hotIndex,
    String? searchIndex,
  }) {
    return JlzsKeywordData(
      keyword: keyword ?? this.keyword,
      hotIndex: hotIndex ?? this.hotIndex,
      searchIndex: searchIndex ?? this.searchIndex,
    );
  }
}

/// 巨量指数分开查询模式数据模型
class JlzsDataModel {
  String? region;           // 地区名称
  String platform;         // 平台 (aweme/toutiao)
  bool isCompleted;        // 是否完成采集
  Map<String, List<JlzsKeywordData>>? data; // 以日期为键，关键词数据为值

  JlzsDataModel({
    this.region, 
    required this.platform, 
    this.isCompleted = false, 
    this.data
  });

  /// 从JSON创建实例
  factory JlzsDataModel.fromJson(Map<String, dynamic> json) {
    Map<String, List<JlzsKeywordData>>? dataMap;
    if (json['data'] != null) {
      dataMap = {};
      (json['data'] as Map<String, dynamic>).forEach((date, keywordList) {
        dataMap![date] = (keywordList as List)
            .map((item) => JlzsKeywordData.fromJson(item))
            .toList();
      });
    }

    return JlzsDataModel(
      region: json['region'],
      platform: json['platform'] ?? 'aweme',
      isCompleted: json['isCompleted'] ?? false,
      data: dataMap,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    Map<String, dynamic>? dataMap;
    if (data != null) {
      dataMap = {};
      data!.forEach((date, keywordList) {
        dataMap![date] = keywordList.map((item) => item.toJson()).toList();
      });
    }

    return {
      'region': region,
      'platform': platform,
      'isCompleted': isCompleted,
      'data': dataMap,
    };
  }

  /// 复制实例
  JlzsDataModel copyWith({
    String? region,
    String? platform,
    bool? isCompleted,
    Map<String, List<JlzsKeywordData>>? data,
  }) {
    return JlzsDataModel(
      region: region ?? this.region,
      platform: platform ?? this.platform,
      isCompleted: isCompleted ?? this.isCompleted,
      data: data ?? this.data,
    );
  }

  /// 添加关键词数据
  void addKeywordData(String date, JlzsKeywordData keywordData) {
    data ??= {};
    data![date] ??= [];
    data![date]!.add(keywordData);
  }

  /// 获取指定日期的关键词数据
  List<JlzsKeywordData>? getKeywordDataByDate(String date) {
    return data?[date];
  }

  /// 获取所有日期
  List<String> getAllDates() {
    return data?.keys.toList() ?? [];
  }

  /// 获取总关键词数量
  int getTotalKeywordCount() {
    if (data == null) return 0;
    int total = 0;
    data!.values.forEach((keywordList) {
      total += keywordList.length;
    });
    return total;
  }
}

/// 巨量指数合并查询模式数据模型
class JlzsMergedData {
  String keyword;          // 关键词
  String date;            // 日期 (YYYYMMDD)
  String platform;        // 平台 (aweme/toutiao)
  List<String> regions;   // 地区列表
  String hotIndex;        // 综合指数
  String searchIndex;     // 搜索指数

  JlzsMergedData({
    required this.keyword,
    required this.date,
    required this.platform,
    required this.regions,
    required this.hotIndex,
    required this.searchIndex
  });

  /// 从JSON创建实例
  factory JlzsMergedData.fromJson(Map<String, dynamic> json) {
    return JlzsMergedData(
      keyword: json['keyword'] ?? '',
      date: json['date'] ?? '',
      platform: json['platform'] ?? 'aweme',
      regions: List<String>.from(json['regions'] ?? []),
      hotIndex: json['hotIndex'] ?? '0',
      searchIndex: json['searchIndex'] ?? '0',
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'keyword': keyword,
      'date': date,
      'platform': platform,
      'regions': regions,
      'hotIndex': hotIndex,
      'searchIndex': searchIndex,
    };
  }

  /// 复制实例
  JlzsMergedData copyWith({
    String? keyword,
    String? date,
    String? platform,
    List<String>? regions,
    String? hotIndex,
    String? searchIndex,
  }) {
    return JlzsMergedData(
      keyword: keyword ?? this.keyword,
      date: date ?? this.date,
      platform: platform ?? this.platform,
      regions: regions ?? this.regions,
      hotIndex: hotIndex ?? this.hotIndex,
      searchIndex: searchIndex ?? this.searchIndex,
    );
  }
}
