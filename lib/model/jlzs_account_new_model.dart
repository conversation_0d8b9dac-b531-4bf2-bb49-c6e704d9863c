
import 'dart:io';

/// 巨量指数账号状态枚举
enum AccountStatus {
  inactive,    // 未激活
  active,      // 活跃
  offline,     // 离线
  error,       // 错误状态
  expired,     // 会话过期
}

/// 代理类型枚举
enum ProxyType {
  http,
  socks5,
}

/// 巨量指数代理配置模型
class JlzsProxyConfig {
  final String address;
  final int port;
  final String? username;
  final String? password;
  final ProxyType type;
  final int? validTime;        // 有效时间（分钟）
  final DateTime? startTime;   // 开始时间

  JlzsProxyConfig({
    required this.address,
    required this.port,
    this.username,
    this.password,
    this.type = ProxyType.http,
    this.validTime,
    this.startTime,
  });

  /// 转换为Puppeteer启动参数（不包含认证信息）
  List<String> toArgs() {
    List<String> args = [];
    String proxyUrl = type == ProxyType.http ? 'http' : 'socks5';
    args.add('--proxy-server=$proxyUrl://$address:$port');

    // 注意：认证信息通过page.authenticate()处理，不在启动参数中
    return args;
  }

  /// 获取代理显示信息
  String get displayInfo {
    String typeStr = type == ProxyType.http ? 'HTTP' : 'SOCKS5';
    String authStr = (username != null && username!.isNotEmpty) ? '(认证)' : '';
    String timeStr = validTime != null ? ' ${validTime}分钟' : '';
    return '$typeStr://$address:$port$authStr$timeStr';
  }

  /// 检查代理是否过期
  bool get isExpired {
    if (validTime == null || startTime == null) return false;
    DateTime expiry = startTime!.add(Duration(minutes: validTime!));
    return DateTime.now().isAfter(expiry);
  }

  /// 获取剩余有效时间（分钟）
  int get remainingMinutes {
    if (validTime == null || startTime == null) return -1;
    DateTime expiry = startTime!.add(Duration(minutes: validTime!));
    int remaining = expiry.difference(DateTime.now()).inMinutes;
    return remaining > 0 ? remaining : 0;
  }

  /// 验证代理配置（参考百度指数实现）
  Future<bool> validate() async {
    try {
      print('🔍 开始验证代理: $displayInfo');

      // 基础格式验证
      if (address.isEmpty || port <= 0 || port > 65535) {
        print('❌ 代理配置格式错误');
        return false;
      }

      // 检查是否过期
      if (isExpired) {
        print('⚠️ 代理已过期');
        return false;
      }

      // 使用HttpClient进行代理连接测试（参考百度指数的checkProxy方法）
      final HttpClient client = HttpClient();
      print('PROXY $address:$port');

      // 设置代理（修复认证逻辑，参考百度指数实现）
      client.findProxy = (uri) {
        if (username != null && username!.isNotEmpty) {
          print('🔐 使用认证代理: $username:***@$address:$port');
          return 'PROXY $username:$password@$address:$port';
        }
        return 'PROXY $address:$port';
      };

      // 设置超时
      client.connectionTimeout = Duration(seconds: 10);

      try {
        // 测试连接（使用百度作为测试网站，参考百度指数实现）
        print('🌐 测试连接: https://www.baidu.com');
        final request = await client.getUrl(Uri.parse('https://www.baidu.com'));
        final response = await request.close();

        // 关闭客户端
        client.close();

        // 检查响应状态
        if (response.statusCode == 200) {
          print('✅ 代理验证成功');
          return true;
        } else {
          print('❌ 代理验证失败: HTTP ${response.statusCode}');
          return false;
        }
      } catch (e) {
        print('❌ 代理验证失败: $e');
        client.close();
        return false;
      }

    } catch (e) {
      print('❌ 代理验证异常: $e');
      return false;
    }
  }



  Map<String, dynamic> toJson() {
    return {
      'address': address,
      'port': port,
      'username': username,
      'password': password,
      'type': type.name,
    };
  }

  factory JlzsProxyConfig.fromJson(Map<String, dynamic> json) {
    return JlzsProxyConfig(
      address: json['address'],
      port: json['port'],
      username: json['username'],
      password: json['password'],
      type: ProxyType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => ProxyType.http,
      ),
    );
  }
}

/// 关联的抖音/头条账号信息
class ConnectedAccount {
  final String platform;           // aweme_v2, toutiao
  final String platformUid;        // 平台用户ID
  final String secPlatformUid;     // 加密的平台用户ID
  final String screenName;         // 显示名称
  final String profileImageUrl;    // 头像URL
  final DateTime expiredTime;      // 过期时间
  final int platformAppId;         // 平台应用ID

  ConnectedAccount({
    required this.platform,
    required this.platformUid,
    required this.secPlatformUid,
    required this.screenName,
    required this.profileImageUrl,
    required this.expiredTime,
    required this.platformAppId,
  });

  /// 获取平台显示名称
  String get platformDisplayName {
    switch (platform) {
      case 'aweme_v2':
        return '抖音';
      case 'toutiao':
        return '头条';
      default:
        return platform;
    }
  }

  /// 检查是否已过期
  bool get isExpired => DateTime.now().isAfter(expiredTime);

  Map<String, dynamic> toJson() {
    return {
      'platform': platform,
      'platform_uid': platformUid,
      'sec_platform_uid': secPlatformUid,
      'screen_name': screenName,
      'profile_image_url': profileImageUrl,
      'expired_time': expiredTime.millisecondsSinceEpoch,
      'platform_app_id': platformAppId,
    };
  }

  factory ConnectedAccount.fromJson(Map<String, dynamic> json) {
    return ConnectedAccount(
      platform: json['platform'] ?? '',
      platformUid: json['platform_uid'] ?? '',
      secPlatformUid: json['sec_platform_uid'] ?? '',
      screenName: json['platform_screen_name'] ?? json['screen_name'] ?? '',
      profileImageUrl: json['profile_image_url'] ?? '',
      expiredTime: DateTime.fromMillisecondsSinceEpoch(
        json['expired_time'] is int
          ? (json['expired_time'] < *************
              ? json['expired_time'] * 1000  // 秒级时间戳转毫秒
              : json['expired_time'])        // 已经是毫秒级
          : DateTime.now().millisecondsSinceEpoch
      ),
      platformAppId: json['platform_app_id'] ?? 0,
    );
  }
}

/// 巨量指数会话信息
class JlzsSession {
  final String accountId;
  Map<String, String> cookies = {};
  Map<String, dynamic> userInfo = {};
  List<ConnectedAccount> connectedAccounts = [];
  DateTime lastActive = DateTime.now();
  bool isActive = false;
  int? debugPort;

  JlzsSession(this.accountId);

  /// 从账号信息API更新会话数据
  void updateFromAccountInfo(Map<String, dynamic> accountData) {
    userInfo = {
      'user_id': accountData['user_id']?.toString() ?? '',
      'sec_user_id': accountData['sec_user_id'] ?? '',
      'mobile': accountData['mobile'] ?? '',
      'name': accountData['name'] ?? '',
      'screen_name': accountData['screen_name'] ?? '',
      'avatar_url': accountData['avatar_url'] ?? '',
      'app_id': accountData['app_id']?.toString() ?? '',
    };

    // 更新关联账号信息
    connectedAccounts.clear();
    if (accountData['connects'] != null) {
      for (var connect in accountData['connects']) {
        try {
          connectedAccounts.add(ConnectedAccount.fromJson(connect));
        } catch (e) {
          print('解析关联账号失败: $e');
        }
      }
    }

    isActive = true;
    lastActive = DateTime.now();
  }

  /// 获取用户显示名称
  String get displayName {
    return userInfo['name'] ?? userInfo['screen_name'] ?? '未知用户';
  }

  /// 获取脱敏手机号
  String get maskedMobile {
    String mobile = userInfo['mobile'] ?? '';
    if (mobile.length >= 11) {
      return '${mobile.substring(0, 3)}****${mobile.substring(7)}';
    }
    return mobile;
  }

  /// 检查会话是否有效（1小时内活跃）
  bool get isValid {
    return isActive && DateTime.now().difference(lastActive).inHours < 1;
  }

  Map<String, dynamic> toJson() {
    return {
      'account_id': accountId,
      'cookies': cookies,
      'user_info': userInfo,
      'connected_accounts': connectedAccounts.map((e) => e.toJson()).toList(),
      'last_active': lastActive.millisecondsSinceEpoch,
      'is_active': isActive,
      'debug_port': debugPort,
    };
  }

  factory JlzsSession.fromJson(Map<String, dynamic> json) {
    var session = JlzsSession(json['account_id']);
    session.cookies = Map<String, String>.from(json['cookies'] ?? {});
    session.userInfo = Map<String, dynamic>.from(json['user_info'] ?? {});
    session.lastActive = DateTime.fromMillisecondsSinceEpoch(
      json['last_active'] ?? DateTime.now().millisecondsSinceEpoch
    );
    session.isActive = json['is_active'] ?? false;
    session.debugPort = json['debug_port'];

    // 恢复关联账号
    if (json['connected_accounts'] != null) {
      for (var connectJson in json['connected_accounts']) {
        try {
          session.connectedAccounts.add(ConnectedAccount.fromJson(connectJson));
        } catch (e) {
          print('恢复关联账号失败: $e');
        }
      }
    }

    return session;
  }
}

/// 巨量指数账号数据模型 (新版本 - 手机号登录)
class JlzsAccountModel {
  String id;                       // 账号ID
  String mobile;                   // 手机号
  String? nickname;                // 昵称
  JlzsProxyConfig? proxy;          // 代理配置
  JlzsSession? session;            // 会话信息
  DateTime lastUsed;               // 最后使用时间
  DateTime createdAt;              // 创建时间
  AccountStatus status;            // 账号状态
  int? debugPort;                  // 浏览器调试端口（用于重连）
  String? remark;                  // 备注

  JlzsAccountModel({
    required this.id,
    required this.mobile,
    this.nickname,
    this.proxy,
    this.session,
    DateTime? lastUsed,
    DateTime? createdAt,
    this.status = AccountStatus.inactive,
    this.debugPort,
    this.remark,
  }) : lastUsed = lastUsed ?? DateTime.now(),
       createdAt = createdAt ?? DateTime.now();

  /// 获取显示名称
  String get displayName {
    if (session?.displayName.isNotEmpty == true) {
      return session!.displayName;
    }
    return nickname ?? maskedMobile;
  }

  /// 获取脱敏手机号
  String get maskedMobile {
    if (mobile.length >= 11) {
      return '${mobile.substring(0, 3)}****${mobile.substring(7)}';
    }
    return mobile;
  }

  /// 获取头像URL
  String? get avatarUrl => session?.userInfo['avatar_url'];

  /// 获取关联账号列表
  List<ConnectedAccount> get connectedAccounts => session?.connectedAccounts ?? [];

  /// 检查账号是否在线
  bool get isOnline => status == AccountStatus.active && (session?.isValid ?? false);

  /// 获取状态显示文本
  String get statusText {
    switch (status) {
      case AccountStatus.active:
        return '在线';
      case AccountStatus.offline:
        return '离线';
      case AccountStatus.error:
        return '错误';
      case AccountStatus.expired:
        return '已过期';
      case AccountStatus.inactive:
        return '未激活';
    }
  }

  /// 从JSON创建实例
  factory JlzsAccountModel.fromJson(Map<String, dynamic> json) {
    var account = JlzsAccountModel(
      id: json['id'] ?? '',
      mobile: json['mobile'] ?? '',
      nickname: json['nickname'],
      lastUsed: DateTime.parse(json['lastUsed'] ?? DateTime.now().toIso8601String()),
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      status: AccountStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => AccountStatus.inactive,
      ),
      debugPort: json['debug_port'],
      remark: json['remark'],
    );

    if (json['proxy'] != null) {
      account.proxy = JlzsProxyConfig.fromJson(json['proxy']);
    }

    if (json['session'] != null) {
      account.session = JlzsSession.fromJson(json['session']);
    }

    return account;
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'mobile': mobile,
      'nickname': nickname,
      'proxy': proxy?.toJson(),
      'session': session?.toJson(),
      'lastUsed': lastUsed.toIso8601String(),
      'createdAt': createdAt.toIso8601String(),
      'status': status.name,
      'debug_port': debugPort,
      'remark': remark,
    };
  }

  /// 复制实例
  JlzsAccountModel copyWith({
    String? id,
    String? mobile,
    String? nickname,
    JlzsProxyConfig? proxy,
    JlzsSession? session,
    DateTime? lastUsed,
    DateTime? createdAt,
    AccountStatus? status,
    int? debugPort,
    String? remark,
  }) {
    return JlzsAccountModel(
      id: id ?? this.id,
      mobile: mobile ?? this.mobile,
      nickname: nickname ?? this.nickname,
      proxy: proxy ?? this.proxy,
      session: session ?? this.session,
      lastUsed: lastUsed ?? this.lastUsed,
      createdAt: createdAt ?? this.createdAt,
      status: status ?? this.status,
      debugPort: debugPort ?? this.debugPort,
      remark: remark ?? this.remark,
    );
  }

  /// 获取最后使用时间的友好显示
  String get lastUsedFriendly {
    final now = DateTime.now();
    final difference = now.difference(lastUsed);

    if (difference.inDays > 0) {
      return '${difference.inDays}天前';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }

  /// 更新最后使用时间
  void updateLastUsed() {
    lastUsed = DateTime.now();
    if (session != null) {
      session!.lastActive = DateTime.now();
    }
  }

  /// 验证账号信息是否有效
  bool isValid() {
    return id.isNotEmpty && mobile.isNotEmpty && mobile.length >= 11;
  }

  @override
  String toString() {
    return 'JlzsAccountModel{id: $id, mobile: $maskedMobile, status: $statusText, '
           'lastUsed: $lastUsed, createdAt: $createdAt, remark: $remark}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is JlzsAccountModel &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          mobile == other.mobile;

  @override
  int get hashCode => id.hashCode ^ mobile.hashCode;
}
