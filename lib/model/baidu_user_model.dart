class BaiDuUsers {
  String cookie;
  String apiKeyTime;
  String apiKey;
  String username;
  String time_str;
  bool isStart;
  bool isProxy;
  bool isSelected;
  String? proxyAddress;
  String? proxyPort;      // 代理端口
  String? proxyUsername;  // 代理用户名
  String? proxyPassword;  // 代理密码
  int? proxyValidTime;    // 代理有效时间（分钟）
  DateTime? proxyStartTime; // 代理开始时间
  Map<String, String> decryptCache;
  bool isError;

  // 给除 time_str 外的其他参数设置默认值
  BaiDuUsers({
    this.cookie = '',
    this.apiKeyTime = '',
    this.apiKey = '',
    this.username = '',
    this.time_str = '',
    this.proxyUsername = '',
    this.proxyPassword = '',
    this.isStart = false,
    this.isProxy = false,
    this.isSelected = false,
    this.isError = false,
    Map<String, String>? decryptCache,
  }) : decryptCache = decryptCache ?? {};  // 如果传入了 decryptCache 就使用它，否则使用空 Map

}
