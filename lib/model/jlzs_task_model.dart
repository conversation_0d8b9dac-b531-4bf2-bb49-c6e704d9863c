/// 巨量指数任务状态枚举
enum JlzsTaskStatus {
  idle,       // 空闲
  running,    // 运行中
  paused,     // 暂停
  completed,  // 完成
  error,      // 错误
  cancelled,  // 取消
}

/// 巨量指数任务数据模型
class JlzsTaskModel {
  String id;                       // 任务ID
  String name;                     // 任务名称
  JlzsTaskStatus status;           // 任务状态
  DateTime createdAt;              // 创建时间
  DateTime? startedAt;             // 开始时间
  DateTime? completedAt;           // 完成时间
  int totalItems;                  // 总项目数
  int completedItems;              // 已完成项目数
  int errorItems;                  // 错误项目数
  String? errorMessage;            // 错误信息
  Map<String, dynamic> config;    // 任务配置
  List<String> logs;               // 任务日志

  JlzsTaskModel({
    required this.id,
    required this.name,
    this.status = JlzsTaskStatus.idle,
    DateTime? createdAt,
    this.startedAt,
    this.completedAt,
    this.totalItems = 0,
    this.completedItems = 0,
    this.errorItems = 0,
    this.errorMessage,
    this.config = const {},
    this.logs = const [],
  }) : createdAt = createdAt ?? DateTime.now();

  /// 从JSON创建实例
  factory JlzsTaskModel.fromJson(Map<String, dynamic> json) {
    return JlzsTaskModel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      status: JlzsTaskStatus.values.byName(json['status'] ?? 'idle'),
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
      startedAt: json['startedAt'] != null ? DateTime.parse(json['startedAt']) : null,
      completedAt: json['completedAt'] != null ? DateTime.parse(json['completedAt']) : null,
      totalItems: json['totalItems'] ?? 0,
      completedItems: json['completedItems'] ?? 0,
      errorItems: json['errorItems'] ?? 0,
      errorMessage: json['errorMessage'],
      config: Map<String, dynamic>.from(json['config'] ?? {}),
      logs: List<String>.from(json['logs'] ?? []),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'status': status.name,
      'createdAt': createdAt.toIso8601String(),
      'startedAt': startedAt?.toIso8601String(),
      'completedAt': completedAt?.toIso8601String(),
      'totalItems': totalItems,
      'completedItems': completedItems,
      'errorItems': errorItems,
      'errorMessage': errorMessage,
      'config': config,
      'logs': logs,
    };
  }

  /// 复制实例
  JlzsTaskModel copyWith({
    String? id,
    String? name,
    JlzsTaskStatus? status,
    DateTime? createdAt,
    DateTime? startedAt,
    DateTime? completedAt,
    int? totalItems,
    int? completedItems,
    int? errorItems,
    String? errorMessage,
    Map<String, dynamic>? config,
    List<String>? logs,
  }) {
    return JlzsTaskModel(
      id: id ?? this.id,
      name: name ?? this.name,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      startedAt: startedAt ?? this.startedAt,
      completedAt: completedAt ?? this.completedAt,
      totalItems: totalItems ?? this.totalItems,
      completedItems: completedItems ?? this.completedItems,
      errorItems: errorItems ?? this.errorItems,
      errorMessage: errorMessage ?? this.errorMessage,
      config: config ?? this.config,
      logs: logs ?? this.logs,
    );
  }

  /// 获取任务状态显示名称
  String get statusDisplayName {
    switch (status) {
      case JlzsTaskStatus.idle:
        return '空闲';
      case JlzsTaskStatus.running:
        return '运行中';
      case JlzsTaskStatus.paused:
        return '暂停';
      case JlzsTaskStatus.completed:
        return '完成';
      case JlzsTaskStatus.error:
        return '错误';
      case JlzsTaskStatus.cancelled:
        return '取消';
    }
  }

  /// 获取进度百分比
  double get progressPercentage {
    if (totalItems == 0) return 0.0;
    return (completedItems / totalItems) * 100;
  }

  /// 获取剩余项目数
  int get remainingItems {
    return totalItems - completedItems - errorItems;
  }

  /// 获取任务持续时间
  Duration? get duration {
    if (startedAt == null) return null;
    final endTime = completedAt ?? DateTime.now();
    return endTime.difference(startedAt!);
  }

  /// 获取任务持续时间的友好显示
  String get durationFriendly {
    final dur = duration;
    if (dur == null) return '未开始';
    
    if (dur.inHours > 0) {
      return '${dur.inHours}小时${dur.inMinutes % 60}分钟';
    } else if (dur.inMinutes > 0) {
      return '${dur.inMinutes}分钟${dur.inSeconds % 60}秒';
    } else {
      return '${dur.inSeconds}秒';
    }
  }

  /// 开始任务
  void start() {
    status = JlzsTaskStatus.running;
    startedAt = DateTime.now();
    addLog('任务开始执行');
  }

  /// 暂停任务
  void pause() {
    if (status == JlzsTaskStatus.running) {
      status = JlzsTaskStatus.paused;
      addLog('任务已暂停');
    }
  }

  /// 恢复任务
  void resume() {
    if (status == JlzsTaskStatus.paused) {
      status = JlzsTaskStatus.running;
      addLog('任务已恢复');
    }
  }

  /// 完成任务
  void complete() {
    status = JlzsTaskStatus.completed;
    completedAt = DateTime.now();
    addLog('任务执行完成');
  }

  /// 设置错误状态
  void setError(String message) {
    status = JlzsTaskStatus.error;
    errorMessage = message;
    addLog('任务执行出错: $message');
  }

  /// 取消任务
  void cancel() {
    status = JlzsTaskStatus.cancelled;
    completedAt = DateTime.now();
    addLog('任务已取消');
  }

  /// 更新进度
  void updateProgress(int completed, int errors) {
    completedItems = completed;
    errorItems = errors;
    addLog('进度更新: 完成 $completed/$totalItems, 错误 $errors');
  }

  /// 添加日志
  void addLog(String message) {
    final timestamp = DateTime.now().toString().substring(0, 19);
    logs.add('[$timestamp] $message');
  }

  /// 清空日志
  void clearLogs() {
    logs.clear();
  }

  /// 获取最新的日志条目
  List<String> getRecentLogs(int count) {
    if (logs.length <= count) return logs;
    return logs.sublist(logs.length - count);
  }

  /// 判断任务是否可以开始
  bool canStart() {
    return status == JlzsTaskStatus.idle || status == JlzsTaskStatus.error;
  }

  /// 判断任务是否可以暂停
  bool canPause() {
    return status == JlzsTaskStatus.running;
  }

  /// 判断任务是否可以恢复
  bool canResume() {
    return status == JlzsTaskStatus.paused;
  }

  /// 判断任务是否可以取消
  bool canCancel() {
    return status == JlzsTaskStatus.running || status == JlzsTaskStatus.paused;
  }

  /// 判断任务是否已完成
  bool get isCompleted {
    return status == JlzsTaskStatus.completed;
  }

  /// 判断任务是否有错误
  bool get hasError {
    return status == JlzsTaskStatus.error || errorItems > 0;
  }

  /// 判断任务是否正在运行
  bool get isRunning {
    return status == JlzsTaskStatus.running;
  }

  @override
  String toString() {
    return 'JlzsTaskModel{id: $id, name: $name, status: $status, '
           'progress: $completedItems/$totalItems, errors: $errorItems}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is JlzsTaskModel &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}
