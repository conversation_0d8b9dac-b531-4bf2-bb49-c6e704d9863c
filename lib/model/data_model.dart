class DataModel {
    String? region;
    String? id;
    bool isCompleted;
    Map<String, List<Drama>>? data; // 以年份为键，值为包含关键词的 Drama 列表

    DataModel({this.region, this.id, this.isCompleted = false, this.data});

    // 从 JSON 转换为实体对象
    factory DataModel.fromJson(Map<String, dynamic> json) {
        return DataModel(
            region: json['region'],
            id: json['id'],
            isCompleted: json['isCompleted'],
            data: json['data'] != null
                ? (json['data'] as Map<String, dynamic>).map((year, value) {
                return MapEntry(
                    year,
                    (value as List).map((e) => Drama.fromJson(e)).toList(),
                );
            })
                : null,
        );
    }

    // 将实体对象转换为 JSON
    Map<String, dynamic> toJson() {
        return {
            'region': region,
            'id': id,
            'isCompleted': isCompleted,
            'data': data?.map((key, value) {
                return MapEntry(key, value.map((e) => e.toJson()).toList());
            }),
        };
    }
}

class Drama {
    String? keyword;
    String? startData;
    String? endData;
    bool? isCompleted;
    List<dynamic>? all;
    List<dynamic>? pc;
    List<dynamic>? wise;

    Drama({this.keyword, this.startData, this.endData, this.isCompleted, this.all, this.pc, this.wise});

    // 从 JSON 转换为实体对象
    factory Drama.fromJson(Map<String, dynamic> json) {
        return Drama(
            keyword: json['keyword'],
            startData: json['startData'],
            endData: json['endData'],
            isCompleted: json['isCompleted'],
            all: json['all'] ?? [],
            pc: json['pc'] ?? [],
            wise: json['wise'] ?? [],
        );
    }

    // 将实体对象转换为 JSON
    Map<String, dynamic> toJson() {
        return {
            'keyword': keyword,
            'startData': startData,
            'endData': endData,
            'isCompleted': isCompleted,
            'all': all,
            'pc': pc,
            'wise': wise,
        };
    }
}
