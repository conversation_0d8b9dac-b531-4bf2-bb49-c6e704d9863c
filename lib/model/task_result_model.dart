import 'package:bd/model/data_model.dart';

import 'baidu_user_model.dart';

// 在 TaskModel 中添加 toString 方法
class TaskModel {
  final DataModel dataModel;
  final String keyword;
  final Drama drama;
  final String year;
  final String cityId;
  final String region;

  TaskModel({
    required this.dataModel,
    required this.keyword,
    required this.drama,
    required this.year,
    required this.cityId,
    required this.region,
  });

  @override
  String toString() {
    return '{region: $region, keyword: $keyword, year: $year, startDate: ${drama.startData}, endDate: ${drama.endData}}';
  }
}
// 任务结果模型
class TaskResult {
  final bool success;
  final String message;
  final TaskModel task;
  final BaiDuUsers user;

  TaskResult({
    required this.success,
    required this.message,
    required this.task,
    required this.user,
  });
}