# 巨量指数数据保存功能需求文档

## 1. 功能概述

完善巨量指数模块的数据保存功能，支持分开查询和合并查询两种模式的数据导出，参考百度指数的实现方式，提供灵活的数据保存方案。

## 2. 数据结构分析

### 2.1 分开查询模式数据结构
```dart
class JlzsDataModel {
  String? region;           // 地区名称
  String platform;         // 平台 (aweme/toutiao)
  bool isCompleted;        // 是否完成采集
  Map<String, List<JlzsKeywordData>>? data; // 以日期为键，关键词数据为值
}

class JlzsKeywordData {
  String keyword;          // 关键词
  String hotIndex;         // 综合指数
  String searchIndex;      // 搜索指数
}
```

### 2.2 合并查询模式数据结构
```dart
class JlzsMergedData {
  String keyword;          // 关键词
  String date;            // 日期 (YYYYMMDD)
  String platform;        // 平台 (aweme/toutiao)
  List<String> regions;   // 地区列表
  String hotIndex;        // 综合指数
  String searchIndex;     // 搜索指数
}
```

## 3. 保存功能需求

### 3.1 分开查询模式保存
- **文件夹结构**: `{地区名称}/{关键词}/`
- **文件命名**: `{地区}-{平台}-{处理周期}-{关键词}-{开始日期}-{结束日期}.csv`
- **数据格式**: 
  - 表头: ["关键词", "地区", "平台", "日期/周期", "综合指数", "搜索指数"]
  - 数据行: 按时间顺序排列的指数数据

### 3.2 合并查询模式保存
- **文件夹结构**: `合并查询结果/`
- **文件命名**: `合并查询-{平台}-{处理周期}-{开始日期}-{结束日期}.csv`
- **数据格式**:
  - 表头: ["关键词", "日期/周期", "平台", "地区列表", "综合指数", "搜索指数"]
  - 数据行: 包含所有地区的综合数据

### 3.3 处理周期支持
根据配置的`processingPeriods`支持多种时间聚合：
- **day**: 按日导出
- **week**: 按周聚合导出
- **month**: 按月聚合导出
- **year**: 按年聚合导出

### 3.4 聚合方式支持
根据配置的`aggregationMethod`：
- **sum**: 求和聚合
- **average**: 平均值聚合

## 4. 技术实现方案

### 4.1 核心方法实现
```dart
/// 保存采集结果到文件
void _saveCollectionResults() {
  try {
    // 1. 处理分开查询数据
    if (config.value.regionType == 'separate') {
      _saveSeparateQueryResults();
    }
    
    // 2. 处理合并查询数据
    if (config.value.regionType == 'merged') {
      _saveMergedQueryResults();
    }
    
    addLog('✅ 数据保存完成');
  } catch (e) {
    addLog('❌ 保存数据失败: $e');
  }
}
```

### 4.2 分开查询数据保存
```dart
Future<void> _saveSeparateQueryResults() async {
  for (final dataModel in separateData) {
    if (dataModel.data == null || dataModel.data!.isEmpty) continue;
    
    // 按关键词分组保存
    final keywordGroups = _groupDataByKeyword(dataModel);
    
    for (final keyword in keywordGroups.keys) {
      // 创建文件夹
      final path = await StoreUtil.checkAndCreateFolders(
        dataModel.region ?? '未知地区', 
        keyword
      );
      
      // 按处理周期生成数据
      for (final period in config.value.processingPeriods) {
        final csvData = _generateCsvDataForPeriod(
          keywordGroups[keyword]!, 
          dataModel, 
          keyword, 
          period
        );
        
        // 导出CSV文件
        final fileName = _generateFileName(
          dataModel.region, 
          dataModel.platform, 
          period, 
          keyword
        );
        
        exportCsv(csvData, path, fileName);
        addLog('✅ 保存成功: ${dataModel.region}-$keyword-$period');
      }
    }
  }
}
```

### 4.3 合并查询数据保存
```dart
Future<void> _saveMergedQueryResults() async {
  if (mergedData.isEmpty) return;
  
  // 创建合并查询结果文件夹
  final path = await StoreUtil.checkAndCreateFolders('', '合并查询结果');
  
  // 按处理周期生成数据
  for (final period in config.value.processingPeriods) {
    final csvData = _generateMergedCsvDataForPeriod(period);
    
    final fileName = _generateMergedFileName(period);
    exportCsv(csvData, path, fileName);
    addLog('✅ 保存成功: 合并查询-$period');
  }
}
```

### 4.4 数据聚合处理
```dart
List<List<dynamic>> _generateCsvDataForPeriod(
  Map<String, List<JlzsKeywordData>> keywordData,
  JlzsDataModel dataModel,
  String keyword,
  String period
) {
  List<List<dynamic>> csvData = [];
  
  // 添加表头
  csvData.add(["关键词", "地区", "平台", _getPeriodHeader(period), "综合指数", "搜索指数"]);
  
  // 根据周期聚合数据
  final aggregatedData = _aggregateDataByPeriod(keywordData, period);
  
  // 生成CSV行数据
  for (final entry in aggregatedData.entries) {
    for (final keywordInfo in entry.value) {
      csvData.add([
        keyword,
        dataModel.region,
        dataModel.platform,
        entry.key, // 时间周期
        keywordInfo.hotIndex,
        keywordInfo.searchIndex
      ]);
    }
  }
  
  return csvData;
}
```

## 5. 文件命名规范

### 5.1 分开查询文件命名
- 格式: `{地区}-{平台}-{周期}-{关键词}-{开始日期}-{结束日期}.csv`
- 示例: `北京-aweme-日-手机-20240101-20240131.csv`

### 5.2 合并查询文件命名
- 格式: `合并查询-{平台}-{周期}-{开始日期}-{结束日期}.csv`
- 示例: `合并查询-aweme-月-20240101-20240131.csv`

## 6. 错误处理和日志

### 6.1 错误处理
- 数据为空时的处理
- 文件创建失败的处理
- CSV导出异常的处理

### 6.2 日志记录
- 保存进度提示
- 成功/失败状态记录
- 文件路径信息输出

## 7. 依赖和工具

### 7.1 现有依赖
- `csv: ^6.0.0` - CSV文件生成
- `path: ^1.8.0` - 路径处理

### 7.2 现有工具类
- `StoreUtil.checkAndCreateFolders()` - 文件夹创建
- `exportCsv()` - CSV导出（需从百度指数模块复用）

## 8. 实现优先级

1. **高优先级**: 分开查询模式的基础保存功能
2. **中优先级**: 合并查询模式的保存功能
3. **低优先级**: 多周期聚合和高级数据处理功能

## 9. 测试验证

### 9.1 功能测试
- 分开查询数据保存测试
- 合并查询数据保存测试
- 多周期数据聚合测试

### 9.2 边界测试
- 空数据处理测试
- 大量数据处理测试
- 异常情况处理测试
