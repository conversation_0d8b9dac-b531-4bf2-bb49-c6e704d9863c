# AccountManageDialog 重构方案

## 项目背景

**文件路径**: `lib/pages/jlzs/widgets/account_manage_dialog.dart`
**当前状态**: 1258行代码，单文件过于复杂
**重构目标**: 拆分为多个组件，保证功能完全不变

## 当前文件分析

### 文件结构概览
```
AccountManageDialog (StatefulWidget)
├── _AccountManageDialogState
    ├── build() - 主UI构建 (26-185行)
    │   ├── 标题栏 (36-53行)
    │   ├── 操作按钮栏 (57-138行)
    │   └── 账号列表 (142-180行)
    ├── _buildAccountCard() - 账号卡片构建 (188-354行)
    ├── _buildStatusChip() - 状态标签 (357-394行)
    ├── _buildMsTokenStatus() - Token状态显示 (397-456行)
    ├── _showAddAccountDialog() - 添加账号对话框 (459-712行)
    ├── _startLoginProcess() - 登录流程 (715-757行)
    ├── 账号操作方法 (760-857行)
    │   ├── _closeBrowser()
    │   ├── _toggleAccountStatus()
    │   └── _refreshMsToken()
    ├── 账号管理方法 (860-992行)
    │   ├── _editAccount()
    │   ├── _deleteAccount()
    │   ├── _checkAllMsTokens()
    │   ├── _batchRefreshMsToken()
    │   └── _batchDelete()
    └── 代理相关方法 (995-1256行)
        ├── _validateProxy()
        ├── _autoGetProxy()
        ├── _showProxyApiConfigDialog()
        ├── _fetchProxyFromApi()
        └── _parseProxyResponse()
```

### 依赖关系分析
- **核心依赖**: JlzsLogic (Get.find<JlzsLogic>())
- **数据模型**: JlzsAccountModel, JlzsProxyConfig, ProxyType
- **UI框架**: Flutter Material, GetX
- **状态管理**: Obx, RxList, selectedAccounts, isSelectMode

### 功能职责分析
1. **UI渲染职责** (约30%)
   - 对话框布局
   - 账号列表显示
   - 状态标签渲染

2. **表单管理职责** (约25%)
   - 添加账号表单
   - 代理配置表单
   - 表单验证逻辑

3. **账号操作职责** (约20%)
   - 启用/禁用账号
   - 删除账号
   - 刷新Token

4. **批量操作职责** (约15%)
   - 批量选择模式
   - 批量删除
   - 批量刷新Token

5. **代理管理职责** (约10%)
   - 代理验证
   - 自动获取代理
   - API配置

## 拆分方案设计

### 组件层次结构
```
AccountManageDialog (主对话框)
├── AccountActionBar (操作按钮栏)
├── AccountListWidget (账号列表)
│   └── AccountCardWidget (账号卡片)
│       ├── AccountStatusChip (状态标签)
│       └── MsTokenStatusWidget (Token状态)
├── AddAccountDialog (添加账号对话框)
│   └── ProxyConfigWidget (代理配置)
└── BatchOperationMixin (批量操作逻辑)
```

### 文件拆分计划

#### 1. account_manage_dialog.dart (主文件重构)
**保留内容**:
- 主对话框结构
- 组件组合逻辑
- 状态管理 (selectedAccounts, isSelectMode)

**移除内容**:
- 具体UI构建逻辑
- 表单处理逻辑
- 业务操作方法

#### 2. account_action_bar.dart (新建)
**功能**:
- 添加账号按钮
- 刷新状态按钮
- 检查Token按钮
- 批量操作切换
- 账号统计显示

**接口**:
```dart
class AccountActionBar extends StatelessWidget {
  final VoidCallback onAddAccount;
  final VoidCallback onRefreshAll;
  final VoidCallback onCheckTokens;
  final VoidCallback onToggleBatchMode;
  final VoidCallback onBatchDelete;
  final VoidCallback onBatchRefreshToken;
  final bool isSelectMode;
  final int selectedCount;
  final int totalCount;
}
```

#### 3. account_list_widget.dart (新建)
**功能**:
- 账号列表渲染
- 空状态显示
- 列表滚动管理

**接口**:
```dart
class AccountListWidget extends StatelessWidget {
  final List<JlzsAccountModel> accounts;
  final bool isSelectMode;
  final Set<String> selectedAccounts;
  final Function(String) onAccountSelect;
  final Function(JlzsAccountModel) onAccountAction;
}
```

#### 4. account_card_widget.dart (新建)
**功能**:
- 单个账号信息显示
- 操作按钮组
- 选择状态管理

**接口**:
```dart
class AccountCardWidget extends StatelessWidget {
  final JlzsAccountModel account;
  final bool isSelectMode;
  final bool isSelected;
  final VoidCallback? onSelect;
  final Function(AccountAction) onAction;
}

enum AccountAction {
  toggle, refresh, closeBrowser, edit, delete
}
```

#### 5. add_account_dialog.dart (新建)
**功能**:
- 账号添加表单
- 表单验证
- 登录流程启动

**接口**:
```dart
class AddAccountDialog extends StatefulWidget {
  final Function(String remark, JlzsProxyConfig? proxy) onStartLogin;
}
```

#### 6. proxy_config_widget.dart (新建)
**功能**:
- 代理配置表单
- 代理验证
- 自动获取代理

**接口**:
```dart
class ProxyConfigWidget extends StatefulWidget {
  final JlzsProxyConfig? initialProxy;
  final Function(JlzsProxyConfig?) onProxyChanged;
}
```

## API和依赖关系分析

### GetX状态管理模式
项目使用GetX进行状态管理，主要模式：
- **响应式变量**: `final selectedAccounts = <String>{}.obs;`
- **响应式列表**: `final RxList<JlzsAccountModel> accounts = <JlzsAccountModel>[].obs;`
- **UI响应**: `Obx(() => Widget)` 用于响应式UI更新
- **控制器注入**: `final JlzsLogic logic = Get.find<JlzsLogic>();`
- **对话框**: `Get.dialog()` 用于显示对话框
- **提示消息**: `Get.snackbar()` 用于显示提示

### JlzsLogic核心方法
- `refreshAllAccountStatus()` - 刷新所有账号状态
- `checkAllMsTokens()` - 检查所有账号Token
- `refreshAccountMsToken(account)` - 刷新指定账号Token
- `removeAccount(accountId)` - 删除账号
- `startLoginProcessWithoutMobile()` - 开始登录流程
- `browserManager.closeBrowserForAccount()` - 关闭浏览器实例

### 数据模型接口
- `JlzsAccountModel` - 账号模型，包含id、mobile、proxy、session等
- `JlzsProxyConfig` - 代理配置，包含address、port、username、password等
- `AccountStatus` - 账号状态枚举：active、offline、error、expired、inactive
- `ProxyType` - 代理类型枚举：http、socks5

## 实施计划

### 阶段1: 准备工作
- [x] 创建重构文档
- [x] 分析现有API和依赖关系
- [ ] 确定组件接口设计

### 阶段2: 创建基础组件
- [x] 创建 AccountStatusChip 组件
- [x] 创建 MsTokenStatusWidget 组件
- [x] 测试基础组件功能

### 阶段3: 创建复合组件
- [ ] 创建 AccountCardWidget 组件
- [ ] 创建 AccountListWidget 组件
- [ ] 创建 AccountActionBar 组件

### 阶段4: 创建对话框组件
- [ ] 创建 ProxyConfigWidget 组件
- [ ] 创建 AddAccountDialog 组件

### 阶段5: 重构主文件
- [ ] 重构 AccountManageDialog 主文件
- [ ] 集成所有子组件
- [ ] 测试完整功能

### 阶段6: 测试和优化
- [ ] 功能完整性测试
- [ ] 性能优化
- [ ] 代码清理

## 注意事项

1. **保持功能不变**: 重构过程中不能改变任何现有功能
2. **状态管理**: 确保GetX状态管理正确传递
3. **依赖注入**: 保持JlzsLogic的正确注入和使用
4. **错误处理**: 保持原有的错误处理逻辑
5. **UI一致性**: 保持原有的UI样式和交互

## 风险评估

**低风险**:
- UI组件拆分
- 状态标签组件

**中风险**:
- 表单组件拆分
- 状态管理传递

**高风险**:
- 主对话框重构
- 批量操作逻辑

## 进度跟踪

**开始时间**: 2025-01-01
**预计完成**: 待定
**当前阶段**: 阶段3 - 创建复合组件
**完成进度**: 35%

## 实施记录

### 阶段2完成记录 (2025-01-01)
✅ **AccountStatusChip组件创建完成**
- 文件: `lib/pages/jlzs/widgets/components/account_status_chip.dart`
- 功能: 显示账号状态标签 (active/offline/error/expired/inactive)
- 样式: 保持与原版完全一致的颜色和布局
- 测试: 已集成到主文件并通过编译检查

✅ **MsTokenStatusWidget组件创建完成**
- 文件: `lib/pages/jlzs/widgets/components/ms_token_status_widget.dart`
- 功能: 显示msToken状态和过期时间
- 逻辑: 支持未获取/有效/即将过期/已过期状态显示
- 测试: 已集成到主文件并通过编译检查

✅ **主文件集成完成**
- 已导入新组件
- 已替换原有方法调用
- 已删除重复的 `_buildStatusChip` 和 `_buildMsTokenStatus` 方法
- 代码行数减少: 1258行 → 1200行 (减少58行)
