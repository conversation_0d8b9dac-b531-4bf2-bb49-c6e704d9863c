# 巨量指数代理IP配置优化需求

## 📋 需求概述

基于用户反馈，巨量指数模块的代理IP配置需要进行以下优化：

## 🔧 具体需求

### 1. 代理认证方式修复
**当前问题**: 使用浏览器启动参数 `--proxy-auth=username:password` 进行代理认证
**需求**: 改为使用 `page.authenticate()` 方法进行浏览器代理验证
**原因**: 某些代理服务器不支持启动参数认证方式

**实现要点**:
- 在账号添加时勾选使用代理
- 代理验证成功后使用 `page.authenticate()` 方法
- 移除启动参数中的 `--proxy-auth` 配置

### 2. 代理有效时间输入框
**当前问题**: 缺少代理有效时间配置
**需求**: 添加代理有效时间输入框，单位为分钟
**UI位置**: 在代理配置区域添加"有效时间(分钟)"输入框

### 3. 代理类型说明
**当前问题**: 代理类型有HTTP和SOCKS5两种，用户不明白区别
**需求**: 
- 添加代理类型说明文档
- 在UI中添加提示信息
- 默认推荐使用HTTP类型

### 4. 代理自动获取功能
**当前问题**: 需要手动输入代理信息
**需求**: 支持通过API自动获取代理IP
**功能要点**:
- 添加"自动获取代理"按钮
- 配置代理API接口地址
- 自动填充代理信息到输入框
- 支持批量获取多个代理

**UI设计建议**:
- 在代理配置区域添加"自动获取"按钮
- 添加代理API配置入口
- 显示获取状态和结果

### 5. 禁用账号功能修复
**当前问题**: 禁用账号没有反应
**需求**: 实现账号禁用/启用功能
**功能要点**:
- 禁用账号时关闭对应浏览器实例
- 更新账号状态显示
- 禁用状态下不参与任务执行

### 6. msToken获取和管理
**当前问题**: 登录成功后需要获取和管理msToken
**需求**: 
- 登录成功后自动获取浏览器cookie中的msToken
- 判断msToken有效期（天数）
- 将msToken与登录账号关联存储
- 为后续任务提供msToken支持

**实现要点**:
- 在登录成功回调中提取msToken
- 解析msToken有效期
- 更新账号模型存储msToken信息
- 添加msToken有效性检查

## 🎯 优先级排序

1. **高优先级**: 代理认证方式修复、msToken获取
2. **中优先级**: 代理有效时间、禁用账号功能
3. **低优先级**: 代理自动获取、代理类型说明

## 📝 技术实现要点

### 代理认证修复
```dart
// 移除启动参数认证
// args.add('--proxy-auth=${account.proxy!.username}:${account.proxy!.password}');

// 改为页面级认证
if (account.proxy?.username != null && account.proxy?.password != null) {
  await page.authenticate(
    username: account.proxy!.username!, 
    password: account.proxy!.password!
  );
}
```

### msToken提取
```dart
// 获取所有cookies
var cookies = await page.cookies();
var msToken = cookies.firstWhere(
  (cookie) => cookie.name == 'msToken',
  orElse: () => null,
);

if (msToken != null) {
  // 解析有效期并存储
  account.session?.msToken = msToken.value;
  account.session?.msTokenExpiry = msToken.expires;
}
```

## 🔄 实施计划

1. 修复代理认证方式
2. 添加代理有效时间配置
3. 实现msToken获取和存储
4. 修复禁用账号功能
5. 实现代理自动获取
6. 完善代理类型说明

## 📋 验收标准

- [ ] 代理认证使用page.authenticate()方法
- [ ] 代理有效时间输入框正常工作
- [ ] msToken正确获取并关联账号
- [ ] 禁用账号功能正常
- [ ] 代理自动获取功能可用
- [ ] 所有功能编译无错误
