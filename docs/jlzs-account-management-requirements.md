# 巨量指数账号管理系统需求文档

## 1. 项目概述

### 1.1 背景
巨量指数（TrendInsight）是字节跳动旗下的数据分析平台，需要通过手机验证码登录。本系统需要实现多账号管理、登录状态持久化、以及实时数据拦截功能。

### 1.2 核心需求
- 支持多账号同时在线管理
- 每个账号独立浏览器实例和代理配置
- 登录状态持久化（浏览器不关闭）
- 实时拦截API数据进行Hook处理
- 账号信息安全存储

## 2. 技术架构设计

### 2.1 核心组件
```
JlzsAccountManager
├── BrowserManager          # 浏览器实例管理
├── SessionManager          # 会话状态管理  
├── InterceptorManager      # JS拦截器管理
├── ProxyManager           # 代理配置管理
└── StorageManager         # 数据持久化管理
```

### 2.2 数据流设计
```
用户添加账号 → 创建浏览器实例 → 执行登录流程 → 拦截认证API → 提取会话数据 → 持久化存储
```

## 3. 登录流程设计

### 3.1 登录页面信息
- **登录URL**: `https://trendinsight.oceanengine.com/login`
- **登录方式**: 手机号 + 短信验证码
- **成功跳转**: `https://trendinsight.oceanengine.com/`

### 3.2 关键表单元素
```html
<!-- 手机号输入框 -->
<input name="mobile" type="number" placeholder="请输入手机号码" />

<!-- 图形验证码（可能出现） -->
<input name="captcha" type="text" placeholder="验证码" />

<!-- 短信验证码 -->
<input name="mobilecaptcha" type="number" placeholder="验证码" />

<!-- 发送验证码按钮 -->
<div class="account-center-code-captcha">发送验证码</div>

<!-- 用户协议勾选 -->
<div class="account-center-agreement-check"></div>

<!-- 登录按钮 -->
<button class="account-center-action-button">登录</button>
```

### 3.3 登录状态验证
**拦截API**: `https://trendinsight.oceanengine.com/passport/account/info/v2/`

**关键返回数据**:
```json
{
  "data": {
    "user_id": ****************,
    "sec_user_id": "MS4wLjABAAAA7v8fgYjXiKS-s7jQWJtSJ_Qr-wSQzavQCSERU4EorcB5rZ54hAYm11hlT6ung3IH",
    "mobile": "156******70",
    "name": "用户*************",
    "avatar_url": "https://p3-passport.byteacctimg.com/img/mosaic-legacy/3792/**********~120x256.image",
    "connects": [
      {
        "platform": "aweme_v2",
        "platform_screen_name": "软软宝宝真可爱",
        "sec_platform_uid": "MS4wLjABAAAA4HFwEI5JqMh2a2AtFa5a588sKwTQN97U5chE0BIF1FM"
      }
    ]
  }
}
```

## 4. 系统架构实现

### 4.1 浏览器管理器
- 每个账号独立的浏览器实例
- 独立的用户数据目录：`./jlzs_data/account_{id}`
- 独立的调试端口分配
- 代理配置隔离

### 4.2 登录流程管理器
- 自动化登录流程
- 手机号输入
- 图形验证码处理（如需要）
- 短信验证码等待用户输入
- 登录成功检测

### 4.3 API拦截器
- 监听 `/passport/account/info/v2/` 接口
- 提取用户信息和关联账号数据
- 实时Hook其他关键API
- 数据回调处理

### 4.4 会话数据管理
- Cookies持久化
- 用户信息存储
- 关联账号信息管理
- 会话有效性检查

### 4.5 浏览器实例管理
采用管理器模式维护账号与浏览器实例的映射关系：

```dart
class JlzsBrowserManager {
  static final JlzsBrowserManager _instance = JlzsBrowserManager._internal();
  factory JlzsBrowserManager() => _instance;
  JlzsBrowserManager._internal();

  // 账号ID -> 浏览器实例映射
  final Map<String, Browser> _accountBrowsers = {};
  // 账号ID -> 调试端口映射
  final Map<String, int> _debugPorts = {};

  // 获取账号对应的浏览器实例
  Browser? getBrowserByAccountId(String accountId);

  // 为账号创建浏览器实例
  Future<Browser> createBrowserForAccount(JlzsAccount account);

  // 关闭账号对应的浏览器
  Future<void> closeBrowserForAccount(String accountId);

  // 重新连接到现有浏览器实例
  Future<Browser?> reconnectToBrowser(String accountId, int debugPort);
}
```

**设计优势**：
- 职责分离：账号模型只管数据，管理器负责浏览器实例
- 便于序列化：账号对象可以正常转JSON存储
- 统一管理：所有浏览器实例集中控制
- 内存优化：支持懒加载和资源回收

**生命周期管理**：
- 登录成功后浏览器实例保持运行状态
- 账号删除时关闭对应浏览器实例
- 应用重启时通过调试端口重新连接
- 支持手动关闭和重启浏览器实例

## 5. 数据模型设计

### 5.1 账号模型
```dart
class JlzsAccount {
  String id;                    // 账号ID
  String mobile;                // 手机号
  String? nickname;             // 昵称
  ProxyConfig? proxy;           // 代理配置
  JlzsSession? session;         // 会话信息
  DateTime createTime;          // 创建时间
  DateTime? lastLoginTime;      // 最后登录时间
  AccountStatus status;         // 账号状态
  int? debugPort;               // 浏览器调试端口（用于重连）

  // 注意：不直接持有Browser实例，通过JlzsBrowserManager获取
  // Browser实例通过账号ID在管理器中查找
}
```

**账号与浏览器实例关联方式**：
```dart
// 获取账号对应的浏览器实例
var browserManager = JlzsBrowserManager();
var browser = browserManager.getBrowserByAccountId(account.id);
if (browser != null) {
  var page = await browser.newPage();
  await page.goto('https://trendinsight.oceanengine.com/');
}
```

### 5.2 关联账号模型
```dart
class ConnectedAccount {
  String platform;             // aweme_v2, toutiao
  String platformUid;          // 平台用户ID
  String secPlatformUid;       // 加密用户ID
  String screenName;           // 显示名称
  String profileImageUrl;      // 头像URL
  DateTime expiredTime;        // 过期时间
}
```

### 5.3 代理配置模型
```dart
class ProxyConfig {
  String address;              // 代理地址
  int port;                    // 代理端口
  String? username;            // 用户名
  String? password;            // 密码
  ProxyType type;              // HTTP/SOCKS5
}
```

## 6. 用户界面设计

### 6.1 配置管理区域的账号统计显示

**当前问题**：巨量指数配置管理区域缺少账号状态统计显示，不如百度指数页面完善。

**改进设计**：
```
┌─────────────────────────────────────┐
│ 账号管理                             │
├─────────────────────────────────────┤
│ [账号管理] 按钮                      │
│                                    │
│ 账号状态统计:                       │
│ ┌─────────────────────────────────┐ │
│ │ 📊 3账号  🔐 2已登录  ✅ 1已启用 │ │
│ └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

**统计项说明**：
- **账号数**: 显示总账号数量，灰色图标
- **已登录**: 显示在线/已登录账号数，蓝色图标
- **已启用**: 显示启用状态的账号数，绿色图标

**实现要求**：
```dart
// 参考百度指数的实现方式
Row(
  children: [
    _buildCountItem(
      count: jlzsAccounts.length,
      label: "账号",
      color: Colors.grey[700]!,
    ),
    SizedBox(width: 8),
    _buildCountItem(
      count: jlzsAccounts.where((account) => account.isOnline).length,
      label: "已登录",
      color: Colors.blue[700]!,
    ),
    SizedBox(width: 8),
    _buildCountItem(
      count: jlzsAccounts.where((account) => account.isActive).length,
      label: "已启用",
      color: Colors.green[700]!,
    ),
  ],
)
```

### 6.2 账号管理弹窗
```
┌─────────────────────────────────────┐
│ 巨量指数账号管理                      │
├─────────────────────────────────────┤
│ [+添加账号] [刷新状态] [批量操作]      │
├─────────────────────────────────────┤
│ 账号列表:                           │
│ ┌─────────────────────────────────┐ │
│ │ ☐ 📱 156****70 [在线] [编辑][删除] │ │
│ ├─────────────────────────────────┤ │
│ │ ☐ 📱 138****88 [离线] [编辑][删除] │ │
│ ├─────────────────────────────────┤ │
│ │ ☐ 📱 139****99 [错误] [编辑][删除] │ │
│ └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

**功能说明**：
- **复选框**: 支持多选进行批量操作
- **刷新状态**: 检查所有账号的实时在线状态和会话有效性
- **批量操作**: 支持批量删除、批量设置代理、批量启用/禁用等操作
- **状态显示**: 在线/离线/错误/已过期/未激活
- **关联账号**: 点击[编辑]查看详细的抖音/头条关联账号信息

### 6.3 添加账号流程
```
步骤1: 代理配置（可选）
┌─────────────────────────┐
│ 代理设置:               │
│ ☐ 使用HTTP代理          │
│   服务器: [___________] │
│   端口:   [_____]       │
│   用户名: [___________] │
│   密码:   [___________] │
│                        │
│ ☐ 使用SOCKS5代理        │
│   服务器: [___________] │
│   端口:   [_____]       │
│   用户名: [___________] │
│   密码:   [___________] │
│                        │
│        [开始登录]       │
└─────────────────────────┘

步骤2: 浏览器登录
┌─────────────────────────┐
│ 🌐 浏览器窗口已打开      │
│ 请在浏览器中完成以下步骤: │
│ 1. 输入手机号码         │
│ 2. 获取并输入短信验证码  │
│ 3. 勾选用户协议         │
│ 4. 点击登录按钮         │
│                        │
│ 💡 软件正在监听登录状态... │
│ [取消] [重新开始]       │
└─────────────────────────┘

步骤3: 登录成功
┌─────────────────────────┐
│ ✅ 账号添加成功          │
│ 手机号: 156****70       │
│ 用户名: 用户*************│
│ 关联平台: 2个抖音账号    │
│ 代理状态: 已配置HTTP代理  │
│                        │
│        [完成]           │
└─────────────────────────┘
```

**代理配置说明**：
- **HTTP代理**: 适用于大多数网络环境，支持用户名密码认证
- **SOCKS5代理**: 更高级的代理协议，支持UDP和TCP
- **认证信息**: 用户名和密码为可选项，部分代理服务器需要
- **代理隔离**: 每个账号使用独立的代理IP，避免账号关联
- **代理验证**: 启动浏览器前会测试代理连接是否正常

## 7. 实现要点

### 7.1 关键技术点
1. **浏览器持久化**: 使用userDataDir保持登录状态
2. **API拦截**: 监听account/info接口获取用户信息
3. **多实例隔离**: 每个账号独立端口和数据目录
4. **代理支持**: 支持HTTP/SOCKS5代理配置
5. **会话恢复**: 支持应用重启后恢复账号状态
6. **实例管理**: 通过管理器模式统一管理浏览器实例生命周期
7. **端口管理**: 自动分配和管理调试端口，支持重连机制

### 7.2 安全考虑
1. **数据加密**: 敏感信息本地加密存储
2. **代理隔离**: 每个账号使用独立代理IP
3. **会话管理**: 定期检查账号在线状态
4. **错误处理**: 登录失败、网络异常等容错机制

### 7.3 性能优化
1. **懒加载**: 按需启动浏览器实例
2. **资源管理**: 及时释放无用的页面资源
3. **并发控制**: 限制同时登录的账号数量
4. **缓存策略**: 缓存用户信息减少API调用

## 8. 开发计划

### Phase 1: 基础架构 (1周)
- [ ] 浏览器管理器实现
- [ ] 数据模型定义
- [ ] 基础UI框架

### Phase 2: 登录流程 (1周)
- [ ] 登录流程自动化
- [ ] API拦截实现
- [ ] 会话数据提取

### Phase 3: 账号管理 (1周)
- [ ] 账号CRUD操作
- [ ] 配置管理区域账号统计显示
- [ ] 状态监控和刷新功能
- [ ] 批量操作功能（删除、代理设置、启用/禁用）
- [ ] 代理配置

### Phase 4: 优化完善 (1周)
- [ ] 错误处理
- [ ] 性能优化
- [ ] 用户体验优化

## 9. 风险评估

### 9.1 技术风险
- 登录页面结构变化
- API接口调整
- 反爬虫机制升级

### 9.2 缓解措施
- 灵活的选择器配置
- 多重验证机制
- 人工介入备选方案
