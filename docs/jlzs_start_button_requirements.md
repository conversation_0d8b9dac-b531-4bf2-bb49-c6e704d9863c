# 巨量指数开始按钮基础功能需求文档

## 1. 功能概述

巨量指数模块的开始按钮需要在用户点击时进行全面的配置验证，确保所有必要的参数都已正确设置，并且符合特定的业务规则。

## 2. 验证项目清单

### 2.1 基础必填项验证

#### 2.1.1 关键词列表验证
- **验证规则**: 关键词列表不能为空
- **错误提示**: "❌ 请先添加关键词"
- **实现位置**: `JlzsLogic.startCollection()`
- **当前状态**: 需要实现

#### 2.1.2 时间范围验证
- **验证规则**: 
  - 开始日期和结束日期都必须选择
  - 开始日期不能大于结束日期
- **错误提示**: 
  - "❌ 请选择时间范围"
  - "❌ 开始日期不能大于结束日期"
- **实现位置**: `JlzsLogic.startCollection()`
- **当前状态**: 需要实现

#### 2.1.3 地区选择验证
- **验证规则**: 至少选择一个地区（省份或城市）
- **错误提示**: "❌ 请选择地区"
- **实现位置**: `JlzsLogic.startCollection()`
- **当前状态**: 需要实现

#### 2.1.4 采集平台验证
- **验证规则**: 必须选择采集平台（抖音/头条）
- **错误提示**: "❌ 请选择采集平台"
- **实现位置**: `JlzsLogic.startCollection()`
- **当前状态**: 已有默认值，需要验证

#### 2.1.5 地区类型验证
- **验证规则**: 必须选择地区类型（分别查询/合并查询）
- **错误提示**: "❌ 请选择地区类型"
- **实现位置**: `JlzsLogic.startCollection()`
- **当前状态**: 已有默认值，需要验证

### 2.2 特殊业务规则验证

#### 2.2.1 时间范围限制规则
- **规则1**: 勾选全国时，采集时间范围开始日期只能从2019-01-01开始
  - **验证逻辑**: 检查选中地区是否包含"全国"，如果包含则验证开始日期 >= 2019-01-01
  - **错误提示**: "❌ 选择全国地区时，开始日期不能早于2019年1月1日"
  
- **规则2**: 勾选省份或城市时，开始日期只能从2022-06-04开始
  - **验证逻辑**: 检查选中地区是否只包含省份或城市（不包含全国），如果是则验证开始日期 >= 2022-06-04
  - **错误提示**: "❌ 选择省份或城市时，开始日期不能早于2022年6月4日"

#### 2.2.2 地区选择冲突规则
- **规则**: 地区类型选择"合并查询"时，如果勾选全国则不能勾选其他地区
  - **验证逻辑**: 
    - 检查地区类型是否为"merged"（合并查询）
    - 如果是，检查是否同时选择了"全国"和其他地区
  - **错误提示**: "❌ 合并查询模式下，选择全国后不能再选择其他地区"

## 3. 实现技术要求

### 3.1 验证方法结构
```dart
bool _validateStartConditions() {
  // 基础必填项验证
  if (!_validateBasicRequirements()) {
    return false;
  }
  
  // 特殊业务规则验证
  if (!_validateBusinessRules()) {
    return false;
  }
  
  return true;
}
```

### 3.2 地区判断逻辑
- **全国判断**: 检查 `selectedRegions` 是否包含"全国"
- **省份城市判断**: 检查 `selectedRegions` 是否包含除"全国"外的其他地区
- **地区类型判断**: 检查 `config.value.regionType` 的值

### 3.3 日期比较逻辑
- 使用 `DateTime.parse()` 解析日期字符串
- 使用 `DateTime.isBefore()` 和 `DateTime.isAfter()` 进行日期比较
- 特殊日期常量:
  - 全国最早日期: `DateTime(2019, 1, 1)`
  - 省市最早日期: `DateTime(2022, 6, 4)`

## 4. 错误处理机制

### 4.1 错误显示方式
- 使用 `addLog()` 方法在运行日志中显示错误信息
- 使用 `Get.snackbar()` 在顶部显示错误提示
- 错误信息格式: "❌ [具体错误描述]"

### 4.2 验证失败处理
- 验证失败时，不执行采集逻辑
- 保持开始按钮为"开始"状态
- 不改变按钮的启用/禁用状态

## 5. 用户体验优化

### 5.1 验证顺序
1. 先验证基础必填项
2. 再验证特殊业务规则
3. 按重要性顺序显示错误信息

### 5.2 友好提示
- 错误信息要明确指出问题所在
- 提供解决方案的提示
- 避免技术术语，使用用户易懂的语言

## 6. 测试用例

### 6.1 基础验证测试
- [ ] 空关键词列表测试
- [ ] 空时间范围测试
- [ ] 空地区选择测试
- [ ] 开始日期大于结束日期测试

### 6.2 特殊规则测试
- [ ] 全国+2019年前日期测试
- [ ] 省市+2022年6月4日前日期测试
- [ ] 合并查询+全国+其他地区测试

### 6.3 正常流程测试
- [ ] 全国+有效日期测试
- [ ] 省市+有效日期测试
- [ ] 分别查询+混合地区测试

## 7. 实现优先级

1. **高优先级**: 基础必填项验证
2. **高优先级**: 时间范围限制规则
3. **中优先级**: 地区选择冲突规则
4. **低优先级**: 用户体验优化

## 8. 相关文件

- **主要逻辑**: `lib/pages/jlzs/jlzs_logic.dart`
- **配置模型**: `lib/model/jlzs_config_model.dart`
- **UI组件**: `lib/pages/jlzs/widgets/collection_control_widget.dart`
- **地区组件**: `lib/pages/jlzs/widgets/jlzs_region_tree_widget.dart`

## 9. 验证结果数据观察

### 9.1 最终验证结果JSON格式
验证通过后，系统将输出以下格式的结果供观察：

```json
{
  "keyword": ["关键词1", "关键词2", "关键词3"],
  "regions": ["全国", "北京", "上海"],
  "start_date": "20190101",
  "end_date": "20240101",
  "app_name": "aweme",
  "regions_type": "separate"
}
```

### 9.2 数据字段说明
- **keyword**: 关键词数组
- **regions**: 地区数组
- **start_date**: 开始时间（格式：YYYYMMDD）
- **end_date**: 结束时间（格式：YYYYMMDD）
- **app_name**: 采集平台（"aweme"=抖音, "toutiao"=头条，显示时转换为中文）
- **regions_type**: 地区类型（"separate"=分别查询, "merged"=合并查询）
```

## 10. 数据请求任务生成规则

### 10.1 任务生成基本规则
- **关键词分组**: 每次请求最多5个关键词
- **并发控制**: 并发数 = 可用账号数
- **Cookie获取**: 从已登录账号中获取msToken等认证信息
- **代理IP支持**: 每个账号使用独立的代理IP进行请求

### 10.2 地区分开查询模式 (separate)
**规则**: 每个地区单独请求所有关键词组
```
示例数据:
- 关键词: [中国, 美国, 英国, 奥利给, 加拿大, 美国人, 中国人, 飘窗]
- 地区: [全国, 北京, 上海, 安徽, 合肥, 亳州]

生成任务:
任务1: 全国 + [中国, 美国, 英国, 奥利给, 加拿大]
任务2: 全国 + [美国人, 中国人, 飘窗]
任务3: 北京 + [中国, 美国, 英国, 奥利给, 加拿大]
任务4: 北京 + [美国人, 中国人, 飘窗]
...依此类推
```

### 10.3 地区合并查询模式 (merged)
**规则**: 所有地区合并请求所有关键词组

**特殊处理规则**:
1. 选择全国时不能选择其他地区
2. **仅在合并查询模式下**: 选择省份下所有城市时，只保留省份名称

```
地区优化示例(仅合并查询模式):
原始: [银川, 吴忠, 固原, 石嘴山, 中卫]
优化后: [宁夏]
```

**生成任务**:
```
任务1: [全国] + [中国, 美国, 英国, 奥利给, 加拿大]
任务2: [全国] + [美国人, 中国人, 飘窗]
任务3: [北京, 上海, 安徽, 合肥, 亳州] + [中国, 美国, 英国, 奥利给, 加拿大]
任务4: [北京, 上海, 安徽, 合肥, 亳州] + [美国人, 中国人, 飘窗]
```

## 11. API请求规范

### 11.1 请求接口
```
URL: https://trendinsight.oceanengine.com/api/v2/index/get_multi_keyword_hot_trend
参数: msToken, X-Bogus, _signature
方法: POST
```

### 11.2 请求体格式
```json
{
    "keyword_list": ["关键词1", "关键词2", "关键词3", "关键词4", "关键词5"],
    "start_date": "20250101",
    "end_date": "20250110",
    "app_name": "aweme",
    "region": ["地区1", "地区2"]
}
```

### 11.3 响应数据解密
- **加密方式**: AES-CBC
- **Key**: "SjXbYTJb7zXoUToSicUL3A=="
- **IV**: "OekMLjghRg8vlX/PemLc+Q=="

## 12. 数据存储设计

### 12.1 分开查询模式数据结构 (适配巨量指数)
```dart
class JlzsDataModel {
  String? region;           // 地区名称
  String platform;         // 平台 (aweme/toutiao)
  bool isCompleted;        // 是否完成采集
  Map<String, List<JlzsKeywordData>>? data; // 以日期为键，关键词数据为值

  JlzsDataModel({this.region, required this.platform, this.isCompleted = false, this.data});
}

class JlzsKeywordData {
  String keyword;          // 关键词
  String hotIndex;         // 综合指数
  String searchIndex;      // 搜索指数

  JlzsKeywordData({required this.keyword, required this.hotIndex, required this.searchIndex});
}

// 分开查询使用
List<JlzsDataModel> separateData = [];
```

### 12.2 合并查询模式数据结构
```dart
class JlzsMergedData {
  String keyword;          // 关键词
  String date;            // 日期 (YYYYMMDD)
  String platform;        // 平台 (aweme/toutiao)
  List<String> regions;   // 地区列表
  String hotIndex;        // 综合指数
  String searchIndex;     // 搜索指数

  JlzsMergedData({
    required this.keyword,
    required this.date,
    required this.platform,
    required this.regions,
    required this.hotIndex,
    required this.searchIndex
  });
}

// 合并查询使用
List<JlzsMergedData> mergedData = [];
```

## 12.3 并发代理IP使用机制

### 12.3.1 代理IP分配原则
- **一账号一代理**: 每个账号绑定独立的代理IP
- **代理隔离**: 不同账号的代理IP完全隔离，避免冲突
- **状态同步**: 账号可用 = 代理可用，账号不可用时代理也不使用

### 12.3.2 并发执行中的代理使用流程

**第1轮并发（3个任务同时执行）**:
```
账号A + 代理IP_A → 执行任务1 → API请求通过代理IP_A
账号B + 代理IP_B → 执行任务2 → API请求通过代理IP_B
账号C + 代理IP_C → 执行任务3 → API请求通过代理IP_C
```

**第2轮并发（等第1轮完成后）**:
```
账号A + 代理IP_A → 执行任务4 → API请求通过代理IP_A
账号B + 代理IP_B → 执行任务5 → API请求通过代理IP_B
账号C + 代理IP_C → 执行任务6 → API请求通过代理IP_C
```

### 12.3.3 代理请求实现示例
```dart
// 每个任务执行时的代理使用
Future<http.Response> executeTaskWithProxy(JlzsAccountModel account, TaskData task) {
  // 1. 从账号配置中获取代理信息（已验证可用）
  JlzsProxyConfig proxy = account.proxyConfig;

  // 2. 构建带代理的HTTP客户端
  HttpClient client = HttpClient();
  client.findProxy = (uri) => "PROXY ${proxy.host}:${proxy.port}";

  // 3. 发起API请求
  return client.postUrl(Uri.parse(apiUrl))
    .then((request) => request.close());
}
```

### 12.3.4 实际执行示例

**假设**：
- 任务总数：12个（分开查询模式）
- 可用账号：账号A（代理IP_A）、账号B（代理IP_B）、账号C（代理IP_C）
- 并发数：3

**执行过程（假设提取间隔30秒）**：
```
时间 0秒:     第1轮开始 - 账号A(代理IP_A)执行任务1, 账号B(代理IP_B)执行任务2, 账号C(代理IP_C)执行任务3
时间 5-10秒:  第1轮完成 - 等待提取间隔
时间 30秒:    第2轮开始 - 账号A(代理IP_A)执行任务4, 账号B(代理IP_B)执行任务5, 账号C(代理IP_C)执行任务6
时间 35-40秒: 第2轮完成 - 等待提取间隔
时间 60秒:    第3轮开始 - 账号A(代理IP_A)执行任务7, 账号B(代理IP_B)执行任务8, 账号C(代理IP_C)执行任务9
时间 65-70秒: 第3轮完成 - 等待提取间隔
时间 90秒:    第4轮开始 - 账号A(代理IP_A)执行任务10,账号B(代理IP_B)执行任务11,账号C(代理IP_C)执行任务12
时间 95-100秒:第4轮完成 - 所有任务结束
```

### 12.3.5 提取间隔控制
- **间隔设置**: 用户在UI中配置提取间隔（默认30秒）
- **轮次等待**: 每轮并发任务完成后，等待指定的提取间隔时间
- **统一控制**: 所有账号同步等待，避免请求过于频繁
- **实现方式**: 使用`await Future.delayed(Duration(seconds: extractInterval))`

### 12.3.6 关键优势
- **IP分散**: 3个不同的代理IP同时请求，降低被限流风险
- **负载均衡**: 每个代理IP承担相同的请求压力
- **故障隔离**: 单个代理IP出问题不影响其他账号继续执行
- **频率控制**: 通过提取间隔避免请求过于频繁被限流


## 13. 代理IP管理机制

### 13.1 账号管理阶段代理配置
- **API代理获取**: 通过API自动获取代理IP，不再手动配置
- **自动验证**: 获取代理后立即验证可用性
- **过期监控**: 监控代理有效期，临近过期自动续期
- **状态维护**: 确保账号的代理始终处于可用状态

### 13.2 任务执行阶段代理使用
- **直接取用**: 任务执行时直接使用账号关联的代理IP
- **无需验证**: 因为账号管理阶段已确保代理可用
- **请求隔离**: 每个账号使用独立的代理IP进行API请求
- **错误处理**: 如果代理请求失败，记录错误并可选择重试

### 13.3 代理API集成要点
```dart
class JlzsProxyApiManager {
  // 获取新代理
  Future<JlzsProxyConfig> getNewProxy();

  // 续期代理
  Future<JlzsProxyConfig> renewProxy(String proxyId);

  // 验证代理
  Future<bool> validateProxy(JlzsProxyConfig proxy);
}
```

### 13.4 账号代理状态管理
- **配置时**: API获取 → 验证 → 保存 → 启动监控
- **使用时**: 直接从账号配置中获取代理信息
- **维护时**: 后台自动检查过期并通过API续期

## 14. 参考实现

可参考百度指数模块的验证逻辑：
- 文件: `lib/pages/baidu_index/baidu_index_logic.dart`
- 方法: `_checkCanStart()`
- 行数: 754-870
